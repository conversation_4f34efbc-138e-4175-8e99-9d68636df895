
## BIM Website
Bim website 

### Installation
1. Install Composer packages;
``` composer require ```

2. Install NPM packages
``` npm install ```

3. Install cross-env globally
``` npm install -g cross-env ```

4. Start developing with blazing fast auto-refresh server
``` npm run dev:frontend ``` or ``` npm run dev:frontend-rtl ``` for RTL

5. Build for production
``` npm run build:frontend && npm run build:frontend-rtl ```


### Stack used
- **[<PERSON><PERSON>](https://laravel.com/)**
- **[Vuejs](https://vuejs.org/)**
- **[Tailwind css](https://tailwindcss.com/)**
