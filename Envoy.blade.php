@servers(['production' => ['deployer@**************']])


@task('deploy', ['on' => 'production'])
    source ~/.nvm/nvm.sh
    nvm use stable
    cd /var/www/bim
    php artisan down
    git checkout .
    git pull
    composer install --no-interaction --no-plugins --no-progress --no-scripts --optimize-autoloader
    php artisan migrate --force
    php artisan cache:clear
    php artisan route:clear
    php artisan view:clear
    php artisan config:clear
    php artisan view:cache
    php artisan config:cache
    php artisan route:cache
    php artisan optimize
    php artisan queue:restart
    php artisan up
    npm install
    npm run build
@endtask