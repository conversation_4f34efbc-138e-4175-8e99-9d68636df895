{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.0.2", "based/momentum-modal": "^0.1.8", "diglactic/laravel-breadcrumbs": "^8.0", "doctrine/dbal": "^3.5", "guzzlehttp/guzzle": "^7.2", "inertiajs/inertia-laravel": "^0.6.3", "laravel/framework": "^9.19", "laravel/sanctum": "^3.0", "laravel/scout": "^9.4", "laravel/tinker": "^2.7", "livewire/livewire": "^2.10", "spatie/laravel-activitylog": "^4.5", "spatie/laravel-honeypot": "^4.3", "spatie/laravel-medialibrary": "^10.4", "spatie/laravel-menu": "^4.1", "spatie/laravel-model-states": "^2.4", "spatie/laravel-permission": "^5.5", "spatie/laravel-sitemap": "^6.2", "spatie/laravel-translatable": "^6.0", "spatie/laravel-view-models": "^1.5", "tightenco/ziggy": "^1.4"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/breeze": "^1.12", "laravel/envoy": "^2.8", "laravel/pint": "^1.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^6.1", "phpstan/phpstan": "^1.8", "phpunit/phpunit": "^9.5.10", "spatie/laravel-ignition": "^1.0", "spatie/laravel-ray": "^1.30"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helpers/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true}}, "minimum-stability": "dev", "prefer-stable": true}