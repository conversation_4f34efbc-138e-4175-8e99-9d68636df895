/* Project Map Styles */
.project-map-container {
    position: relative;
    overflow: hidden;
    border-radius: 0.75rem;
    /* Hardware acceleration for smoother rendering */
    transform: translateZ(0);
    will-change: transform;
    backface-visibility: hidden;
}

.project-map {
    height: 600px;
    width: 100%;
    border-radius: 0.75rem;
    z-index: 1;
    overflow: hidden !important; /* Ensure content doesn't overflow rounded corners */
    /* Enable hardware acceleration for smoother map operations */
    transform: translateZ(0);
    will-change: transform;
    backface-visibility: hidden;
}

/* Add nice styling to the popups */
.marker-popup h4 {
    font-weight: 600;
    font-size: 1rem;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.marker-popup p {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
    color: #4b5563;
}

.marker-popup p.address {
    font-style: italic;
    color: #6b7280;
    font-size: 0.75rem;
}

/* Ensure Leaflet markers display properly */
.leaflet-marker-icon {
    z-index: 10 !important;
    /* Optimize marker rendering */
    will-change: transform;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Kurdistan city boundary popup styles */
.city-boundary-popup {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    min-width: 200px;
    max-width: 280px;
}

.city-boundary-popup h4 {
    margin: 0 0 8px 0 !important;
    color: #2c3e50 !important;
    font-size: 14px !important;
    font-weight: bold !important;
    border-bottom: 1px solid #ecf0f1;
    padding-bottom: 4px;
}

.city-boundary-popup > div {
    font-size: 12px !important;
    color: #7f8c8d !important;
    line-height: 1.4 !important;
}

.city-boundary-popup > div > div {
    margin-bottom: 2px;
}

.city-boundary-popup > div > div:last-child {
    margin-top: 6px !important;
    font-style: italic !important;
    color: #95a5a6 !important;
    font-size: 11px !important;
}

/* Custom markers styling */
.custom-marker-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    transform: translate3d(0, 0, 0);
    will-change: transform;
}

.custom-marker-icon.bim {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.custom-marker-icon.atm {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

/* Improve marker rendering during zoom */
.leaflet-zoom-animated .leaflet-marker-icon {
    transform: translate3d(0, 0, 0) !important;
    transition: none !important;
}

/* Make sure popups are above markers */
.leaflet-popup {
    z-index: 11 !important;
}

/* Make sure controls are visible */
.leaflet-control-container {
    z-index: 12 !important;
}

.project-map-tall {
    height: 500px;
}

.project-map-short {
    height: 300px;
}

/* City Navigation */
.city-navigation {
    padding: 1rem 0;
}

.city-btn {
    font-size: 0.875rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
}

.city-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.city-btn.active {
    background-color: #008CD3 !important;
    color: white !important;
}

/* Map Popup Styles */
.project-map-popup {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    max-width: 250px;
}

.popup-title {
    color: #1f2937;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.popup-description {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.popup-address {
    color: #9ca3af;
    font-size: 0.8125rem;
    margin-bottom: 0;
    line-height: 1.4;
}

.popup-address strong {
    color: #4b5563;
}

/* Map Loading State */
.map-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;
    background-color: #f9fafb;
    border-radius: 0.75rem;
    color: #6b7280;
    font-size: 0.875rem;
}

.map-loading-spinner {
    width: 2rem;
    height: 2rem;
    border: 3px solid #e5e7eb;
    border-top: 3px solid #008CD3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.75rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Map Controls */
.leaflet-control-container .leaflet-control-zoom {
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.leaflet-control-zoom a {
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.leaflet-control-zoom a:hover {
    background-color: #f3f4f6;
}

/* Map Attribution */
.leaflet-control-attribution {
    background-color: rgba(255, 255, 255, 0.9) !important;
    font-size: 0.75rem !important;
    border-radius: 0.25rem;
}

/* City Info Panel */
.city-info-panel {
    animation: fadeInUp 0.3s ease-out;
}

.city-info-panel h4 {
    font-weight: 600;
    color: #1e40af;
    margin-bottom: 0.5rem;
}

.city-info-panel p {
    color: #1e3a8a;
    line-height: 1.5;
}

/* Project Map Section */
.project-map-section {
    transition: box-shadow 0.3s ease;
}


/* Multi-City Map Container */
.multi-city-map-container {
    position: relative;
}

.multi-city-map {
    height: 400px;
    width: 100%;
    border-radius: 0.75rem;
}

/* Bounds Overlay Styles */
.leaflet-interactive {
    cursor: pointer;
}

.leaflet-popup-content {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
}

/* City Bounds Rectangle */
.leaflet-overlay-pane svg path[stroke="#008CD3"] {
    stroke-dasharray: 5, 5;
    animation: dash 20s linear infinite;
}

@keyframes dash {
    to {
        stroke-dashoffset: -10;
    }
}

/* City Info Panel Animation */
.city-info-panel {
    transition: all 0.3s ease-in-out;
}

/* Responsive Design */
@media (max-width: 768px) {
    .project-map {
        height: 300px;
    }
    
    .project-map-tall {
        height: 350px;
    }
    
    .city-navigation {
        padding: 0.75rem 0;
    }
    
    .city-btn {
        font-size: 0.8125rem;
        padding: 0.5rem 0.75rem;
    }
    
    .popup-title {
        font-size: 1rem;
    }
    
    .popup-description {
        font-size: 0.8125rem;
    }
    
    .popup-address {
        font-size: 0.75rem;
    }
    
    .city-navigation .flex {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .city-btn {
        width: 100%;
        text-align: center;
    }
    
    .project-map,
    .multi-city-map {
        height: 300px;
    }
    
    .city-info-panel {
        padding: 1rem;
    }
}

@media (max-width: 480px) {
    .project-map,
    .multi-city-map {
        height: 250px;
    }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
    .map-loading {
        background-color: #1f2937;
        color: #9ca3af;
    }
    
    .city-btn {
        background-color: #374151;
        color: #d1d5db;
    }
    
    .city-btn:hover {
        background-color: #008CD3;
        color: white;
    }
    
    .leaflet-control-attribution {
        background-color: rgba(31, 41, 55, 0.9) !important;
        color: #d1d5db !important;
    }
}

/* Map Section Styling */
.project-maps-section {
    margin-top: 3rem;
}

.project-maps-section h2 {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1.5rem;
}

.map-item {
    margin-bottom: 2.5rem;
    padding: 1.5rem;
    background-color: white;
    border-radius: 1rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.map-item h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.map-item p {
    color: #6b7280;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.map-type-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    background-color: #dbeafe;
    color: #1e40af;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
    margin-bottom: 1rem;
}

.map-type-badge.multi-city {
    background-color: #dcfce7;
    color: #166534;
}

/* Animation for map loading */
.map-fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
