import ImageUploader from "./editorImageUploader";
import walkDom from "./utils/walkDom";

window.addEventListener("load", initEditor);

function initEditor() {
    ContentTools.IMAGE_UPLOADER = ImageUploader.createImageUploader;

    ContentTools.StylePalette.add([
        new ContentTools.Style("parallax", "parallax", ["img"]),
        new ContentTools.Style("wide", "wide", ["img"]),
    ]);

    const editor = ContentTools.EditorApp.get();

    editor.init("*[data-editable]", "data-name");

    //Register events
    editor.addEventListener("ready", () => {
        console.log("Readyyy");
    });
    editor.addEventListener("start", function (ev) {
        // Call save every 30 seconds
        function autoSave() {
            this.save(true);
        }
        this.autoSaveTimer = setInterval(autoSave.bind(this), 30 * 1000);
    });

    editor.addEventListener("stop", function (ev) {
        clearInterval(this.autoSaveTimer);
    });

    editor.addEventListener("saved", handleSave);


    function handleSave(ev) {
        var name, payload, regions, xhr;
        const endpoint = document
            .querySelector("[data-editor-endpoint]")
            .getAttribute("data-editor-endpoint");
      
            // Check that something changed
            regions = ev.detail().regions;
            console.log(regions)
        if (Object.keys(regions).length == 0) {
            return;
        }

        // Set the editor as busy while we save our changes
        this.busy(true);

        // Collect the contents of each region into a FormData instance
        payload = new FormData();
        for (name in regions) {
            if (regions.hasOwnProperty(name)) {
                payload.append(name, regions[name]);
            }
        }
        let _this = this;
        // Send the update content to the server to be saved
        function onStateChange(ev) {
            // Check if the request is finished
            if (ev.target.readyState == 4) {
                _this.busy(false);
                if (ev.target.status == "200") {
                    // Save was successful, notify the user with a flash
                    new ContentTools.FlashUI("ok");
                } else {
                    // Save failed, notify the user with a flash
                    new ContentTools.FlashUI("no");
                }
            }
        }

        setTimeout(function () {
            xhr = new XMLHttpRequest();
            xhr.addEventListener("readystatechange", onStateChange);
            xhr.open("POST", endpoint);
            xhr.setRequestHeader(
                "X-CSRF-TOKEN",
                document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content")
            );
            xhr.send(payload);
        }, 200);
    }
}
