import {animate, inView, stagger} from "motion";
import animationNames from "./animations/animationNames";

const animationTargets = document.querySelectorAll('[data-animation]');
animationTargets.forEach((target) => {
    const animationName = target.dataset.animation || "fade";
    const durationInMilliseconds = target.dataset.duration || 500;
    const durationInSeconds = durationInMilliseconds / 1000;
    const delayInMilliseconds = target.dataset.delay || 0;
    const delayInSeconds = delayInMilliseconds / 1000;
    const easing = target.dataset.easing || "ease-in-out";

    let keyframe = animationNames[animationName];
    if (animationName !== "fade") {
        keyframe = {
            ...keyframe,
            ...animationNames.fade,
        }
    }
    const options = {
        duration: durationInSeconds,
        fill: 'forwards',
        easing: easing,
    };
    if (delayInSeconds > 0) {
        options.delay = delayInSeconds;
    }
    inView(target, () => {
        animate(target, keyframe, options);
    });
});


const staggeredTargets = document.querySelectorAll('[data-animation-stagger]');

staggeredTargets.forEach((target) => {
    const animationName = target.dataset.animationStagger || "fade";
    const durationInMilliseconds = target.dataset.duration || 500;
    const durationInSeconds = durationInMilliseconds / 1000;
    const staggerInMilliseconds = target.dataset.staggerDuration || 100;
    const staggerInSeconds = staggerInMilliseconds / 1000;
    let keyframe = animationNames[animationName];
    const staggerTarget = target.dataset.staggerTarget;
    const delayInMilliseconds = target.dataset.delay || 0;
    const delayInSeconds = delayInMilliseconds / 1000;
    const easing = target.dataset.easing || "ease-in-out";

    if (animationName !== "fade") {
        keyframe = {
            ...keyframe,
            ...animationNames.fade,
        }
    }
    const options = {
        fill: 'forwards',
        easing: easing,
        duration: durationInSeconds,
    };

    inView(target, () => {
        animate(target.querySelectorAll(`& > ${staggerTarget}`), keyframe, {
            ...options,
            delay: stagger(staggerInSeconds, {start: delayInSeconds}),
        });
    });
});