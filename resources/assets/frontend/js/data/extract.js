#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Import the Kurdistan cities data
const { KURDISTAN_CITIES } = require('./kurdistan-cities.js');

// Create data directory if it doesn't exist
const dataDir = path.join(__dirname, 'data');
if (!fs.existsSync(dataDir)) {
    fs.mkdirSync(dataDir, { recursive: true });
}

// Function to convert coordinates array to lat/lng objects
function convertCoordinates(coordinates) {
    // Handle the nested array structure: coordinates[0] is the outer polygon
    const polygon = coordinates[0];
    
    return polygon.map(coord => ({
        lat: coord[1], // latitude is second element
        lng: coord[0]  // longitude is first element
    }));
}

// Extract each city's coordinates
Object.keys(KURDISTAN_CITIES).forEach(cityKey => {
    const cityData = KURDISTAN_CITIES[cityKey];
    const cityName = cityData.name;
    
    console.log(`Processing ${cityName} (${cityKey})...`);
    
    // Convert coordinates to the desired format
    const formattedCoordinates = [convertCoordinates(cityData.coordinates)];
    
    // Create filename based on city key
    const filename = `${cityKey}.json`;
    const filepath = path.join(dataDir, filename);
    
    // Write to JSON file
    try {
        fs.writeFileSync(filepath, JSON.stringify(formattedCoordinates, null, 2));
        console.log(`✓ Created ${filename} with ${formattedCoordinates[0].length} coordinates`);
    } catch (error) {
        console.error(`✗ Failed to create ${filename}:`, error.message);
    }
});

console.log('\n🎉 All city coordinate files have been generated in the data/ folder!');
console.log('\nGenerated files:');
fs.readdirSync(dataDir)
    .filter(file => file.endsWith('.json'))
    .forEach(file => {
        const stats = fs.statSync(path.join(dataDir, file));
        console.log(`  - ${file} (${(stats.size / 1024).toFixed(2)} KB)`);
    });