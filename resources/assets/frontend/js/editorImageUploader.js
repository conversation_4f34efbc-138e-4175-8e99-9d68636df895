const ImageUploader = (function () {
    var image, xhr, xhrComplete, xhrProgress;

    function ImageUploader(dialog) {
        this._dialog = dialog;
        this._dialog.addEventListener(
            "cancel",
            (function (_this) {
                return function () {
                    return _this._onCancel();
                };
            })(this)
        );
        this._dialog.addEventListener(
            "imageuploader.cancelupload",
            (function (_this) {
                return function () {
                    return _this._onCancelUpload();
                };
            })(this)
        );
        this._dialog.addEventListener(
            "imageuploader.clear",
            (function (_this) {
                return function () {
                    return _this._onClear();
                };
            })(this)
        );
        this._dialog.addEventListener(
            "imageuploader.fileready",
            (function (_this) {
                return function (ev) {
                    return _this._onFileReady(ev.detail().file);
                };
            })(this)
        );
        this._dialog.addEventListener(
            "imageuploader.mount",
            (function (_this) {
                return function () {
                    return _this._onMount();
                };
            })(this)
        );
        this._dialog.addEventListener(
            "imageuploader.rotateccw",
            (function (_this) {
                return function () {
                    return _this._onRotateCCW();
                };
            })(this)
        );
        this._dialog.addEventListener(
            "imageuploader.rotatecw",
            (function (_this) {
                return function () {
                    return _this._onRotateCW();
                };
            })(this)
        );
        this._dialog.addEventListener(
            "imageuploader.save",
            (function (_this) {
                return function () {
                    return _this._onSave();
                };
            })(this)
        );
        this._dialog.addEventListener(
            "imageuploader.unmount",
            (function (_this) {
                return function () {
                    return _this._onUnmount();
                };
            })(this)
        );
    }

    ImageUploader.prototype._onCancel = function () {
        console.log("on cancel");
    };

    ImageUploader.prototype._onCancelUpload = cancelUpload;

    function cancelUpload() {
        if (xhr) {
            xhr.upload.removeEventListener("progress", xhrProgress);
            xhr.removeEventListener("readystatechange", xhrComplete);
            xhr.abort();
        }
        return this._dialog.state("empty");
    }

    ImageUploader.prototype._onClear = function () {
        image = null;
        return this._dialog.clear();
    };

    ImageUploader.prototype._onFileReady = function (file) {
        var formData;
        // Define functions to handle upload progress and completion
        xhrProgress = function (ev) {
            // Set the progress for the upload
            this._dialog.progress((ev.loaded / ev.total) * 100);
        }.bind(this);

        xhrComplete = function (ev) {
            var response;

            // Check the request is complete
            if (ev.target.readyState != 4) {
                return;
            }

            // Clear the request
            xhr = null;
            xhrProgress = null;
            xhrComplete = null;

            // Handle the result of the upload
            if (parseInt(ev.target.status) == 200) {
                // Unpack the response (from JSON)
                response = JSON.parse(ev.target.responseText);

                if (response.error) {
                    cancelUpload.call(this);
                    alert(response.message);
                    return;
                }
                // Store the image details
                image = {
                    url: response.url,
                    dimensions: response.dimensions
                };

                // Populate the dialog
                this._dialog.populate(image.url);
            } else {
                // The request failed, notify the user
                new ContentTools.FlashUI("no");
            }
        }.bind(this);

        // Set the dialog state to uploading and reset the progress bar to 0
        this._dialog.state("uploading");
        this._dialog.progress(0);

        // Build the form data to post to the server
        formData = new FormData();
        formData.append("image", file);

        // Make the request
        xhr = new XMLHttpRequest();
        xhr.upload.addEventListener("progress", xhrProgress);
        xhr.addEventListener("readystatechange", xhrComplete);
        xhr.open("POST", "/en/stories/upload-image", true);
        xhr.setRequestHeader(
            "X-CSRF-TOKEN",
            document
                .querySelector('meta[name="csrf-token"]')
                .getAttribute("content")
        );
        xhr.send(formData);
    };

    ImageUploader.prototype._onMount = function () {};

    ImageUploader.prototype._onSave = function () {
        this._dialog.busy(true);
        this._dialog.busy(false);
        this._dialog.save(image.url, image.dimensions);
    };

    ImageUploader.prototype._onUnmount = function () {};

    ImageUploader.createImageUploader = function (dialog) {
        return new ImageUploader(dialog);
    };

    return ImageUploader;
})();

export default ImageUploader;
