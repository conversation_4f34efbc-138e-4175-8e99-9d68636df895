import 'preline'
import Alpine from "alpinejs";
import collapse from "@alpinejs/collapse";
import focus from "@alpinejs/focus";
import "./components";
import "./animation";

import.meta.glob(["../images/**", "../fonts/**"]);

Alpine.data("menu", function () {
    return {
        open: true,
        toggleMenu() {
            this.open = !this.open;
        },
    };
});

Alpine.data("searchModal", function () {
    return {
        searchOpen: false,
        closeSearchModal() {
            this.searchOpen = false;
        },
        openSearchModal() {
            this.searchOpen = true;
            this.$refs.searchInput.focus();
        },
    };
});

Alpine.plugin(focus);
Alpine.plugin(collapse);

window.Alpine = Alpine;
Alpine.start();
