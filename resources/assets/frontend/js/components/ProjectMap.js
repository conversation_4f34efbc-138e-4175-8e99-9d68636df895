import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { getKurdistanRegionCoordinates } from '../data/kurdistan-region-boundary.js';

// Fix for default markers in Leaflet with webpack
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
    iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
    iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
    shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

class ProjectMap {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.options = {
            zoom: 10,
            center: [36.1915, 44.0092], // Default to Erbil, Iraq
            ...options
        };
        this.map = null;
        this.markers = [];
        this.markerClusterGroup = null;
        this.boundsOverlay = null;
        this.kurdistanRegionBoundary = null; // Store Kurdistan Region boundary polygon
        
        // Determine if this is a multi-city map
        this.isMultiCityMap = this.containerId.includes('multi-city-map') || options.isMultiCity === true;
    }

    init() {
        const container = document.getElementById(this.containerId);
        if (!container) {
            return;
        }

        // Initialize the map
        try {
            this.map = L.map(this.containerId, {
                center: this.options.center,
                zoom: this.options.zoom,
                zoomControl: true,
                scrollWheelZoom: true,
                doubleClickZoom: true,
                preferCanvas: true, // Use canvas renderer for better performance
                // Add additional options for better performance
                fadeAnimation: false, // Disable fade animations for better performance
                markerZoomAnimation: false, // Disable marker zoom animations completely
                zoomAnimation: true, // Keep zoom animation for better UX
                trackResize: true,
                renderer: L.canvas({ padding: 0.5 }) // Use canvas renderer with padding to prevent edge clipping
            });

            // Add OpenStreetMap tiles
            L.tileLayer('https://{s}.basemaps.cartocdn.com/light_nolabels/{z}/{x}/{y}{r}.png', {
                attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
                maxZoom: 19,
            }).addTo(this.map);
            
            // Reset markers array 
            this.markers = [];
            
            // Add throttling and debouncing for map events to prevent UI freezing
            let moveTimeout;
            this.map.on('movestart', () => {
                clearTimeout(moveTimeout);
                // Hide markers during map movement to prevent blinking
                this._updateMarkersVisibility(false);
            });
            
            this.map.on('move', this._throttle(() => {
                // Throttled move event handling
            }, 100));
            
            this.map.on('moveend', () => {
                clearTimeout(moveTimeout);
                moveTimeout = setTimeout(() => {
                    // Show markers after movement has ended
                    this._updateMarkersVisibility(true);
                }, 200);
            });
            
            // Similar for zoom events
            let zoomTimeout;
            this.map.on('zoomstart', () => {
                clearTimeout(zoomTimeout);
                // Hide markers during zoom to prevent blinking
                this._updateMarkersVisibility(false);
            });
            
            this.map.on('zoom', this._throttle(() => {
                // Throttled zoom event handling
            }, 100));
            
            this.map.on('zoomend', () => {
                clearTimeout(zoomTimeout);
                zoomTimeout = setTimeout(() => {
                    // Debounced zoomend event handling
                    // Ensure minimum zoom level
                    const minZoom = this.options.zoom || 6;
                    const currentZoom = this.map.getZoom();
                    if (currentZoom < minZoom) {
                        this.map.setZoom(minZoom);
                    }
                    // Show markers after zoom has ended
                    this._updateMarkersVisibility(true);
                }, 200);
            });
            
            // Show Kurdistan Region boundary only on single-city maps, not multi-city maps
            if (!this.isMultiCityMap) {
                this.showKurdistanRegionBoundary();
            }
        } catch (error) {
            
            // Display error in the container
            const errorMapElement = document.getElementById(`error-map-${this.containerId.replace('project-map-', '')}`);
            if (errorMapElement) {
                errorMapElement.classList.remove('hidden');
            }
        }

        return this;
    }
    
    // Helper method to update marker visibility
    _updateMarkersVisibility(visible) {
        this.markers.forEach(marker => {
            const icon = marker.getElement();
            if (icon) {
                if (visible) {
                    icon.style.display = '';
                    icon.style.opacity = '1';
                } else {
                    icon.style.opacity = '0.01'; // Almost invisible but still present
                }
            }
        });
    }
    
    // Utility methods for throttling and debouncing
    _throttle(func, delay) {
        let lastCall = 0;
        return function(...args) {
            const now = new Date().getTime();
            if (now - lastCall < delay) {
                return;
            }
            lastCall = now;
            return func(...args);
        };
    }
    
    _debounce(func, delay) {
        let timeout;
        return function(...args) {
            clearTimeout(timeout);
            timeout = setTimeout(() => {
                func(...args);
            }, delay);
        };
    }    // Define custom icon options
    getCustomIcon(iconType = 'default') {
        // Icon type definitions
        const iconTypes = {
            'default': {
                iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
                shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
                iconSize: [25, 41],
                iconAnchor: [12, 41],
                popupAnchor: [1, -34],
                shadowSize: [41, 41],
                className: 'custom-marker-icon default'
            },
            'bim': {
                iconUrl: '/bim_logo.png',
                // No shadow for custom icons
                iconSize: [32, 32],
                iconAnchor: [16, 32],
                popupAnchor: [0, -32],
                className: 'custom-marker-icon bim'
            },
            'atm': {
                iconUrl: '/atm_icon.svg',
                // No shadow for custom icons
                iconSize: [32, 32],
                iconAnchor: [16, 32],
                popupAnchor: [0, -32],
                className: 'custom-marker-icon atm'
            }
        };
        
        // Use the specified icon type or fall back to default
        const iconConfig = iconTypes[iconType] || iconTypes['default'];
        
        return L.icon(iconConfig);
    }

    addMarker(lat, lng, options = {}) {
        // Validate coordinates
        lat = parseFloat(lat);
        lng = parseFloat(lng);
        
        if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
            
            // Add error to map container for debugging
            const errorElement = document.getElementById(`error-${this.containerId}`);
            if (errorElement) {
                errorElement.classList.remove('hidden');
                errorElement.textContent = `Invalid coordinates detected: [${lat}, ${lng}]`;
                setTimeout(() => {
                    errorElement.classList.add('hidden');
                }, 5000);
            }
            
            return null; // Return null to indicate failure
        }
        
        try {
            
            // Handle custom icon if provided
            if (options.iconType) {
                if (!options.icon) {
                    options.icon = this.getCustomIcon(options.iconType);
                }
            }
            
            // Create marker with optimized options for performance
            const markerOptions = {
                ...options,
                // Add properties to prevent marker blinking
                pane: 'markerPane',
                bubblingMouseEvents: false,
                interactive: true,
                riseOnHover: false, // Disable rise on hover which can cause rendering issues
                riseOffset: 0       // Disable rise offset
            };
            
            const marker = L.marker([lat, lng], markerOptions);
            
            if (options.popup) {
                marker.bindPopup(this.createPopupContent(options.popup));
            }

            marker.addTo(this.map);
            this.markers.push(marker);
            
            // Add CSS to make marker rendering more stable
            const markerElement = marker.getElement();
            if (markerElement) {
                markerElement.style.willChange = 'transform';
                markerElement.style.transformStyle = 'preserve-3d';
                markerElement.style.backfaceVisibility = 'hidden';
                markerElement.style.perspective = '1000px';
            }
            
            return marker;
        } catch (error) {
            
            // Add error to map container for debugging
            const errorElement = document.getElementById(`error-${this.containerId}`);
            if (errorElement) {
                errorElement.classList.remove('hidden');
                errorElement.textContent = `Error creating marker: ${error.message}`;
                setTimeout(() => {
                    errorElement.classList.add('hidden');
                }, 5000);
            }
            
            return null;
        }
    }

    addMarkers(markers) {
        if (!Array.isArray(markers)) {
            return;
        }
        
        let validMarkersCount = 0;
        const BATCH_SIZE = 10; // Process markers in batches to prevent UI freezing
        
        // First, hide all existing markers during the update
        this._updateMarkersVisibility(false);
        
        // Clear existing markers if we're replacing them
        this.markers.forEach(marker => {
            if (this.map) {
                this.map.removeLayer(marker);
            }
        });
        this.markers = [];
        
        // Helper function to process a batch of markers
        const processBatch = (startIndex) => {
            const endIndex = Math.min(startIndex + BATCH_SIZE, markers.length);
            
            for (let i = startIndex; i < endIndex; i++) {
                const markerData = markers[i];
                
                // Handle various coordinate formats that might be coming from the backend
                let coordinates = markerData.coordinates;
                
                // If coordinates is a string (possibly JSON), try to parse it
                if (typeof coordinates === 'string') {
                    try {
                        coordinates = JSON.parse(coordinates);
                    } catch (e) {
                        console.warn('Failed to parse coordinates string:', coordinates, e);
                    }
                }
                
                // Now normalize the coordinates to a [lat, lng] array
                let normalizedCoordinates;
                
                if (Array.isArray(coordinates)) {
                    if (coordinates.length >= 2) {
                        normalizedCoordinates = [parseFloat(coordinates[0]), parseFloat(coordinates[1])];
                    }
                } else if (coordinates && typeof coordinates === 'object') {
                    if (coordinates.lat !== undefined && coordinates.lng !== undefined) {
                        normalizedCoordinates = [parseFloat(coordinates.lat), parseFloat(coordinates.lng)];
                    } else if (coordinates.latitude !== undefined && coordinates.longitude !== undefined) {
                        normalizedCoordinates = [parseFloat(coordinates.latitude), parseFloat(coordinates.longitude)];
                    }
                }
                
                // Validate the normalized coordinates
                if (!normalizedCoordinates) {
                    continue; // Skip this marker
                }
                
                const [lat, lng] = normalizedCoordinates;
                
                // Additional validation that the coordinates are within valid ranges
                if (isNaN(lat) || isNaN(lng) || lat < -90 || lat > 90 || lng < -180 || lng > 180) {
                    continue; // Skip this marker
                }
                
                validMarkersCount++;
                
                // Extract icon type from marker data if available
                const iconType = markerData.icon_type || 'default';
                
                this.addMarker(
                    lat,
                    lng,
                    {
                        iconType: iconType, // Pass the icon type to addMarker
                        popup: {
                            title: markerData.title,
                            description: markerData.description,
                            address: markerData.address
                        }
                    }
                );
            }
            
            // If there are more markers to process, schedule the next batch
            if (endIndex < markers.length) {
                setTimeout(() => processBatch(endIndex), 0);
            } else {                
                // Show markers after all processing is complete
                setTimeout(() => {
                    this._updateMarkersVisibility(true);
                    this.fitMarkersToMap();
                }, 100);
            }
        };
        
        // Start processing the first batch
        processBatch(0);
    }
    
    // New method to fit markers to map bounds
    fitMarkersToMap() {
        // Fit map to show all markers if we have any, but maintain a minimum zoom level
        if (this.markers.length > 0) {
            try {
                // Store current zoom level to respect minimum zoom
                const currentZoom = this.map.getZoom();
                const minZoom = this.options.zoom || 6; // Use initial zoom as minimum or default to 6
                
                const group = new L.featureGroup(this.markers);
                this.map.fitBounds(group.getBounds().pad(0.1));
                
                // If the new zoom level is too far out (lower than our minimum), set it back
                const newZoom = this.map.getZoom();
                if (newZoom < minZoom) {
                    this.map.setZoom(minZoom);
                }
                
            } catch (error) {
                console.error('Error fitting map to markers:', error);
            }
        }
    }
    
    // Method to show Kurdistan Region boundary on single-city maps only
    showKurdistanRegionBoundary() {
        try {
            // Get Kurdistan Region coordinates
            const coordinates = getKurdistanRegionCoordinates();

            // Create polygon for Kurdistan Region boundary
            this.kurdistanRegionBoundary = L.polygon(coordinates, {
                color: '#e74c3c',        // Red border
                weight: 2,               // Border width
                opacity: 0.8,            // Border opacity
                fillColor: '#e74c3c',    // Fill color
                fillOpacity: 0.1,        // Low fill opacity for subtle highlighting
                dashArray: '5, 10',      // Dashed line pattern
                interactive: true,       // Allow interaction
                pane: 'overlayPane'      // Ensure it's below markers
            });

            // Add popup with region information
            const popupContent = `
                <div class="region-boundary-popup">
                    <h4 style="margin: 0 0 8px 0; color: #2c3e50; font-size: 14px; font-weight: bold;">
                        Kurdistan Region of Iraq
                    </h4>
                    <div style="font-size: 12px; color: #7f8c8d; line-height: 1.4;">
                        <div style="margin-top: 6px; font-style: italic; color: #95a5a6;">
                            Administrative boundary of Kurdistan Region
                        </div>
                    </div>
                </div>
            `;

            this.kurdistanRegionBoundary.bindPopup(popupContent, {
                closeButton: true,
                autoClose: false,
                closeOnClick: false
            });

            // Add hover effects
            this.kurdistanRegionBoundary.on('mouseover', function() {
                this.setStyle({
                    weight: 3,
                    opacity: 1,
                    fillOpacity: 0.2
                });
            });

            this.kurdistanRegionBoundary.on('mouseout', function() {
                this.setStyle({
                    weight: 2,
                    opacity: 0.8,
                    fillOpacity: 0.1
                });
            });

            // Add to map
            this.kurdistanRegionBoundary.addTo(this.map);

        } catch (error) {
            console.error('Error creating Kurdistan Region boundary:', error);
        }
    }

    // Method to remove Kurdistan Region boundary
    removeKurdistanRegionBoundary() {
        if (this.kurdistanRegionBoundary && this.map.hasLayer(this.kurdistanRegionBoundary)) {
            this.map.removeLayer(this.kurdistanRegionBoundary);
            this.kurdistanRegionBoundary = null;
        }
    }

    // Method to toggle Kurdistan Region boundary visibility
    toggleKurdistanBoundary() {
        if (this.kurdistanRegionBoundary) {
            const isVisible = this.map.hasLayer(this.kurdistanRegionBoundary);

            if (isVisible) {
                this.map.removeLayer(this.kurdistanRegionBoundary);
            } else {
                this.kurdistanRegionBoundary.addTo(this.map);
            }

            return !isVisible; // Return new visibility state
        }
        return false;
    }

    createPopupContent(popupData) {
        const { title, description, address } = popupData;
        
        // Helper function to get text from multilingual object or string
        const getLocalizedText = (text) => {
            if (!text) return '';
            
            // If it's already a string, return it
            if (typeof text === 'string') return text;
            
            // If it's an object (multilingual), extract the appropriate language
            if (typeof text === 'object' && text !== null) {
                // Get current locale from window or default to 'en'
                const locale = window.currentLocale || 'en';
                
                // Try current locale first, then fallback to 'en', then any available value
                return text[locale] || text['en'] || text[Object.keys(text)[0]] || '';
            }
            
            return '';
        };
        
        let content = `<div class="project-map-popup">`;
        
        const localizedTitle = getLocalizedText(title);
        if (localizedTitle) {
            content += `<h3 class="popup-title">${localizedTitle}</h3>`;
        }
        
        const localizedDescription = getLocalizedText(description);
        if (localizedDescription) {
            content += `<p class="popup-description">${localizedDescription}</p>`;
        }
        
        const localizedAddress = getLocalizedText(address);
        if (localizedAddress) {
            content += `<p class="popup-address"><strong>Address:</strong> ${localizedAddress}</p>`;
        }
        
        content += `</div>`;
        
        return content;
    }

    setBounds(bounds, options = {}) {
        // Handle backward compatibility - if options is boolean, treat as fitBounds
        if (typeof options === 'boolean') {
            options = { fitBounds: options };
        }
        
        const { fitBounds = false, preserveZoom = false, maxZoom = null } = options;
        
        if (bounds && bounds.length >= 2) {
            let boundsData;
            
            // Handle different bounds formats
            if (bounds.length === 4 && Array.isArray(bounds[0])) {
                // Format: [[lat1, lng1], [lat2, lng2]] - Rectangle bounds
                boundsData = { type: 'rectangle', data: bounds };
            } else if (bounds.length === 4 && typeof bounds[0] === 'number') {
                // Format: [lat1, lng1, lat2, lng2] - Rectangle bounds
                boundsData = { type: 'rectangle', data: [[bounds[0], bounds[1]], [bounds[2], bounds[3]]] };
            } else if (Array.isArray(bounds) && bounds.length > 0 && typeof bounds[0] === 'object') {
                // Check if it's polygon coordinates with lat/lng properties
                if (bounds[0].lat !== undefined && bounds[0].lng !== undefined) {
                    // Polygon format: [{lat: x, lng: y}, {lat: x, lng: y}, ...]
                    const coordinates = bounds.map(point => [point.lat, point.lng]);
                    boundsData = { type: 'polygon', data: coordinates };
                } else if (Array.isArray(bounds[0]) && bounds[0].length >= 2) {
                    // Format: [[lat, lng], [lat, lng], ...] - Polygon coordinates
                    boundsData = { type: 'polygon', data: bounds };
                } else {
                    // Format: [[[lat1, lng1]], [[lat2, lng2]]] - Rectangle bounds
                    boundsData = { type: 'rectangle', data: bounds.map(coord => Array.isArray(coord[0]) ? coord[0] : coord) };
                }
            } else {
                // Fallback to rectangle
                boundsData = { type: 'rectangle', data: bounds };
            }
            
            // Store current zoom if we need to preserve it
            const currentZoom = preserveZoom ? this.map.getZoom() : null;
            
            // Only fit map to bounds if requested (to preserve zoom level)
            if (fitBounds) {
                const fitOptions = {};
                
                // If maxZoom is not specified, use a default minimum zoom
                if (maxZoom === null) {
                    const minZoom = this.options.zoom || 6; // Use options.zoom as minimum or default to 6
                    fitOptions.minZoom = minZoom;
                } else {
                    fitOptions.maxZoom = maxZoom;
                }
                
                this.map.fitBounds(boundsData.data, fitOptions);
                
                // Ensure we don't zoom out too far
                const afterFitZoom = this.map.getZoom();
                const minZoom = this.options.zoom || 6;
                if (afterFitZoom < minZoom) {
                    this.map.setZoom(minZoom);
                }
                
                // Restore zoom level if preserveZoom is true
                if (preserveZoom && currentZoom !== null) {
                    this.map.setZoom(currentZoom);
                }
            }
            
            // Always add bounds overlay
            this.addBoundsOverlay(boundsData);
        }
    }

    addBoundsOverlay(boundsData) {
        // Remove existing bounds overlay
        if (this.boundsOverlay) {
            this.map.removeLayer(this.boundsOverlay);
        }
        
        // Create appropriate overlay based on bounds type
        if (boundsData.type === 'polygon') {
            // Create polygon overlay for polygon bounds
            this.boundsOverlay = L.polygon(boundsData.data, {
                color: '#008CD3',
                weight: 2,
                opacity: 0.8,
                fillColor: '#008CD3',
                fillOpacity: 0.1,
                dashArray: '5, 5'
            }).addTo(this.map);
        } else {
            // Create rectangle overlay for rectangle bounds
            this.boundsOverlay = L.rectangle(boundsData.data, {
                color: '#008CD3',
                weight: 2,
                opacity: 0.8,
                fillColor: '#008CD3',
                fillOpacity: 0.1,
                dashArray: '5, 5'
            }).addTo(this.map);
        }
        
        // Add popup to bounds
        this.boundsOverlay.bindPopup('<div class="text-sm font-medium">City Bounds</div>');
    }

    setCenter(lat, lng, zoom = null) {
        // If zoom is not provided, use current zoom, but don't go below the initial zoom setting
        const minZoom = this.options.zoom || 6; // Use options.zoom as minimum or default to 6
        const currentZoom = this.map.getZoom();
        
        // Determine the zoom level to use
        let zoomToUse = zoom;
        if (zoomToUse === null) {
            zoomToUse = currentZoom; // Keep current zoom if not specified
        }
        
        // Ensure zoom is at least the minimum
        if (zoomToUse < minZoom) {
            zoomToUse = minZoom;
        }
        
        this.map.setView([lat, lng], zoomToUse);
    }

    destroy() {
        if (this.map) {
            // Remove bounds overlay if it exists
            if (this.boundsOverlay) {
                this.map.removeLayer(this.boundsOverlay);
                this.boundsOverlay = null;
            }

            // Remove Kurdistan Region boundary
            this.removeKurdistanRegionBoundary();

            this.map.remove();
            this.map = null;
            this.markers = [];
        }
    }

    resize() {
        if (this.map) {
            this.map.invalidateSize();
        }
    }
}

// Export for use in other scripts
window.ProjectMap = ProjectMap;

export { ProjectMap };
