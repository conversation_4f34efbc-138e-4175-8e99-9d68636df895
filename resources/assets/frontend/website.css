@tailwind base;
@tailwind components;
@tailwind utilities;

.outer,
.out-container {
    padding: 0 max(1rem, 4vmin);
}

.main-slide-in {
    animation: slide-in 1s cubic-bezier(0.19, 1, 0.22, 1) forwards;
}

@keyframes slide-in {
    0% {
        transform: translateY(-20%);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

.swiper-clients .swiper-wrapper {
    transition-timing-function: linear;
}

.inner,
.container {
    max-width: 82rem;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
}

.floating-header {
    visibility: hidden;
    opacity: 0;
    transition: all 0.5s cubic-bezier(0.19, 1, 0.22, 1);
    transform: translate3d(0, 120%, 0);
}

.floating-header.floating-active {
    visibility: visible;
    opacity: 1;
    transition: all 0.5s cubic-bezier(0.22, 1, 0.27, 1);
    transform: translateZ(0);
}

.prose .ql-video {
    @apply max-w-full w-full;
    min-height: 400px;
    aspect-ratio: 16 / 9;
}

/* Mobile Menu Trigger
/* ---------------------------------------------------------- */

.burger {
    position: relative;
    cursor: pointer;
}

.burger-box {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 33px;
    height: 33px;
}

.burger-box {
    color: theme("colors.brand.700");
}

.burger-box::before {
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    bottom: 0;
    margin: auto;
    content: "";
    width: 100%;
    height: 1px;
    background: currentcolor;
    transition: transform 300ms cubic-bezier(0.2, 0.6, 0.3, 1),
        width 300ms cubic-bezier(0.2, 0.6, 0.3, 1);
    will-change: transform, width;
}

.burger-inner::before,
.burger-inner::after {
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    bottom: 0;
    margin: auto;
    content: "";
    width: 100%;
    height: 1px;
    background: currentcolor;
    transition: transform 250ms cubic-bezier(0.2, 0.7, 0.3, 1),
        width 250ms cubic-bezier(0.2, 0.7, 0.3, 1);
    will-change: transform, width;
}

.burger-inner::before {
    transform: translatey(-6px);
}
.burger-inner::after {
    transform: translatey(6px);
}

body:not(.head-open) .burger:hover .burger-inner::before {
    transform: translatey(-8px);
}
body:not(.head-open) .burger:hover .burger-inner::after {
    transform: translatey(8px);
}

.head-open .burger-box::before {
    width: 0;
    transform: translatex(19px);
    transition: transform 200ms cubic-bezier(0.2, 0.7, 0.3, 1),
        width 200ms cubic-bezier(0.2, 0.7, 0.3, 1);
}

.head-open .burger-inner::before {
    width: 26px;
    transform: translatex(6px) rotate(135deg);
}

.head-open .burger-inner::after {
    width: 26px;
    transform: translatex(6px) rotate(-135deg);
}
.mobile-menu {
    background: #fff;
    flex-direction: column;
    justify-content: space-around;
    align-items: stretch;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow-y: scroll;
    display: none;
}

.head-open .mobile-menu {
    display: flex;
}

.datepicker {
    padding: 0 2rem;
}
.datepicker-grid,
.days-of-week,
.days,
.datepicker-view,
.datepicker-picker {
    width: 100% !important;
}

.datepicker-picker {
    box-shadow: none !important;
}

.datepicker-header {
    @apply border-b border-gray-200 pb-1 mb-3;
}

.view-switch {
    font-size: 1.3rem !important;
}

.datepicker-cell {
    @apply border-2 border-transparent;
}
.datepicker-cell.focused {
    @apply bg-transparent text-brand border-2 border-brand bg-brand-50;
}

.slideshow {
    --swiper-theme-color: theme("colors.brand.500");
    --swiper-pagination-bullet-width: 0.8rem;
    --swiper-pagination-bullet-size: 0.8rem;
    --swiper-pagination-bullet-horizontal-gap: 0.5rem;
    --swiper-pagination-bullet-inactive-color: #999;
    --swiper-pagination-bullet-inactive-opacity: 0.35;
}

.swiper-pagination-bullet {
    border: 1px solid #fff;
}

[dir="ltr"] .swiper-horizontal > .swiper-pagination-bullets,
.swiper-pagination-bullets.swiper-pagination-horizontal,
.swiper-pagination-custom,
.swiper-pagination-fraction {
    padding-right: 2rem;
    bottom: 1.5rem !important;
}

[dir="rtl"] .swiper-pagination {
    text-align: left !important;
    padding-left: 2rem;
    bottom: 1.5rem !important;
}

.loading-skeleton h1,
.loading-skeleton h2,
.loading-skeleton h3,
.loading-skeleton h4,
.loading-skeleton h5,
.loading-skeleton h6,
.loading-skeleton p,
.loading-skeleton li,
.loading-skeleton .btn,
.loading-skeleton label,
.loading-skeleton .title,
.loading-skeleton .card-title,
.loading-skeleton .form-control {
    color: transparent !important;
    appearance: none !important;
    -webkit-appearance: none !important;
    background-color: #eee !important;
    border-color: #eee !important;
}
.dark .loading-skeleton h1,
.dark .loading-skeleton h2,
.dark .loading-skeleton h3,
.dark .loading-skeleton h4,
.dark .loading-skeleton h5,
.dark .loading-skeleton h6,
.dark .loading-skeleton p,
.dark .loading-skeleton li,
.dark .loading-skeleton .btn,
.dark .loading-skeleton label,
.dark .loading-skeleton .title,
.dark .loading-skeleton .card-title,
.dark .loading-skeleton .form-control {
    background-color: #111622 !important;
    border-color: #111622 !important;
}
.loading-skeleton h1::placeholder,
.loading-skeleton h2::placeholder,
.loading-skeleton h3::placeholder,
.loading-skeleton h4::placeholder,
.loading-skeleton h5::placeholder,
.loading-skeleton h6::placeholder,
.loading-skeleton p::placeholder,
.loading-skeleton li::placeholder,
.loading-skeleton .btn::placeholder,
.loading-skeleton label::placeholder,
.loading-skeleton .title::placeholder,
.loading-skeleton .card-title::placeholder,
.loading-skeleton .form-control::placeholder {
    color: transparent !important;
}
@keyframes loading-skeleton {
    from {
        opacity: 0.4;
    }
    to {
        opacity: 1;
    }
}
.loading-skeleton {
    pointer-events: none;
    animation: loading-skeleton 1s infinite alternate;
}
.loading-skeleton img {
    filter: grayscale(100) contrast(0%) brightness(1.8);
}

.dark .loading-skeleton img {
    filter: grayscale(100) contrast(0%) brightness(0);
}

figure {
    margin: 0;
    display: grid;
    grid-template-rows: 1fr auto;
    margin-bottom: 1.5rem;
    break-inside: avoid;
}

figure > img {
    grid-row: 1 / -1;
    grid-column: 1;
}

figure a {
    color: black;
    text-decoration: none;
}

figcaption {
    grid-row: 2;
    grid-column: 1;
    background-color: rgba(255, 255, 255, 0.5);
    padding: 0.2em 0.5em;
    justify-self: start;
}

@media only screen and (min-width: 768px) {
    #gallery {
        column-count: 3;
        column-gap: 1.5rem;
    }

    #gallery.single-gallery {
        column-count: 3;
        column-gap: 1rem;
    }

    #gallery.apartment-gallery {
        column-count: 2;
        column-gap: 1rem;
    }
}
