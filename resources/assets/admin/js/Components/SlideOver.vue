<template>
    <TransitionRoot as="template" :show="open">
        <Dialog as="div" class="relative z-10" @close="emit('close')">
            <TransitionChild as="template" enter="ease-in-out duration-200" enter-from="opacity-0"
                enter-to="opacity-100" leave="ease-in-out duration-200" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            </TransitionChild>

            <div class="fixed inset-0 overflow-hidden">
                <div class="absolute inset-0 overflow-hidden">
                    <div class="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
                        <TransitionChild as="template"
                            enter="transform transition ease-in-out duration-200 sm:duration-300"
                            enter-from="translate-x-full" enter-to="translate-x-0"
                            leave="transform transition ease-in-out duration-200 sm:duration-300"
                            leave-from="translate-x-0" leave-to="translate-x-full">
                            <DialogPanel class="pointer-events-auto relative w-96">
                                <TransitionChild as="template" enter="ease-in-out duration-200" enter-from="opacity-0"
                                    enter-to="opacity-100" leave="ease-in-out duration-200" leave-from="opacity-100"
                                    leave-to="opacity-0">
                                    <div class="absolute top-0 left-0 -ml-8 flex pt-4 pr-2 sm:-ml-10 sm:pr-4">
                                        <button type="button"
                                            class="rounded-md text-gray-300 hover:text-white focus:outline-none focus:ring-2 focus:ring-white"
                                            @click="emit('close')">
                                            <span class="sr-only">Close panel</span>
                                            <XMarkIcon class="h-6 w-6" aria-hidden="true" />
                                        </button>
                                    </div>
                                </TransitionChild>
                                <div class="h-full overflow-y-auto bg-white p-8">
                                    <div class="space-y-6 pb-16">
                                        <div class="mt-6">
                                                <h3 class="font-medium text-lg text-gray-900 mb-2 flex justify-between capitalize">Date</h3>
                                                <p class="text-sm font-medium text-gray-500">{{ date }} - {{ time }}</p>
                                            </div>
                                        <div>
                                            <h3 class="font-medium text-lg text-gray-900 mb-4 flex justify-between capitalize">
                                                <span
                                                :class="[
                                                    'capitalize font-semibold inline-block rounded',
                                                    {'text-red-500': action == 'deleted'},
                                                    {'text-green-500': action == 'created'},
                                                    {'text-blue-500': action == 'updated'},
                                                ]"
                                                >{{ action }}</span>
                                                <span>{{ model }}</span>
                                            </h3>
                                            <dl class="mt-2 divide-y divide-gray-200 border-t border-b border-gray-200">
                                                <div v-for="(change, keyName) in allChanages" :key="keyName" class="flex justify-between py-3 text-sm font-medium">
                                                    <dt class="text-gray-500">{{ keyName }}</dt>
                                                    <dd class="text-gray-900">
                                                        <span class="align-middle inline-block text-gray-500 truncate max-w-[6rem]">{{ change.old }}</span>
                                                        <ArrowLongRightIcon class="align-middle h-6 inline-block mx-1" />
                                                        <span class="align-middle inline-block truncate max-w-[6rem]">{{ change.new }}</span>
                                                    </dd>
                                                </div>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </DialogPanel>
                        </TransitionChild>
                    </div>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>
  
<script setup>
import { Dialog, DialogPanel, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { XMarkIcon, ArrowLongRightIcon } from '@heroicons/vue/24/outline'
import { computed } from 'vue';

const props = defineProps({
    open: {
        type: Boolean,
        default: false,
    },
    changes: {
        type: Object,
        default: () => ({}),
    },
    model: {
        type: String,
        required: false,
    },
    action: {
        type: String,
        required: false,
    },
    date: {
        type: String,
        required: false,
    },
    time: {
        type: String,
        required: false,
    },
});
const emit = defineEmits(['close']);

const allChanages = computed(() => {
    const { changes } = props;
    
    if(!changes.old) {
        return;
    }

    return Object.keys(changes.old).reduce((acc, key) => {
        if(changes.old[key] !== changes.attributes[key]) {
            acc[key] = {old: changes.old[key], new: changes.attributes[key]};
        }
        return acc;
    }, {})
})

</script>