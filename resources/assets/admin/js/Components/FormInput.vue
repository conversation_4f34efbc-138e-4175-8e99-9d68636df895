<script setup>
import { computed, onMounted, ref } from 'vue';
import Label from '@/Components/Label.vue';
import FormError from '@/Components/FormError.vue';
import { v4 as uuid } from 'uuid';

const props = defineProps({
    id: {
        type: String,
        default: () => `text-input-${uuid()}`,
    },
    modelValue: {
        type: [String, Number],
        default: null,
    },
    error: {
        type: String,
        default: null,
    },
    
    label: {
        type: String,
    },

    type: {
        type: String,
        default: 'text'
    },
});

defineEmits(['update:modelValue']);

const input = ref(null);

const inputComponent = computed(() => {
    if(props.type == 'textarea') {
        return 'textarea';
    }
    return 'input';
})

onMounted(() => {
    if (input.value.hasAttribute('autofocus')) {
        input.value.focus();
    }
});
</script>
    
<template>
    <div v-bind:class="$attrs.class">
        <Label :for="id">{{ label }}</Label>
        <div class="my-1 relative">
            <component :is="inputComponent" :id="id" v-bind="{ ...$attrs, class: null }" :class="[error ? 'text-red-900 border-red-300 placeholder-red-300 focus:border-red-500 focus:outline-none focus:ring-red-500' : 'border-gray-300 focus:border-primary-500 focus:ring-primary-500']" :type="type"
                class="block w-full rounded-md shadow-sm sm:text-sm"
                :value="modelValue" @input="$emit('update:modelValue', $event.target.value)" ref="input" />
                <slot name="loading" />
        </div>
        <FormError v-if="error">{{ error }}</FormError>
    </div>
</template>
    