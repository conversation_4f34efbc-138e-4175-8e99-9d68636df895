<script setup>
import { ref, computed, onMounted } from 'vue';
import Label from '@/Components/Label.vue';
import FormError from '@/Components/FormError.vue';
import route from 'ziggy';
import { useToast } from "vue-toastification"
import { Inertia } from '@inertiajs/inertia';

const router = Inertia;

const { success: successToast, error: errorToast } = useToast();

const props = defineProps({
  existingImages: {
    type: Array,
    default: () => ([])
  },
  error: {
    type: String,
    default: null
  },
  label: {
    type: String,
    default: "Project Gallery"
  },
  required: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['imagesUpdated', 'imageDeleted']);

const images = ref([]);
const pendingUploads = ref([]);
const deletingImageId = ref(null);
const isUploading = ref(false);
const uploadProgress = ref(0);
const dragActive = ref(false);
const showDeleteConfirm = ref(false);
const imageToDelete = ref(null);

const fileInput = ref(null);

// Computed properties
const hasImages = computed(() => images.value.length > 0 || pendingUploads.value.length > 0);
const hasError = computed(() => !!props.error);
const labelText = computed(() => props.required ? `${props.label} *` : props.label);

// Initialize component with existing images
onMounted(() => {
  if (props.existingImages && props.existingImages.length > 0) {
    images.value = [...props.existingImages];
  }
});

// Handle file selection
function handleFileSelect(event) {
  addFiles(event.target.files);
  event.target.value = null; // Reset input to allow selecting the same file again
}

// Handle drag and drop
function handleDrag(event) {
  event.preventDefault();
  event.stopPropagation();
  if (event.type === 'dragenter' || event.type === 'dragover') {
    dragActive.value = true;
  } else if (event.type === 'dragleave') {
    dragActive.value = false;
  }
}

function handleDrop(event) {
  event.preventDefault();
  event.stopPropagation();
  dragActive.value = false;
  if (event.dataTransfer.files.length > 0) {
    addFiles(event.dataTransfer.files);
  }
}

// Process files before adding to pending uploads
function addFiles(fileList) {
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp'];
  const maxFileSize = 1 * 1024 * 1024; // 1MB
  
  Array.from(fileList).forEach(file => {
    // Check file type
    if (!allowedTypes.includes(file.type)) {
      alert('Only JPG, PNG, and WebP files are allowed');
      return;
    }
    
    // Check file size
    if (file.size > maxFileSize) {
      alert('File size must be less than 1MB');
      return;
    }
    
    // Create a preview URL
    const preview = URL.createObjectURL(file);
    
    // Add to pending uploads
    const newImage = {
      id: `temp-${Date.now()}-${pendingUploads.value.length}`,
      file,
      preview,
      name: file.name,
      isPending: true
    };
    
    pendingUploads.value.push(newImage);
    emit('imagesUpdated', getFiles());
  });
}

// Show delete confirmation dialog
function confirmDeleteImage(image) {
  imageToDelete.value = image;
  showDeleteConfirm.value = true;
}

// Cancel delete operation
function cancelDelete() {
  showDeleteConfirm.value = false;
  imageToDelete.value = null;
}

// Delete an existing image from server
async function deleteImage() {
  if (!imageToDelete.value) return;
  
  const id = imageToDelete.value.id;
  deletingImageId.value = id;
  showDeleteConfirm.value = false;
  
  try {
    router.delete(route('admin.projects.media.destroy', {media: id}), {
      preserveScroll: true,
      onStart: () => {
        isUploading.value = true;
        uploadProgress.value = 0;
      },
      onProgress: (event) => {
        if (event.lengthComputable) {
          uploadProgress.value = Math.round((event.loaded / event.total) * 100);
        }
      },
      onFinish: () => {
        isUploading.value = false;
        uploadProgress.value = 0;
      },
      onSuccess: () => {
        images.value = images.value.filter(img => img.id !== id);
        emit('imageDeleted', id);
      },
      onError: (error) => {
        console.error(error);
        errorToast('Failed to delete image. Please try again.');
      }
    });
    
  } catch (e) {
    console.error(e)
    errorToast('Failed to delete image. Please try again.');
  } finally {
    deletingImageId.value = null;
    imageToDelete.value = null;
  }
}

// Remove a pending upload
function removePendingUpload(tempId) {
  pendingUploads.value = pendingUploads.value.filter(img => img.id !== tempId);
  emit('imagesUpdated', getFiles());
}

// Get array of File objects for form submission
function getFiles() {
  return pendingUploads.value.map(item => item.file);
}

// Expose methods to parent component
defineExpose({
  getFiles,
  clearPendingUploads: () => {
    pendingUploads.value = [];
  }
});
</script>

<template>
  <div class="gallery-uploader">
    <Label>{{ labelText }}</Label>
    
    <!-- Uploader Area -->
    <div 
      class="mt-2 bg-white border-2 border-dashed rounded-lg p-4 text-center relative"
      :class="{
        'border-primary-400 bg-primary-50': dragActive,
        'border-gray-300': !dragActive && !hasError,
        'border-red-500': hasError
      }"
      @dragenter="handleDrag"
      @dragover="handleDrag"
      @dragleave="handleDrag"
      @drop="handleDrop"
    >
      <div v-if="isUploading" class="absolute inset-0 bg-white/80 flex items-center justify-center z-10">
        <div class="text-center">
          <div class="w-full max-w-xs bg-gray-200 rounded-full h-2.5">
            <div class="bg-blue-600 h-2.5 rounded-full" :style="{width: `${uploadProgress}%`}"></div>
          </div>
          <p class="mt-2 text-sm text-gray-600">Uploading... {{ uploadProgress }}%</p>
        </div>
      </div>
      
      <input
        type="file"
        multiple
        ref="fileInput"
        class="hidden"
        accept="image/jpeg,image/png,image/webp"
        @change="handleFileSelect"
      />
      
      <!-- Empty state -->
      <div v-if="!hasImages" class="py-6">
        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
          <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
        <p class="mt-1 text-sm text-gray-600">
          Drag and drop images here, or 
          <button 
            type="button" 
            class="text-primary-600 hover:text-primary-700 font-medium"
            @click="fileInput.click()"
          >
            browse
          </button>
        </p>
        <p class="text-xs text-gray-500 mt-1">JPG, PNG, WebP up to 1MB</p>
      </div>
      
      <!-- Gallery Preview -->
      <div v-else class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 mt-3">
        <!-- Existing images from server -->
        <div 
          v-for="image in images" 
          :key="image.id" 
          class="relative group aspect-square border border-gray-200 rounded-md overflow-hidden"
        >
          <img :src="image.original_url || image.preview" class="w-full h-full object-cover" />
          <div class="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <button 
              type="button"
              @click="confirmDeleteImage(image)"
              class="p-1.5 bg-red-500 hover:bg-red-600 rounded-full text-white"
              :disabled="deletingImageId === image.id"
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
          <div v-if="deletingImageId === image.id" class="absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center">
            <svg class="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </div>
        </div>
        
        <!-- Pending uploads -->
        <div 
          v-for="image in pendingUploads" 
          :key="image.id" 
          class="relative group aspect-square border border-gray-200 rounded-md overflow-hidden"
        >
          <img :src="image.preview" class="w-full h-full object-cover" />
          <div class="absolute top-0 left-0 bg-primary-500 text-white text-xs py-0.5 px-2 rounded-br">
            New
          </div>
          <div class="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <button 
              type="button"
              @click="removePendingUpload(image.id)"
              class="p-1.5 bg-red-500 hover:bg-red-600 rounded-full text-white"
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
        
        <!-- Add more images button -->
        <div 
          class="aspect-square border-2 border-dashed border-gray-300 rounded-md flex items-center justify-center cursor-pointer hover:bg-gray-50"
          @click="fileInput.click()"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-gray-400">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
          </svg>
        </div>
      </div>
    </div>
    
    <!-- Error message -->
    <FormError class="mt-1" v-if="error">{{ error }}</FormError>
    
    <!-- Delete Confirmation Dialog -->
    <div v-if="showDeleteConfirm" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="cancelDelete"></div>

        <!-- Modal panel -->
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                  Delete Image
                </h3>
                <div class="mt-2">
                  <p class="text-sm text-gray-500">
                    Are you sure you want to delete this image? This action cannot be undone.
                  </p>
                  <div v-if="imageToDelete" class="mt-3 flex items-center space-x-3">
                    <img :src="imageToDelete.original_url || imageToDelete.preview" class="w-16 h-16 object-cover rounded-md border flex-shrink-0" />
                    <div class="text-xs text-gray-600">
                      <p v-if="imageToDelete.name">{{ imageToDelete.name }}</p>
                      <p v-else-if="imageToDelete.file_name">{{ imageToDelete.file_name }}</p>
                      <p v-else>Gallery image</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button 
              type="button" 
              @click="deleteImage"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Delete
            </button>
            <button 
              type="button" 
              @click="cancelDelete"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.gallery-uploader {
  margin-bottom: 1rem;
}
</style>
