<template>
  <div class="bg-white p-6 rounded-lg shadow">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-medium text-gray-900">Project Maps</h3>
      <button
        type="button"
        @click="showMapForm = !showMapForm"
        class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      >
        <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Add Map
      </button>
    </div>

    <!-- Existing Maps -->
    <div v-if="existingMaps.length > 0" class="mb-6">
      <h4 class="text-md font-medium text-gray-700 mb-3">Existing Maps</h4>
      <div class="space-y-3">
        <div
          v-for="map in existingMaps"
          :key="map.id"
          class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
        >
          <div class="flex-1">
            <h5 class="font-medium text-gray-900">{{ map.name }}</h5>
            <p class="text-sm text-gray-500">
              Type: 
              <span class="capitalize">{{ map.type === 'single' ? 'Single Map' : 'Multi-City' }}</span>
            </p>
            <p v-if="map.type === 'single' && map.markers_count" class="text-sm text-gray-500">
              {{ map.markers_count }} markers
            </p>
            <p v-if="map.type === 'multi_city' && map.cities_count" class="text-sm text-gray-500">
              {{ map.cities_count }} cities
            </p>
          </div>
          <div class="flex items-center space-x-2">
            <Link
              :href="route('admin.project-maps.show', map.id)"
              class="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
            >
              View
            </Link>
            <Link
              :href="route('admin.project-maps.edit', {project_map: map.id})"
              class="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
            >
              Edit
            </Link>
            <button
              type="button"
              @click="deleteMap(map.id)"
              class="text-red-600 hover:text-red-900 text-sm font-medium"
            >
              Delete
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Add New Map Form -->
    <div v-if="showMapForm" class="border-t pt-6">
      <h4 class="text-md font-medium text-gray-700 mb-4">Add New Map</h4>
      
      <div class="space-y-4">
        <!-- Map Name -->
        <div>
          <label for="map_name" class="block text-sm font-medium text-gray-700">
            Map Name
          </label>
          <input
            id="map_name"
            v-model="newMap.name"
            type="text"
            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            placeholder="Enter map name"
          />
        </div>

        <!-- Map Type -->
        <div>
          <label class="block text-sm font-medium text-gray-700">
            Map Type
          </label>
          <div class="mt-2 space-y-2">
            <label class="inline-flex items-center">
              <input
                v-model="newMap.type"
                type="radio"
                value="single"
                class="form-radio text-indigo-600"
              />
              <span class="ml-2">Single Map (Multiple Markers)</span>
            </label>
            <label class="inline-flex items-center">
              <input
                v-model="newMap.type"
                type="radio"
                value="multi_city"
                class="form-radio text-indigo-600"
              />
              <span class="ml-2">Multi-City Maps</span>
            </label>
          </div>
        </div>

        <!-- Description -->
        <div>
          <label for="map_description" class="block text-sm font-medium text-gray-700">
            Description (optional)
          </label>
          <textarea
            id="map_description"
            v-model="newMap.description"
            rows="2"
            class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            placeholder="Enter map description"
          ></textarea>
        </div>

        <!-- Quick Setup for Single Map -->
        <div v-if="newMap.type === 'single'" class="space-y-3">
          <h5 class="font-medium text-gray-800">Quick Setup</h5>
          
          <!-- Default Center -->
          <div class="grid grid-cols-2 gap-3">
            <div>
              <label for="default_lat" class="block text-sm font-medium text-gray-700">
                Default Latitude
              </label>
              <input
                id="default_lat"
                v-model.number="newMap.default_center[0]"
                type="number"
                step="any"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                placeholder="40.7128"
              />
            </div>
            <div>
              <label for="default_lng" class="block text-sm font-medium text-gray-700">
                Default Longitude
              </label>
              <input
                id="default_lng"
                v-model.number="newMap.default_center[1]"
                type="number"
                step="any"
                class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                placeholder="-74.0060"
              />
            </div>
          </div>

          <!-- Default Zoom -->
          <div>
            <label for="default_zoom" class="block text-sm font-medium text-gray-700">
              Default Zoom Level
            </label>
            <input
              id="default_zoom"
              v-model.number="newMap.default_zoom"
              type="number"
              min="1"
              max="20"
              class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
            />
          </div>
        </div>

        <!-- City Selection for Multi-City Maps -->
        <div v-if="newMap.type === 'multi_city'" class="space-y-3">
          <h5 class="font-medium text-gray-800">City Selection</h5>
          <p class="text-sm text-gray-600">Select existing cities for this map. You can manage cities separately in the Cities section.</p>
          <CitySelector
            v-model="newMap.selectedCityIds"
            :available-cities="availableCities"
            @cities-loaded="onCitiesLoaded"
          />
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-3 pt-4">
          <button
            type="button"
            @click="cancelMapForm"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
          >
            Cancel
          </button>
          <button
            type="button"
            @click="saveMap"
            :disabled="!canSaveMap"
            class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
          >
            Save & Continue Editing
          </button>
        </div>
      </div>
    </div>

    <!-- No Maps Message -->
    <div v-if="existingMaps.length === 0 && !showMapForm" class="text-center py-6">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m0 0L9 7"></path>
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">No maps</h3>
      <p class="mt-1 text-sm text-gray-500">Get started by adding a map to this project.</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Link, useForm } from '@inertiajs/inertia-vue3'
import CitySelector from '@/Components/CitySelector.vue'

const props = defineProps({
  projectId: {
    type: [String, Number],
    default: null,
  },
  existingMaps: {
    type: Array,
    default: () => [],
  },
  availableCities: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['mapCreated', 'mapDeleted'])

const showMapForm = ref(false)

const newMap = ref({
  name: '',
  type: 'single',
  description: '',
  default_center: [40.7128, -74.0060], // Default to NYC
  default_zoom: 10,
  selectedCityIds: [], // For multi-city maps
})

const canSaveMap = computed(() => {
  const baseValid = newMap.value.name.trim() !== '' && 
                   newMap.value.type && 
                   props.projectId
  
  if (newMap.value.type === 'multi_city') {
    return baseValid && newMap.value.selectedCityIds.length > 0
  }
  
  return baseValid
})

const resetMapForm = () => {
  newMap.value = {
    name: '',
    type: 'single',
    description: '',
    default_center: [40.7128, -74.0060],
    default_zoom: 10,
    selectedCityIds: [],
  }
}

const cancelMapForm = () => {
  resetMapForm()
  showMapForm.value = false
}

const onCitiesLoaded = (cities) => {
  // Handle cities loaded event if needed
  console.log('Cities loaded:', cities)
}

const saveMap = async () => {
  if (!canSaveMap.value) return

  const formData = {
    project_id: props.projectId,
    name: newMap.value.name,
    type: newMap.value.type,
    description: newMap.value.description,
    default_center: newMap.value.type === 'single' ? newMap.value.default_center : null,
    default_zoom: newMap.value.type === 'single' ? newMap.value.default_zoom : 10,
    is_active: true,
  }

  // Add selected city IDs for multi-city maps
  if (newMap.value.type === 'multi_city') {
    formData.selected_city_ids = newMap.value.selectedCityIds
  }

  const form = useForm(formData)

  form.post(route('admin.project-maps.store'), {
    preserveScroll: true,
    onSuccess: (response) => {
      resetMapForm()
      showMapForm.value = false
      emit('mapCreated', response.props.flash?.map)
      // Optionally redirect to edit the map with more details
      if (response.props.flash?.map?.id) {
        window.open(route('admin.project-maps.edit', {project_map: response.props.flash.map.id}), '_blank')
      }
    },
    onError: (errors) => {
      console.error('Error creating map:', errors)
    },
  })
}

const deleteMap = async (mapId) => {
  if (!confirm('Are you sure you want to delete this map? This action cannot be undone.')) {
    return
  }

  const form = useForm({})
  form.delete(route('admin.project-maps.destroy', mapId), {
    preserveScroll: true,
    onSuccess: () => {
      emit('mapDeleted', mapId)
    },
    onError: (errors) => {
      console.error('Error deleting map:', errors)
    },
  })
}

// Watch for project ID changes to reset form
watch(() => props.projectId, () => {
  if (showMapForm.value) {
    cancelMapForm()
  }
})
</script>
