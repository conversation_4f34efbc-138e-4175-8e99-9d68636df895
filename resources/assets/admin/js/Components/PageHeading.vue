<script setup>
import { Head } from '@inertiajs/inertia-vue3';

defineProps(['heading', 'description']);
</script>
<template>
    <Head v-if="heading">
        <title>{{ heading }}</title>
    </Head>
    <div class="sm:flex sm:items-center sm:justify-between">
        <div class="flex-shrink-0">
            <h1 class="text-xl font-semibold text-gray-900">{{ heading }}</h1>
            <p class="mt-2 text-sm text-gray-700" v-if="description">{{ description }}</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-8 sm:flex-none">
            <slot name="actions" />
        </div>
    </div>
</template>

