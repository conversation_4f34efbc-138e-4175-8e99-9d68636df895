<script setup>
import { computed } from 'vue';

const props = defineProps({
    color: {
        type: String,
        default: 'gray'
    }
});
const colorMap = {
        'primary': `text-primary-500 hover:bg-gray-100 focus:ring-primary-500`,
        'blue': `text-blue-500 hover:bg-blue-100 focus:ring-blue-500`,
        'red': `text-red-500 hover:bg-red-100 focus:ring-red-500`,
        'gray': `text-gray-500 hover:bg-gray-100 focus:ring-gray-500`,
        'green': `text-green-500 hover:bg-green-100 focus:ring-green-500`,
        'yellow': `text-yellow-500 hover:bg-yellow-100 focus:ring-yellow-500`,
        'orange': `text-orange-500 hover:bg-orange-100 focus:ring-orange-500`,
};
const classes = computed(() => {
    let color = props.color;

    if(! colorMap.hasOwnProperty(color)) {
        color = 'gray';
    }
    
    return colorMap[color];
})

</script>

<template>
    <button type="button" :class="['inline-flex items-center rounded-md px-3 py-2 text-sm font-medium leading-4 focus:outline-none focus:ring-2', classes]">
        <slot />
    </button>
</template>