<script setup>
    import { MagnifyingGlassIcon } from '@heroicons/vue/20/solid'
    import { debounce } from 'lodash';
    
    defineProps(['modelValue']);

    const emit = defineEmits(['update:modeValue']);

    const onInput =  debounce(function (e) {
        emit('update:modelValue', e.target.value)
    }, 350);
</script>
<template>
    <div class="relative mt-1 rounded-md shadow-sm inline-block">
      <div class="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
        <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" aria-hidden="true" />
      </div>
      <input v-bind="{...$attrs, class: null}" :value="modelValue" @input="onInput" type="search" name="search" class="block w-full rounded-md border-gray-300 pl-10 focus:border-primary-500 focus:ring-primary-500 sm:text-sm" />
    </div>
</template>