<template>
  <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
    <div class="flex justify-between items-start mb-4">
      <h4 class="text-lg font-medium text-gray-900">
        {{ marker.title?.en || `Marker ${index + 1}` }}
      </h4>
      <button @click="$emit('remove', index)" 
              class="text-red-600 hover:text-red-800 text-sm">
        Remove
      </button>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Title Fields -->
      <div class="md:col-span-2">
        <label class="block text-sm font-medium text-gray-700 mb-2">Marker Title</label>
        <div class="space-y-2">
          <input v-model="localMarker.title.en" 
                 placeholder="Title (English)" 
                 class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm" />
          <input v-model="localMarker.title.ar" 
                 placeholder="Title (Arabic)" 
                 class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm" />
          <input v-model="localMarker.title.ku" 
                 placeholder="Title (Kurdish)" 
                 class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm" />
        </div>
      </div>

      <!-- Coordinates -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Latitude</label>
        <input v-model.number="localMarker.coordinates.lat" 
               type="number" 
               step="0.000001"
               class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm" />
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Longitude</label>
        <input v-model.number="localMarker.coordinates.lng" 
               type="number" 
               step="0.000001"
               class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm" />
      </div>

      <!-- Icon and Color -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Icon</label>
        <select v-model="localMarker.icon" 
                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm">
          <option value="default">Default</option>
          <option value="home">Home</option>
          <option value="office">Office</option>
          <option value="store">Store</option>
          <option value="restaurant">Restaurant</option>
          <option value="hospital">Hospital</option>
          <option value="school">School</option>
        </select>
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Color</label>
        <div class="flex space-x-2">
          <input v-model="localMarker.color" 
                 type="color" 
                 class="h-8 w-16 border border-gray-300 rounded cursor-pointer" />
          <input v-model="localMarker.color" 
                 type="text" 
                 placeholder="#008CD3"
                 class="flex-1 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm" />
        </div>
      </div>

      <!-- Description -->
      <div class="md:col-span-2">
        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
        <div class="space-y-2">
          <textarea v-model="localMarker.description.en" 
                    placeholder="Description (English)" 
                    rows="2"
                    class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm"></textarea>
          <textarea v-model="localMarker.description.ar" 
                    placeholder="Description (Arabic)" 
                    rows="2"
                    class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm"></textarea>
          <textarea v-model="localMarker.description.ku" 
                    placeholder="Description (Kurdish)" 
                    rows="2"
                    class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm"></textarea>
        </div>
      </div>

      <!-- Status and Order -->
      <div>
        <label class="flex items-center">
          <input v-model="localMarker.is_active" 
                 type="checkbox" 
                 class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" />
          <span class="ml-2 text-sm text-gray-700">Active</span>
        </label>
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
        <input v-model.number="localMarker.sort_order" 
               type="number" 
               min="0"
               class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm" />
      </div>
    </div>

    <!-- Preview -->
    <div class="mt-4 p-3 bg-white rounded border">
      <h5 class="text-sm font-medium text-gray-700 mb-2">Preview</h5>
      <div class="flex items-center space-x-2">
        <div class="w-4 h-4 rounded-full" :style="{ backgroundColor: localMarker.color }"></div>
        <span class="text-sm">{{ localMarker.title?.en || 'Untitled Marker' }}</span>
        <span class="text-xs text-gray-500">
          ({{ localMarker.coordinates.lat?.toFixed(6) }}, {{ localMarker.coordinates.lng?.toFixed(6) }})
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch } from 'vue'

const props = defineProps({
  marker: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['update', 'remove'])

// Create a local reactive copy to avoid direct prop mutation
const localMarker = reactive({
  title: { en: '', ar: '', ku: '', ...props.marker.title },
  description: { en: '', ar: '', ku: '', ...props.marker.description },
  coordinates: { lat: 0, lng: 0, ...props.marker.coordinates },
  icon: props.marker.icon || 'default',
  color: props.marker.color || '#008CD3',
  is_active: props.marker.is_active !== undefined ? props.marker.is_active : true,
  sort_order: props.marker.sort_order || 0
})

// Watch for changes and emit updates
watch(localMarker, (newValue) => {
  emit('update', props.index, newValue)
}, { deep: true })
</script>
