<template>
  <div v-if="modelValue" class="fixed inset-0 z-[9999] overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="$emit('update:modelValue', false)"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-xl sm:w-full relative z-[10000]">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                {{ isEditing ? 'Edit Marker' : 'Add Marker' }}
              </h3>
              
              <div class="mt-4 space-y-6">
                <!-- Title Fields -->
                <div class="grid grid-cols-1 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Title (English) *</label>
                    <Input 
                      type="text"  
                      v-model="markerData.title.en" 
                      @blur="validateTitle"
                      placeholder="Enter marker title"
                      :class="errors.title.en ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''" 
                    />
                    <p v-if="errors.title.en" class="mt-1 text-sm text-red-600">{{ errors.title.en }}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Title (Kurdish)</label>
                    <Input type="text" v-model="markerData.title.ku" placeholder="Enter Kurdish translation" />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Title (Arabic)</label>
                    <Input type="text" v-model="markerData.title.ar" placeholder="Enter Kurdish translation" />
                  </div>
                </div>

                <!-- Coordinates -->
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Latitude *</label>
                    <Input 
                      v-model="markerData.coordinates.lat"
                      type="number" 
                      @blur="validateCoordinates"
                      step="0.000001"
                      :class="errors.coordinates.lat ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''" 
                    />
                    <p v-if="errors.coordinates.lat" class="mt-1 text-sm text-red-600">{{ errors.coordinates.lat }}</p>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Longitude *</label>
                    <Input 
                      v-model="markerData.coordinates.lng"
                      type="number" 
                      @blur="validateCoordinates"
                      step="0.000001" 
                      :class="errors.coordinates.lng ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''"
                    />
                    <p v-if="errors.coordinates.lng" class="mt-1 text-sm text-red-600">{{ errors.coordinates.lng }}</p>
                  </div>
                </div>

                <!-- Icon Selection -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Icon Type *</label>
                  <div class="flex flex-wrap gap-3 mt-2">
                    <div 
                      v-for="icon in iconOptions" 
                      :key="icon.value"
                      @click="markerData.icon_type = icon.value; validateIconType()"
                      class="cursor-pointer p-3 border rounded-md w-24 h-24 flex flex-col items-center justify-center transition-all duration-200"
                      :class="{
                        'bg-primary-50 border-primary-300 shadow-sm': markerData.icon_type === icon.value, 
                        'border-gray-200 hover:border-gray-300': markerData.icon_type !== icon.value,
                        'border-red-300': errors.icon_type && !markerData.icon_type
                      }"
                    >
                      <div class="w-10 h-10 flex items-center justify-center">
                        <img v-if="icon.value === 'bim'" :src="icon.preview" :alt="icon.label" class="w-8 h-8 object-contain mx-auto rounded-full bg-white shadow-sm" />
                        <img v-else :src="icon.preview" :alt="icon.label" class="w-10 h-10 object-contain mx-auto" />
                      </div>
                      <div class="text-xs text-center mt-2 font-medium">{{ icon.label }}</div>
                    </div>
                  </div>
                  <p v-if="errors.icon_type" class="mt-1 text-sm text-red-600">{{ errors.icon_type }}</p>
                </div>

                <!-- Description -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Description (English)</label>
                  <textarea v-model="markerData.description.en" 
                          placeholder="Enter marker description" 
                          rows="2"
                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"></textarea>
                </div>
                
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Description (Kurdish)</label>
                  <textarea v-model="markerData.description.ku" 
                          placeholder="Enter Kurdish translation" 
                          rows="2"
                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"></textarea>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Description (Arabic)</label>
                  <textarea v-model="markerData.description.ar" 
                          placeholder="Enter Arabic translation" 
                          rows="2"
                          class="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"></textarea>
                </div>

                <!-- Status -->
                <div>
                  <label class="flex items-center">
                    <input v-model="markerData.is_active" 
                          type="checkbox" 
                          class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" />
                    <span class="ml-2 text-sm text-gray-700">Active</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button @click="saveMarker" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm">
            {{ isEditing ? 'Update' : 'Add' }}
          </button>
          <button @click="$emit('update:modelValue', false)" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, reactive, watch } from 'vue'
import Input from '@/Components/Input.vue'
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  marker: {
    type: Object,
    default: () => ({
      title: { en: '', ku: '' },
      description: { en: '', ku: '' },
      coordinates: { lat: 36.1911, lng: 44.0094 }, // Default to Erbil
      icon_type: 'default',
      is_active: true
    })
  },
  index: {
    type: Number,
    default: -1
  }
})

const emit = defineEmits(['update:modelValue', 'save'])

const isEditing = computed(() => props.index >= 0)

// Create a copy of the marker data to avoid direct prop mutation
const markerData = reactive({
  title: { en: '', ku: '', ar: '' },
  description: { en: '', ku: '', ar: '' },
  coordinates: { lat: 36.1911, lng: 44.0094 },
  icon_type: 'default',
  is_active: true
})

// Form validation
const errors = reactive({
  title: { en: '' },
  coordinates: { lat: '', lng: '' },
  icon_type: ''
})

// Validate title field
const validateTitle = () => {
  errors.title.en = !markerData.title.en.trim() ? 'English title is required' : ''
  return !errors.title.en
}

// Validate coordinates
const validateCoordinates = () => {
  errors.coordinates.lat = !markerData.coordinates.lat ? 'Latitude is required' : ''
  errors.coordinates.lng = !markerData.coordinates.lng ? 'Longitude is required' : ''
  return !errors.coordinates.lat && !errors.coordinates.lng
}

// Validate icon type
const validateIconType = () => {
  errors.icon_type = !markerData.icon_type ? 'Icon type is required' : ''
  return !errors.icon_type
}

// Validate all fields
const validateForm = () => {
  const titleValid = validateTitle()
  const coordinatesValid = validateCoordinates()
  const iconTypeValid = validateIconType()
  return titleValid && coordinatesValid && iconTypeValid
}

// Reset marker data when the modal opens or closes
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    // Reset marker data when opening
    // Ensure coordinates are parsed as numbers
    const coords = props.marker.coordinates || { lat: 36.1911, lng: 44.0094 };
    
    Object.assign(markerData, {
      title: { en: '', ku: '', ar: '', ...props.marker.title },
      description: { en: '', ku: '', ar: '', ...props.marker.description },
      coordinates: { 
        lat: Number(coords.lat), 
        lng: Number(coords.lng)
      },
      icon_type: props.marker.icon_type || 'default',
      is_active: props.marker.is_active !== undefined ? props.marker.is_active : true
    })
  } else {
    // Reset validation errors when closing
    Object.keys(errors).forEach(key => {
      if (typeof errors[key] === 'object') {
        Object.keys(errors[key]).forEach(subKey => {
          errors[key][subKey] = ''
        })
      } else {
        errors[key] = ''
      }
    })
  }
}, { immediate: true })

// Icon options
const iconOptions = [
  { 
    value: 'default', 
    label: 'Default',
    preview: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png'
  },
  { 
    value: 'bim', 
    label: 'BIM Logo',
    preview: '/bim_logo.png'
  },
  { 
    value: 'atm', 
    label: 'ATM',
    preview: '/atm_icon.svg'
  }
]

const saveMarker = () => {
  // Validate the form
  const isValid = validateForm()
  
  if (!isValid) {
    // Focus on the first input with error
    if (errors.title.en) {
      document.querySelector('input[placeholder="Enter marker title"]').focus()
    } else if (errors.coordinates.lat) {
      document.querySelector('input[type="number"][step="0.000001"]').focus()
    }
    // For icon_type, we'll just show the error message since it's not a direct input field
    return
  }
  
  // Make sure coordinates are numbers, not strings
  const preparedData = {
    ...markerData,
    coordinates: {
      lat: Number(markerData.coordinates.lat),
      lng: Number(markerData.coordinates.lng)
    },
    index: props.index
  }
  
  emit('save', preparedData)
  emit('update:modelValue', false)
}
</script>
