<template>
  <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <div class="px-4 py-6 sm:px-0">
      <div class="border-4 border-dashed border-gray-200 rounded-lg p-6">
        <div class="mb-6">
          <h2 class="text-2xl font-bold text-gray-900 mb-4">Project Map Manager</h2>
          
          <!-- Map Type Selection -->
          <div class="bg-white p-4 rounded-lg shadow mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-3">Map Configuration</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Map Type</label>
                <select v-model="mapData.type" @change="onMapTypeChange" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500">
                  <option value="single">Single Map (Multiple Markers)</option>
                  <option value="multi_city">Multi-City Maps</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Map Title</label>
                <input v-model="mapData.title" type="text" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500" />
              </div>
            </div>
          </div>

          <!-- Map Preview -->
          <div class="bg-white p-4 rounded-lg shadow mb-6">
            <h3 class="text-lg font-medium text-gray-900 mb-3">Map Preview</h3>
            <ProjectMapPreview
              :map-data="mapData"
              :cities="cities"
              :markers="markers"
              @marker-click="onMarkerClick"
              @map-bounds-change="onMapBoundsChange"
            />
          </div>

          <!-- Single Map Markers Management -->
          <div v-if="mapData.type === 'single'" class="bg-white p-4 rounded-lg shadow mb-6">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-medium text-gray-900">Markers</h3>
              <button @click="addMarker" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                Add Marker
              </button>
            </div>
            <div class="space-y-3">
              <div v-for="(marker, index) in markers" :key="marker.id || index" 
                   class="border border-gray-200 rounded-lg p-4">
                <MarkerForm
                  :marker="marker"
                  :index="index"
                  @update="updateMarker"
                  @remove="removeMarker"
                />
              </div>
            </div>
          </div>

          <!-- Multi-City Management -->
          <div v-if="mapData.type === 'multi_city'" class="bg-white p-4 rounded-lg shadow mb-6">
            <div class="flex justify-between items-center mb-4">
              <h3 class="text-lg font-medium text-gray-900">Cities</h3>
              <button @click="addCity" class="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                Add City
              </button>
            </div>
            <div class="space-y-4">
              <div v-for="(city, index) in cities" :key="city.id || index" 
                   class="border border-gray-200 rounded-lg p-4">
                <CityForm
                  :city="city"
                  :index="index"
                  @update="updateCity"
                  @remove="removeCity"
                  @markers-update="updateCityMarkers"
                />
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex justify-end space-x-3">
            <button @click="saveMap" :disabled="isSaving" 
                    class="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 disabled:opacity-50">
              {{ isSaving ? 'Saving...' : 'Save Map' }}
            </button>
            <button @click="previewMap" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-primary-700">
              Preview
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useForm } from '@inertiajs/inertia-vue3'
import ProjectMapPreview from './ProjectMapPreview.vue'
import MarkerForm from './MarkerForm.vue'
import CityForm from './CityForm.vue'

const props = defineProps({
  project: {
    type: Object,
    required: true
  },
  existingMap: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['map-saved', 'map-updated'])

const isSaving = ref(false)

const mapData = reactive({
  type: 'single',
  title: '',
  center_coordinates: [36.1911, 44.0094], // Default to Erbil
  zoom_level: 10,
  bounds: null,
  is_active: true
})

const cities = ref([])
const markers = ref([])

// Initialize data if editing existing map
onMounted(() => {
  if (props.existingMap) {
    Object.assign(mapData, props.existingMap)
    if (props.existingMap.cities) {
      cities.value = [...props.existingMap.cities]
    }
    if (props.existingMap.markers) {
      markers.value = [...props.existingMap.markers]
    }
  }
})

const onMapTypeChange = () => {
  // Clear data when switching map types
  if (mapData.type === 'single') {
    cities.value = []
  } else {
    markers.value = []
  }
}

const addMarker = () => {
  markers.value.push({
    title: { en: '', ar: '', ku: '' },
    description: { en: '', ar: '', ku: '' },
    coordinates: { lat: mapData.center_coordinates[0], lng: mapData.center_coordinates[1] },
    icon: 'default',
    color: '#008CD3',
    is_active: true,
    sort_order: markers.value.length
  })
}

const updateMarker = (index, markerData) => {
  markers.value[index] = { ...markers.value[index], ...markerData }
}

const removeMarker = (index) => {
  markers.value.splice(index, 1)
}

const addCity = () => {
  cities.value.push({
    name: { en: '', ar: '', ku: '' },
    description: { en: '', ar: '', ku: '' },
    center_coordinates: [...mapData.center_coordinates],
    zoom_level: 12,
    bounds: null,
    is_active: true,
    sort_order: cities.value.length,
    markers: []
  })
}

const updateCity = (index, cityData) => {
  cities.value[index] = { ...cities.value[index], ...cityData }
}

const removeCity = (index) => {
  cities.value.splice(index, 1)
}

const updateCityMarkers = (cityIndex, markersData) => {
  cities.value[cityIndex].markers = markersData
}

const onMarkerClick = (marker) => {
  console.log('Marker clicked:', marker)
}

const onMapBoundsChange = (bounds) => {
  mapData.bounds = bounds
}

const saveMap = async () => {
  isSaving.value = true
  
  try {
    const formData = {
      project_id: props.project.id,
      type: mapData.type,
      title: mapData.title,
      center_coordinates: mapData.center_coordinates,
      zoom_level: mapData.zoom_level,
      bounds: mapData.bounds,
      is_active: mapData.is_active,
      cities: mapData.type === 'multi_city' ? cities.value : [],
      markers: mapData.type === 'single' ? markers.value : []
    }

    const response = await fetch(route('admin.project-maps.store'), {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
      },
      body: JSON.stringify(formData)
    })

    const result = await response.json()
    
    if (response.ok) {
      emit('map-saved', result.data)
      // Show success message
    } else {
      // Handle validation errors
      console.error('Validation errors:', result.errors)
    }
  } catch (error) {
    console.error('Error saving map:', error)
  } finally {
    isSaving.value = false
  }
}

const previewMap = () => {
  // Open map preview in new window/modal
  console.log('Preview map:', { mapData, cities: cities.value, markers: markers.value })
}
</script>
