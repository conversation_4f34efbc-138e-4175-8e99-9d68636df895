<script setup>
import { Link } from '@inertiajs/inertia-vue3';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/vue/20/solid'
defineProps(['links', 'pages', 'from', 'to', 'total', 'currentPage'])
</script>

<template>
    <div class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
        <div class="flex flex-1 justify-between sm:hidden">
            <a href="#"
                class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Previous</a>
            <a href="#"
                class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">Next</a>
        </div>
        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700">
                    Showing
                    {{ ' ' }}
                    <span class="font-medium">{{ from }}</span>
                    {{ ' ' }}
                    to
                    {{ ' ' }}
                    <span class="font-medium">{{ to }}</span>
                    {{ ' ' }}
                    of
                    {{ ' ' }}
                    <span class="font-medium">{{ total }}</span>
                    {{ ' ' }}
                    results
                </p>
            </div>
            <div>
                <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                    <template v-for="page in pages">
                        <template v-if="/Previous/ig.test(page.label)">
                            <component :is="page.url ? Link : 'button'" :disabled="!page.url" :href="links.prev"
                                class="relative inline-flex items-center rounded-l-md border disabled:cursor-not-allowed border-gray-300 bg-white px-2 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-20">
                            <span class="sr-only">Previous</span>
                            <ChevronLeftIcon class="h-5 w-5" aria-hidden="true" />
                            </component>
                        </template>
                        <template v-if="page.url && !/Previous|Next/ig.test(page.label)">
                            <Link :href="page.url" aria-current="page"
                                :class="[page.active ? 'relative z-10 inline-flex items-center border border-primary-500 bg-gray-50 px-4 py-2 text-sm font-medium text-primary-600 focus:z-20' : 'relative inline-flex items-center border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-20']">
                            {{ page.label }}</Link>
                        </template>
                        <template v-if="/Next/ig.test(page.label)">
                            <component :is="page.url ? Link : 'button'" :disabled="!page.url" :href="links.next"
                                class="relative inline-flex items-center rounded-r-md border disabled:cursor-not-allowed border-gray-300 bg-white px-2 py-2 text-sm font-medium text-gray-500 hover:bg-gray-50 focus:z-20">
                            <span class="sr-only">Next</span>
                            <ChevronRightIcon class="h-5 w-5" aria-hidden="true" />
                            </component>
                        </template>
                    </template>
                </nav>
            </div>
        </div>
    </div>
</template>