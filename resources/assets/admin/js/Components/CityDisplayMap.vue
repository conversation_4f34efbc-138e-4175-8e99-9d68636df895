<template>
  <div class="city-display-map">
    <div ref="mapContainer" class="w-full rounded-lg border border-gray-300" :style="{ height: height }"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'

// Fix for default markers in Leaflet with webpack
delete L.Icon.Default.prototype._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
})

const props = defineProps({
  center: {
    type: Array,
    default: () => [36.1911, 44.0094] // Default to Erbil coordinates
  },
  zoom: {
    type: Number,
    default: 12
  },
  bounds: {
    type: Object,
    default: null
  },
  showBoundary: {
    type: Boolean,
    default: true
  },
  showCenter: {
    type: Boolean,
    default: false
  },
  height: {
    type: String,
    default: '24rem' // 96 = h-96
  }
})

const mapContainer = ref(null)
let map = null
let boundaryLayer = null
let centerMarker = null

onMounted(() => {
  initializeMap()
})

watch(() => props.center, (newCenter) => {
  if (map && newCenter) {
    map.setView(newCenter, props.zoom)
    updateCenterMarker()
  }
}, { deep: true })

watch(() => props.bounds, (newBounds) => {
  updateBoundary()
}, { deep: true })

watch(() => props.zoom, (newZoom) => {
  if (map) {
    map.setZoom(newZoom)
  }
})

const initializeMap = () => {
  if (!mapContainer.value) return

  // Set the height of the map container
  mapContainer.value.style.height = props.height

  // Initialize the map
  map = L.map(mapContainer.value, {
    zoomControl: true,
    scrollWheelZoom: true,
    dragging: true,
    touchZoom: true,
    doubleClickZoom: true,
    boxZoom: true,
    keyboard: true
  }).setView(props.center, props.zoom)

  // Add OpenStreetMap tile layer
  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
    attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
    maxZoom: 19
  }).addTo(map)

  // Initialize boundary and center marker
  updateBoundary()
  updateCenterMarker()
}

const updateBoundary = () => {
  if (!map) return

  // Remove existing boundary layer
  if (boundaryLayer) {
    map.removeLayer(boundaryLayer)
    boundaryLayer = null
  }

  // Add boundary if it exists and should be shown
  if (props.bounds && props.showBoundary) {
    let coordinates = []
    
    // Handle different bounds formats
    if (Array.isArray(props.bounds)) {
      // Direct array of coordinates
      coordinates = props.bounds
    } else if (props.bounds.coordinates && Array.isArray(props.bounds.coordinates)) {
      // Bounds object with coordinates property
      coordinates = props.bounds.coordinates
    }
    
    if (coordinates.length > 0) {
      // Convert coordinates to Leaflet format
      const latLngs = coordinates.map(coord => {
        // Handle different coordinate formats
        if (coord.lat !== undefined && coord.lng !== undefined) {
          return [coord.lat, coord.lng]
        } else if (Array.isArray(coord) && coord.length >= 2) {
          return [coord[0], coord[1]]
        }
        return null
      }).filter(coord => coord !== null)
      
      if (latLngs.length > 0) {
        // Create polygon
        boundaryLayer = L.polygon(latLngs, {
          color: '#008CD3',
          weight: 3,
          fillColor: '#008CD3',
          fillOpacity: 0.1,
          interactive: false
        }).addTo(map)

        // Add boundary points as small circles
        coordinates.forEach((coord, index) => {
          let lat, lng
          if (coord.lat !== undefined && coord.lng !== undefined) {
            lat = coord.lat
            lng = coord.lng
          } else if (Array.isArray(coord) && coord.length >= 2) {
            lat = coord[0]
            lng = coord[1]
          }
          
          if (lat !== undefined && lng !== undefined) {
            L.circleMarker([lat, lng], {
              radius: 4,
              color: '#1F2937',
              weight: 2,
              fillColor: '#008CD3',
              fillOpacity: 0.8,
              interactive: false
            }).addTo(map).bindTooltip(`Point ${index + 1}`, { 
              permanent: false, 
              direction: 'top',
              offset: [0, -10]
            })
          }
        })

        // Fit map to boundary
        try {
          map.fitBounds(boundaryLayer.getBounds(), { padding: [20, 20] })
        } catch (error) {
          console.warn('Could not fit bounds:', error)
        }
      }
    }
  }
}

const updateCenterMarker = () => {
  if (!map) return

  // Remove existing center marker
  if (centerMarker) {
    map.removeLayer(centerMarker)
    centerMarker = null
  }

  // Add center marker if should be shown
  if (props.showCenter && props.center) {
    centerMarker = L.marker(props.center, {
      interactive: false
    }).addTo(map)
  }
}

// Cleanup on unmount
const cleanup = () => {
  if (map) {
    map.remove()
    map = null
  }
}

// Make sure to cleanup when component is unmounted
import { onBeforeUnmount } from 'vue'
onBeforeUnmount(cleanup)
</script>

<style scoped>
.city-display-map {
  position: relative;
}

/* Ensure leaflet controls are visible */
:deep(.leaflet-control-zoom) {
  border: none !important;
}

:deep(.leaflet-control-zoom a) {
  background-color: white !important;
  color: #374151 !important;
  border: 1px solid #d1d5db !important;
}

:deep(.leaflet-control-zoom a:hover) {
  background-color: #f3f4f6 !important;
}
</style>
