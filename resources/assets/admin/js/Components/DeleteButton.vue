<script setup>
    import LoadingIndicatior from "@/Components/LoadingIndicatior.vue";
defineProps({
    loading: {
        type: Boolean,
        default: false,
    },
})
</script>
<template>

    <button type="button" :disabled="loading"
        class="inline-flex w-full justify-center rounded-md border border-transparent bg-red-600 disabled:hover:cursor-not-allowed disabled:bg-red-300 disabled:hover:bg-red-300 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm">
        <LoadingIndicatior v-if="loading"/>
        <span v-text="loading ? 'Deleting...' : 'Delete'"></span>
    </button>

</template>