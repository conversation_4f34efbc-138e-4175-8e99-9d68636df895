<script setup>
import { ref, computed, onMounted } from 'vue';
import Label from '@/Components/Label.vue';
import FormError from '@/Components/FormError.vue';

const props = defineProps({
  existingIcon: {
    type: Object,
    default: null
  },
  error: {
    type: String,
    default: null
  },
  label: {
    type: String,
    default: "Project Icon (Optional)"
  }
});

const emit = defineEmits(['iconUpdated']);

const iconFile = ref(null);
const iconPreview = ref(null);
const fileInput = ref(null);
const dragActive = ref(false);
const hasChanged = ref(false);

// Initialize with existing icon if available
onMounted(() => {
  if (props.existingIcon) {
    iconPreview.value = props.existingIcon.original_url;
  }
});

// Computed properties
const hasError = computed(() => !!props.error);
const hasIcon = computed(() => !!iconPreview.value);

// Handle file selection
function handleFileSelect(event) {
  if (event.target.files && event.target.files.length > 0) {
    processFile(event.target.files[0]);
  }
}

// Handle drag and drop
function handleDrag(event) {
  event.preventDefault();
  event.stopPropagation();
  if (event.type === 'dragenter' || event.type === 'dragover') {
    dragActive.value = true;
  } else if (event.type === 'dragleave') {
    dragActive.value = false;
  }
}

function handleDrop(event) {
  event.preventDefault();
  event.stopPropagation();
  dragActive.value = false;
  
  if (event.dataTransfer.files.length > 0) {
    processFile(event.dataTransfer.files[0]);
  }
}

// Process the selected file
function processFile(file) {
  const allowedTypes = ['image/png', 'image/webp', 'image/svg+xml'];
  const maxFileSize = 5 * 1024 * 1024; // 5MB (as per your validation rules)
  
  // Check file type
  if (!allowedTypes.includes(file.type)) {
    alert('Only PNG, WebP, and SVG files are allowed');
    return;
  }
  
  // Check file size
  if (file.size > maxFileSize) {
    alert('File size must be less than 5MB');
    return;
  }
  
  // Update icon file and preview
  iconFile.value = file;
  iconPreview.value = URL.createObjectURL(file);
  hasChanged.value = true;
  
  // Emit event to parent component
  emit('iconUpdated', { file, hasChanged: true });
}

// Remove the current icon
function removeIcon() {
  iconFile.value = null;
  iconPreview.value = null;
  hasChanged.value = true;
  fileInput.value.value = null; // Reset input to allow selecting the same file again
  
  // Emit event to parent component
  emit('iconUpdated', { file: null, hasChanged: true });
}

// Expose methods to parent component
defineExpose({
  getFile: () => iconFile.value,
  hasChanged: () => hasChanged.value,
  reset: () => {
    iconFile.value = null;
    iconPreview.value = props.existingIcon ? props.existingIcon.original_url : null;
    hasChanged.value = false;
    fileInput.value.value = null;
  }
});
</script>

<template>
  <div class="icon-uploader">
    <Label>{{ label }}</Label>
    <p class="text-sm text-gray-500 mb-2">Upload a PNG, WebP, or SVG icon for the project</p>
    
    <!-- Uploader Area -->
    <div 
      class="mt-2 bg-white border-2 border-dashed rounded-lg p-4 text-center relative"
      :class="{
        'border-primary-400 bg-primary-50': dragActive,
        'border-gray-300': !dragActive && !hasError,
        'border-red-500': hasError
      }"
      @dragenter="handleDrag"
      @dragover="handleDrag"
      @dragleave="handleDrag"
      @drop="handleDrop"
    >
      <input
        type="file"
        ref="fileInput"
        class="hidden"
        accept="image/png,image/webp,image/svg+xml"
        @change="handleFileSelect"
      />
      
      <!-- Empty state -->
      <div v-if="!hasIcon" class="py-6">
        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
          <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
        <p class="mt-1 text-sm text-gray-600">
          Drag & Drop an icon here, or 
          <button 
            type="button" 
            class="text-primary-600 hover:text-primary-700 font-medium"
            @click="fileInput.click()"
          >
            browse
          </button>
        </p>
        <p class="text-xs text-gray-500 mt-1">PNG, WebP, SVG up to 5MB</p>
      </div>
      
      <!-- Icon Preview -->
      <div v-else class="flex flex-col items-center justify-center">
        <div class="relative w-32 h-32 overflow-hidden border border-gray-200 rounded-md bg-gray-50 p-2 group">
          <img :src="iconPreview" class="w-full h-full object-contain" alt="Project icon" />
          <div class="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <button 
              type="button"
              @click="removeIcon"
              class="p-1.5 bg-red-500 hover:bg-red-600 rounded-full text-white"
            >
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4">
                <path fill-rule="evenodd" d="M8.75 1A2.75 2.75 0 006 3.75v.443c-.795.077-1.584.176-2.365.298a.75.75 0 10.23 1.482l.149-.022.841 10.518A2.75 2.75 0 007.596 19h4.807a2.75 2.75 0 002.742-2.53l.841-10.52.149.023a.75.75 0 00.23-1.482A41.03 41.03 0 0014 4.193V3.75A2.75 2.75 0 0011.25 1h-2.5zM10 4c.84 0 1.673.025 2.5.075V3.75c0-.69-.56-1.25-1.25-1.25h-2.5c-.69 0-1.25.56-1.25 1.25v.325C8.327 4.025 9.16 4 10 4zM8.58 7.72a.75.75 0 00-1.5.06l.3 7.5a.75.75 0 101.5-.06l-.3-7.5zm4.34.06a.75.75 0 10-1.5-.06l-.3 7.5a.75.75 0 101.5.06l.3-7.5z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
        
        <p class="mt-2 text-xs text-gray-500">{{ iconFile ? iconFile.name : 'Current icon' }}</p>
        
        <button 
          type="button" 
          class="mt-2 text-xs text-primary-600 hover:text-primary-700 font-medium"
          @click="fileInput.click()"
        >
          Replace
        </button>
      </div>
    </div>
    
    <!-- Error message -->
    <FormError class="mt-1" v-if="error">{{ error }}</FormError>
  </div>
</template>

<style scoped>
.icon-uploader {
  margin-bottom: 1rem;
}
</style>
