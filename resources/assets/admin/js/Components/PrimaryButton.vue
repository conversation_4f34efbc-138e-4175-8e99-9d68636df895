<script setup>
import LoadingIndicatior from "@/Components/LoadingIndicatior.vue";
defineProps({
    loading: {
        type: Boolean,
        default: false,
    },
});
</script>
<template>
    <button type="button" class="inline-flex items-center rounded-md border border-transparent bg-primary disabled:bg-gray-200 disabled:text-gray-400 disabled:hover:bg-gray-200 disabled:hover:cursor-not-allowed px-3 py-2 text-sm font-medium leading-4 text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
        <LoadingIndicatior class="text-primary-500" v-if="loading"/>
        <slot/>
    </button>
</template>