<template>
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <h4 class="text-lg font-medium text-gray-900">Select Cities</h4>
      <div class="flex items-center space-x-2">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Search cities..."
          class="block w-64 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        />
        <Link
          :href="route('admin.cities.create')"
          target="_blank"
          class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
        >
          <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          New City
        </Link>
      </div>
    </div>

    <!-- Available Cities -->
    <div class="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
      <h5 class="text-sm font-medium text-gray-700 mb-3">Available Cities</h5>
      
      <div v-if="filteredAvailableCities.length === 0" class="text-center py-4 text-gray-500">
        <p v-if="searchQuery">No cities found matching "{{ searchQuery }}"</p>
        <p v-else>No cities available. <Link :href="route('admin.cities.create')" target="_blank" class="text-indigo-600 hover:text-indigo-800">Create a new city</Link></p>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-3">
        <div
          v-for="city in filteredAvailableCities"
          :key="city.id"
          @click="selectCity(city)"
          class="p-3 bg-white rounded-lg border border-gray-200 hover:border-indigo-300 cursor-pointer transition-colors"
          :class="{ 'ring-2 ring-indigo-500': isPreselected(city.id) }"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h6 class="font-medium text-gray-900">{{ city.name }}</h6>
              <div class="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                <span>Zoom: {{ city.zoom_level }}</span>
                <span :class="city.is_active ? 'text-green-600' : 'text-red-600'">
                  {{ city.is_active ? 'Active' : 'Inactive' }}
                </span>
              </div>
            </div>
            <div class="ml-2">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div v-if="selectedCities.length === 0" class="text-center py-4 text-gray-500 text-sm">
      No cities selected. Click on cities above to add them to your project map.
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Link } from '@inertiajs/inertia-vue3'

const props = defineProps({
  availableCities: {
    type: Array,
    default: () => []
  },
  preselectedCities: {
    type: Array,
    default: () => []
  },
  modelValue: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['cities-updated', 'cities-loaded', 'update:modelValue'])

const searchQuery = ref('')
const selectedCities = ref([])

console.log({ availableCities: props.availableCities})

// Initialize selected cities from modelValue or preselectedCities
const initializeSelectedCities = () => {
  if (props.modelValue && props.modelValue.length > 0) {
    // Find cities by IDs from modelValue
    const citiesFromModelValue = props.availableCities.filter(city => 
      props.modelValue.includes(city.id)
    )
    selectedCities.value = citiesFromModelValue
  } else if (props.preselectedCities && props.preselectedCities.length > 0) {
    selectedCities.value = [...props.preselectedCities]
  }
}

// Initialize when component mounts
initializeSelectedCities()

// Computed
const filteredAvailableCities = computed(() => {
  let cities = props.availableCities.filter(city => 
    !selectedCities.value.some(selected => selected.id === city.id)
  )

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    cities = cities.filter(city => 
      (city.name.en && city.name.en.toLowerCase().includes(query)) ||
      (city.name.ar && city.name.ar.toLowerCase().includes(query)) ||
      (city.name.ku && city.name.ku.toLowerCase().includes(query)) ||
      (city.description?.en && city.description.en.toLowerCase().includes(query))
    )
  }

  return cities
})

// Methods
const selectCity = (city) => {
  if (!selectedCities.value.some(selected => selected.id === city.id)) {
    selectedCities.value.push(city)
    emitUpdate()
  }
}

const removeCity = (index) => {
  selectedCities.value.splice(index, 1)
  emitUpdate()
}

const moveUp = (index) => {
  if (index > 0) {
    const item = selectedCities.value.splice(index, 1)[0]
    selectedCities.value.splice(index - 1, 0, item)
    emitUpdate()
  }
}

const moveDown = (index) => {
  if (index < selectedCities.value.length - 1) {
    const item = selectedCities.value.splice(index, 1)[0]
    selectedCities.value.splice(index + 1, 0, item)
    emitUpdate()
  }
}

const isPreselected = (cityId) => {
  return props.preselectedCities.some(city => city.id === cityId)
}

const emitUpdate = () => {
  const cityIds = selectedCities.value.map(city => city.id)
  emit('cities-updated', cityIds)
  emit('update:modelValue', cityIds)
  emit('cities-loaded', selectedCities.value)
}

// Watch for external changes
watch(() => props.preselectedCities, (newCities) => {
  selectedCities.value = [...newCities]
}, { deep: true })

// Watch for modelValue changes
watch(() => props.modelValue, (newIds) => {
  if (newIds && newIds.length > 0) {
    const citiesFromIds = props.availableCities.filter(city => 
      newIds.includes(city.id)
    )
    selectedCities.value = citiesFromIds
  } else {
    selectedCities.value = []
  }
}, { deep: true })

// Watch for availableCities changes to update selected cities
watch(() => props.availableCities, () => {
  initializeSelectedCities()
}, { deep: true })
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-clamp: 2; /* Standard property */
}
</style>
