<script setup>
import { computed } from 'vue';
import { Link, usePage } from '@inertiajs/inertia-vue3';

const props = defineProps(['href', 'active']);
const page = usePage();

const adminHref = computed(() => `/admin${props.href}`)

const classes = computed(() => {
    return [(props.active || page.url.value == adminHref.value) ? 
    'bg-primary-950 text-white' : 'text-primary-800 hover:bg-primary-950 hover:text-white', 'group flex items-center px-2 py-2 text-sm font-medium rounded-md']
});
</script>

<template>
    <Link :href="adminHref" :class="classes">
        <slot />
    </Link>
</template>
