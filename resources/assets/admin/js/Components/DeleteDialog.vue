<script setup>
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { ExclamationTriangleIcon, XMarkIcon } from '@heroicons/vue/24/outline'
import { Inertia } from '@inertiajs/inertia';
import { ref } from 'vue';
import SecondaryButton from './SecondaryButton.vue';
import DeleteButton from './DeleteButton.vue';
import route from 'ziggy';

const props = defineProps({
    rowName: {
        type: String,
        default: null,
    },
    rowId: {
        type: [String, Number],
        default: null,
    },
    open: {
        type: Boolean,
        default: true,
    },
    preserveState: {
        type: Boolean,
        default: false,
    },
    route: {
        type: String,
        default: null,
    }
})

const emit = defineEmits(['close']);
const cancelButtonRef = ref(null);

const deleting = ref(false);

const onDelete = function () {
    if(!props.route) {
        throw Error('Route or Endpoint is not defined');
    }

    Inertia.delete(route(props.route, props.rowId), {
        preserveState: props.preserveState,
        preserveScroll: true,
        onSuccess: close,
        onFinish: close,
        onError: (e) => console.log(e),
        onBefore: () => deleting.value = true,
    });
}

const close = () => {
    deleting.value = false;
    emit('close');
}
</script>
<template>
    <TransitionRoot :initialFocus="cancelButtonRef" as="template" :show="open">
        <Dialog as="div" class="relative z-10" @close="close">
            <TransitionChild as="template" enter="ease-out duration-300" enter-from="opacity-0" enter-to="opacity-100"
                leave="ease-in duration-200" leave-from="opacity-100" leave-to="opacity-0">
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
            </TransitionChild>

            <div class="fixed inset-0 z-10 overflow-y-auto">
                <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                    <TransitionChild as="template" enter="ease-out duration-300"
                        enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                        enter-to="opacity-100 translate-y-0 sm:scale-100" leave="ease-in duration-200"
                        leave-from="opacity-100 translate-y-0 sm:scale-100"
                        leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
                        <DialogPanel
                            class="relative transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                            <div class="absolute top-0 right-0 hidden pt-4 pr-4 sm:block">
                                <button type="button"
                                    class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                                    @click="close">
                                    <span class="sr-only">Close</span>
                                    <XMarkIcon class="h-6 w-6" aria-hidden="true" />
                                </button>
                            </div>
                            <div class="sm:flex sm:items-start">
                                <div
                                    class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                    <ExclamationTriangleIcon class="h-6 w-6 text-red-600" aria-hidden="true" />
                                </div>
                                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                                    <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">Delete row
                                    </DialogTitle>
                                    <div class="mt-2">
                                        <p class="text-sm text-gray-500">Are you sure you want to delete id <span
                                                class="font-bold text-red-600">#{{ rowId }}</span> of <span
                                                class="font-bold text-gray-800">{{ rowName }}</span>?</p>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-5 sm:mt-8 sm:flex sm:flex-row-reverse">
                                <DeleteButton :loading="deleting" @click="onDelete">Delete</DeleteButton>
                                <SecondaryButton type="button" @click="close" ref="cancelButtonRef">Cancel
                                </SecondaryButton>
                            </div>
                        </DialogPanel>
                    </TransitionChild>
                </div>
            </div>
        </Dialog>
    </TransitionRoot>
</template>