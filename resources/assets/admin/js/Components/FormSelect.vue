<script setup>
import { onMounted, ref } from 'vue';
import Label from '@/Components/Label.vue';
import FormError from '@/Components/FormError.vue';
import { v4 as uuid } from 'uuid';

defineProps({
    id: {
        type: String,
        default: () => `text-input-${uuid()}`,
    },
    modelValue: {
        type: [String, Number],
        default: null,
    },
    error: {
        type: String,
        default: null,
    },

    label: {
        type: String,
    },
    placeholder: {
        type: String,
        default: '',
    },
    options: {
        type: Array,
        default: []
    },
});

const emit = defineEmits(['update:modelValue']);

const select = ref(null);

onMounted(() => {
    if (select.value.hasAttribute('autofocus')) {
        select.value.focus();
    }
});

const onChange = (e) => {
    emit('update:modelValue', e.target.value);
}
</script>
<template>
    <div :class="$attrs.class">
        <Label :for="id">{{ label }}</Label>
        <div class="mt-1">
            <select :id="id" :value="modelValue" @input="onChange" ref="select" v-bind="{ ...$attrs, class: null }"
                :autocomplete="id"
                :class="[error ? 'border-red-300 placeholder-red-300 focus:border-red-500 focus:ring-red-500': 'border-gray-300 focus:border-primary-500 focus:outline-none focus:ring-primary-500', 'block w-full rounded-md border bg-white py-2 px-3 shadow-sm  sm:text-sm', 'w-full']">
                <option v-if="placeholder" value="">{{ placeholder }}</option>
                <option v-for="option in options" :value="option.value || option.id" :key="option.value || option.id">{{ option.label }}</option>
            </select>
        </div>
        <FormError class="mt-1" v-if="error">{{ error }}</FormError>
    </div>
</template>