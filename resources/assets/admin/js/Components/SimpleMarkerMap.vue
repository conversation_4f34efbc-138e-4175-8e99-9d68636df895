<template>
  <div class="relative w-full h-full">
    <div ref="mapContainer" class="w-full h-full"></div>
    <div v-if="isLoading" class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 z-50">
      <div class="text-center">
        <svg class="animate-spin h-10 w-10 mx-auto mb-2 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <p class="text-sm font-medium text-gray-700">{{ loadingMessage }}</p>
      </div>
    </div>
    
    <!-- Controls -->
    <div class="absolute top-4 right-4 bg-white rounded-lg shadow-lg p-3 z-40" style="z-index: 99999;">
      <div class="space-y-2">
        <button 
          @click="clearAllMarkers" 
          class="w-full px-3 py-2 bg-red-500 text-white text-sm rounded hover:bg-red-600 transition-colors"
        >
          Clear All
        </button>
        <div class="text-xs text-gray-600">
          Click to add markers<br>
          Drag to move markers<br>
          Right-click to delete
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { nanoid } from 'nanoid';

const props = defineProps({
  center: {
    type: Array,
    default: () => [36.1919, 44.0098], // Erbil coordinates
  },
  zoom: {
    type: Number,
    default: 10,
  },
  bounds: {
    type: Array,
    default: () => [],
  },
  readonly: {
    type: Boolean,
    default: false,
  },
});

console.log({ bounds: props.bounds })

const emit = defineEmits(['center-changed', 'zoom-changed', 'bounds-changed']);

// Reactive state
const mapContainer = ref(null);
const isLoading = ref(false);
const loadingMessage = ref('Loading map...');
const map = ref(null);
const polygon = ref(null); // Add polygon reference
const markers = ref([]); // Array of {id, latlng, marker} objects
const isDragging = ref(false);
const isUpdatingFromProps = ref(false);
const isUserInteraction = ref(false); // Track if changes are from user interaction
const lastDragEndTime = ref(0); // Track when drag operations end

// Expose API
defineExpose({
  getMap: () => map.value,
  addMarker: (latlng) => addMarker(latlng),
  clearMarkers: () => clearAllMarkers(),
  getMarkers: () => markers.value.map(m => m.latlng)
});

onMounted(async () => {
  await nextTick();
  showLoading('Initializing map...');
  try {
    initMap();
  } finally {
    hideLoading();
  }
});

onUnmounted(() => {
  cleanup();
});

// Helper functions
function showLoading(message = 'Loading...') {
  loadingMessage.value = message;
  isLoading.value = true;
}

function hideLoading() {
  isLoading.value = false;
}

function cleanup() {
  if (map.value) {
    // Clean up polygon
    if (polygon.value) {
      map.value.removeLayer(polygon.value);
      polygon.value = null;
    }
    
    // Clean up all markers
    markers.value.forEach(({ marker }) => {
      if (marker) {
        marker.off();
        map.value.removeLayer(marker);
      }
    });
    
    // Remove map
    map.value.remove();
    map.value = null;
  }
  
  markers.value = [];
  isDragging.value = false;
  isUpdatingFromProps.value = false;
}

function initMap() {
  if (!mapContainer.value) return;

  try {
    // Initialize map
    map.value = L.map(mapContainer.value, {
      center: props.center,
      zoom: props.zoom,
      zoomControl: true,
      preferCanvas: true,
      fadeAnimation: false,
      markerZoomAnimation: false
    });

    // Add tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors',
      maxZoom: 19,
    }).addTo(map.value);

    // Set up event listeners
    if (!props.readonly) {
      map.value.on('click', onMapClick);
    }

    map.value.on('moveend', () => {
      if (!isUpdatingFromProps.value) {
        const center = map.value.getCenter();
        emit('center-changed', [center.lat, center.lng]);
      }
    });

    map.value.on('zoomend', () => {
      if (!isUpdatingFromProps.value) {
        emit('zoom-changed', map.value.getZoom());
      }
    });

    // The bounds watcher will handle initial loading with immediate: true
  } catch (error) {
    console.error('Error initializing map:', error);
    alert('There was an error initializing the map. Please refresh the page and try again.');
  }
}

function onMapClick(e) {
  // Prevent adding markers during drag operations or shortly after drag ends
  const timeSinceLastDrag = Date.now() - lastDragEndTime.value;
  
  if (!props.readonly && !isDragging.value && timeSinceLastDrag > 200) {
    addMarker(e.latlng); // Use normal addMarker (don't skip polygon update)
  }
}

function addMarker(latlng, skipPolygonUpdate = false) {
  if (!map.value) return null;

  const markerId = nanoid();
  
  // Create marker
  const marker = L.circleMarker(latlng, {
    radius: 8,
    fillColor: '#007cba',
    color: '#ffffff',
    weight: 2,
    opacity: 1,
    fillOpacity: 0.8,
  }).addTo(map.value);

  // Store marker data
  const markerData = {
    id: markerId,
    latlng: latlng,
    marker: marker
  };

  markers.value.push(markerData);

  // Set up marker interactions
  if (!props.readonly) {
    setupMarkerEvents(marker, markerId);
  }

  // Update polygon display only if not skipped (for batch operations)
  if (!skipPolygonUpdate) {
    updatePolygonDisplay();
    // Mark as user interaction to prevent auto-zoom
    isUserInteraction.value = true;
    // Emit bounds change
    emitBoundsChange();
    // Reset user interaction flag after a short delay
    setTimeout(() => {
      isUserInteraction.value = false;
    }, 100);
  }

  return markerData;
}

function setupMarkerEvents(marker, markerId) {
  let dragStarted = false;

  // Mouse down - prepare for drag
  marker.on('mousedown', (e) => {
    L.DomEvent.stopPropagation(e);
    L.DomEvent.preventDefault(e);
    
    dragStarted = false;
    isDragging.value = true;
    map.value.dragging.disable();
    
    // Change cursor
    map.value.getContainer().style.cursor = 'grabbing';
    
    // Add temporary event listeners for drag
    const onMouseMove = (e) => {
      dragStarted = true; // Mark that we actually started dragging
      marker.setLatLng(e.latlng);
      
      // Update stored position
      const markerData = markers.value.find(m => m.id === markerId);
      if (markerData) {
        markerData.latlng = e.latlng;
        // Update polygon in real-time during drag
        updatePolygonDisplay();
      }
    };

    const onMouseUp = () => {
      isDragging.value = false;
      map.value.dragging.enable();
      map.value.getContainer().style.cursor = '';
      
      // Record when drag operation ended
      lastDragEndTime.value = Date.now();
      
      // Remove temporary listeners
      map.value.off('mousemove', onMouseMove);
      map.value.off('mouseup', onMouseUp);
      document.removeEventListener('mouseup', onMouseUp);
      
      // Only emit bounds change if we actually dragged
      if (dragStarted) {
        // Mark as user interaction to prevent auto-zoom
        isUserInteraction.value = true;
        emitBoundsChange();
        // Reset user interaction flag after a short delay
        setTimeout(() => {
          isUserInteraction.value = false;
        }, 100);
      }
      
      dragStarted = false;
    };

    // Add the event listeners
    map.value.on('mousemove', onMouseMove);
    map.value.on('mouseup', onMouseUp);
    document.addEventListener('mouseup', onMouseUp);
  });

  // Prevent click events on markers from bubbling to map
  marker.on('click', (e) => {
    L.DomEvent.stopPropagation(e);
    L.DomEvent.preventDefault(e);
  });

  // Right click to delete
  marker.on('contextmenu', (e) => {
    L.DomEvent.stopPropagation(e);
    L.DomEvent.preventDefault(e);
    removeMarker(markerId);
  });

  // Visual feedback on hover
  marker.on('mouseover', () => {
    if (!isDragging.value) {
      marker.setStyle({ radius: 10, fillColor: '#dc3545' });
      map.value.getContainer().style.cursor = 'grab';
    }
  });

  marker.on('mouseout', () => {
    if (!isDragging.value) {
      marker.setStyle({ radius: 8, fillColor: '#007cba' });
      map.value.getContainer().style.cursor = '';
    }
  });
}

function removeMarker(markerId) {
  const index = markers.value.findIndex(m => m.id === markerId);
  if (index !== -1) {
    const markerData = markers.value[index];
    
    // Remove from map
    if (markerData.marker) {
      markerData.marker.off();
      map.value.removeLayer(markerData.marker);
    }
    
    // Remove from array
    markers.value.splice(index, 1);
    
    // Update polygon display
    updatePolygonDisplay();
    
    // Mark as user interaction to prevent auto-zoom
    isUserInteraction.value = true;
    // Emit bounds change
    emitBoundsChange();
    // Reset user interaction flag after a short delay
    setTimeout(() => {
      isUserInteraction.value = false;
    }, 100);
  }
}

function clearAllMarkers(shouldEmit = true) {
  markers.value.forEach(({ marker }) => {
    if (marker) {
      marker.off();
      map.value.removeLayer(marker);
    }
  });
  
  markers.value = [];
  
  // Clear polygon if it exists
  if (polygon.value) {
    map.value.removeLayer(polygon.value);
    polygon.value = null;
  }
  
  // Only emit bounds change if explicitly requested (avoid infinite loops)
  if (shouldEmit) {
    emitBoundsChange();
  }
}

function emitBoundsChange() {
  const bounds = markers.value.map(m => ({
    lat: m.latlng.lat,
    lng: m.latlng.lng
  }));
  emit('bounds-changed', bounds);
}

function updatePolygonDisplay() {
  if (!map.value) return;
  
  // Remove existing polygon if it exists
  if (polygon.value) {
    map.value.removeLayer(polygon.value);
    polygon.value = null;
  }
  
  // Only create polygon/polyline if we have 2 or more markers
  if (markers.value.length >= 2) {
    const latLngs = markers.value.map(m => m.latlng);
    
    // Create polygon (closed path) if we have 3+ markers, polyline if 2 markers
    if (markers.value.length >= 3) {
      polygon.value = L.polygon(latLngs, {
        color: '#007cba',
        weight: 2,
        fillColor: '#007cba',
        fillOpacity: 0.2
      });
    } else {
      polygon.value = L.polyline(latLngs, {
        color: '#007cba',
        weight: 2
      });
    }
    
    polygon.value.addTo(map.value);
  }
}

function updateFromProps(bounds) {
  if (!map.value) {
    return;
  }
  
  if (isUpdatingFromProps.value) {
    return;
  }
  
  isUpdatingFromProps.value = true;
  
  try {
    // Clear existing markers first (don't emit to avoid double emission)
    clearAllMarkers(false);
    
    // Add new markers from bounds (skip individual polygon updates)
    if (bounds && Array.isArray(bounds) && bounds.length > 0) {      
      bounds.forEach((point, index) => {
        let latlng;
        
        console.log(`Processing point ${index}:`, point);
        
        if (Array.isArray(point) && point.length >= 2) {
          latlng = L.latLng(point[0], point[1]);
          console.log(`Created latlng from array:`, latlng);
        } else if (point && typeof point.lat === 'number' && typeof point.lng === 'number') {
          latlng = L.latLng(point.lat, point.lng);
          console.log(`Created latlng from object:`, latlng);
        } else {
          console.log(`Could not parse point:`, point, typeof point);
        }
        
        if (latlng) {
          // Skip polygon update for individual markers
          console.log(`Adding marker at:`, latlng);
          addMarker(latlng, true);
        }
      });
      
      console.log(`Total markers added: ${markers.value.length}`);
      
      // Update polygon display once after adding all markers
      updatePolygonDisplay();
      
      // Emit bounds change once
      emitBoundsChange();
      
      // Only fit bounds if this is NOT from user interaction (i.e., from external props)
      if (!isUserInteraction.value && markers.value.length > 0) {
        const latLngs = markers.value.map(m => m.latlng);
        const boundsLatLng = L.latLngBounds(latLngs);
        map.value.fitBounds(boundsLatLng, { padding: [20, 20] });
      }
    }
  } catch (error) {
    console.error('Error updating from props:', error);
  } finally {
    setTimeout(() => {
      isUpdatingFromProps.value = false;
    }, 100);
  }
}

// Watch for prop changes
watch(() => props.bounds, (newBounds) => {
  if (!isDragging.value && newBounds && newBounds.length > 0) {
    updateFromProps(newBounds);
  } else if (!newBounds || newBounds.length === 0) {
    // Clear markers without emitting to avoid infinite loop
    clearAllMarkers(false);
  }
}, { deep: true, immediate: true });

// Watch for map initialization and process bounds when map becomes available
watch(() => map.value, (newMap) => {
  if (newMap && props.bounds && props.bounds.length > 0) {
    updateFromProps(props.bounds);
  }
});

watch(() => props.center, (newCenter) => {
  if (map.value && newCenter && !isUpdatingFromProps.value) {
    isUpdatingFromProps.value = true;
    map.value.setView(newCenter, map.value.getZoom());
    setTimeout(() => {
      isUpdatingFromProps.value = false;
    }, 100);
  }
}, { deep: true });

watch(() => props.zoom, (newZoom) => {
  if (map.value && newZoom !== map.value.getZoom() && !isUpdatingFromProps.value) {
    map.value.setZoom(newZoom);
  }
});
</script>

<style scoped>
.leaflet-container {
  height: 100%;
  width: 100%;
}
</style>
