<script setup>
import { ref } from 'vue';
import Label from '@/Components/Label.vue';

const props = defineProps({
  image: {
    type: Object,
    required: true
  },
  onUpdate: {
    type: Function,
    required: true
  }
});

const showDescriptions = ref(false);

function toggleDescriptions() {
  showDescriptions.value = !showDescriptions.value;
}

function updateDescription(lang, value) {
  const updatedImage = {
    ...props.image,
    [`description_${lang}`]: value
  };
  props.onUpdate(updatedImage);
}
</script>

<template>
  <div class="image-descriptions">
    <button 
      type="button"
      @click="toggleDescriptions"
      class="text-xs text-gray-600 hover:text-primary-600 flex items-center gap-1"
    >
      <span>{{ showDescriptions ? 'Hide' : 'Show' }} Descriptions</span>
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        class="h-4 w-4" 
        :class="{ 'transform rotate-180': showDescriptions }"
        fill="none" 
        viewBox="0 0 24 24" 
        stroke="currentColor"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </button>

    <div v-if="showDescriptions" class="mt-3 space-y-3">
      <!-- English Description -->
      <div>
        <Label>English Description</Label>
        <textarea 
          :value="image.description_en"
          @input="e => updateDescription('en', e.target.value)"
          rows="2"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          placeholder="Enter English description"
        ></textarea>
      </div>

      <!-- Kurdish Description -->
      <div>
        <Label>Kurdish Description</Label>
        <textarea 
          :value="image.description_ku"
          @input="e => updateDescription('ku', e.target.value)"
          rows="2"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          dir="rtl"
          placeholder="وەسفی کوردی بنووسە"
        ></textarea>
      </div>

      <!-- Arabic Description -->
      <div>
        <Label>Arabic Description</Label>
        <textarea 
          :value="image.description_ar"
          @input="e => updateDescription('ar', e.target.value)"
          rows="2"
          class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
          dir="rtl"
          placeholder="اكتب الوصف بالعربية"
        ></textarea>
      </div>
    </div>
  </div>
</template>
