<template>
  <div class="border border-gray-200 rounded-lg p-4 bg-blue-50">
    <div class="flex justify-between items-start mb-4">
      <h4 class="text-lg font-medium text-gray-900">
        {{ city.name?.en || `City ${index + 1}` }}
      </h4>
      <button @click="$emit('remove', index)" 
              class="text-red-600 hover:text-red-800 text-sm">
        Remove City
      </button>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
      <!-- City Name Fields -->
      <div class="md:col-span-2">
        <label class="block text-sm font-medium text-gray-700 mb-2">City Name</label>
        <div class="space-y-2">
          <input v-model="localCity.name.en" 
                 placeholder="City Name (English)" 
                 class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm" />
          <input v-model="localCity.name.ar" 
                 placeholder="City Name (Arabic)" 
                 class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm" />
          <input v-model="localCity.name.ku" 
                 placeholder="City Name (Kurdish)" 
                 class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm" />
        </div>
      </div>

      <!-- Center Coordinates -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Center Latitude</label>
        <input v-model.number="localCity.center_coordinates[0]" 
               type="number" 
               step="0.000000000000001"
               class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm" />
      </div>
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Center Longitude</label>
        <input v-model.number="localCity.center_coordinates[1]" 
               type="number" 
               step="0.000000000000001"
               class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm" />
      </div>

      <!-- Zoom Level -->
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-2">Zoom Level</label>
        <input v-model.number="localCity.zoom_level" 
               type="number" 
               min="1" 
               max="20"
               class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm" />
      </div>

      <!-- Status and Order -->
      <div class="flex items-center space-x-4">
        <label class="flex items-center">
          <input v-model="localCity.is_active" 
                 type="checkbox" 
                 class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50" />
          <span class="ml-2 text-sm text-gray-700">Active</span>
        </label>
        <div class="flex-1">
          <label class="block text-sm font-medium text-gray-700 mb-1">Sort Order</label>
          <input v-model.number="localCity.sort_order" 
                 type="number" 
                 min="0"
                 class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm" />
        </div>
      </div>

      <!-- Description -->
      <div class="md:col-span-2">
        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
        <div class="space-y-2">
          <textarea v-model="localCity.description.en" 
                    placeholder="Description (English)" 
                    rows="2"
                    class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm"></textarea>
          <textarea v-model="localCity.description.ar" 
                    placeholder="Description (Arabic)" 
                    rows="2"
                    class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm"></textarea>
          <textarea v-model="localCity.description.ku" 
                    placeholder="Description (Kurdish)" 
                    rows="2"
                    class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm"></textarea>
        </div>
      </div>

      <!-- Bounds Configuration -->
      <div class="md:col-span-2">
        <div class="flex justify-between items-center mb-3">
          <label class="block text-sm font-medium text-gray-700">Map Bounds</label>
          <button @click="detectBounds" 
                  type="button"
                  class="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded hover:bg-gray-300">
            Auto-detect from markers
          </button>
        </div>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
          <div>
            <label class="block text-gray-600 mb-1">North</label>
            <input v-model.number="boundsForm.north" 
                   type="number" 
                   step="0.000001"
                   class="w-full border-gray-300 rounded text-xs" />
          </div>
          <div>
            <label class="block text-gray-600 mb-1">South</label>
            <input v-model.number="boundsForm.south" 
                   type="number" 
                   step="0.000001"
                   class="w-full border-gray-300 rounded text-xs" />
          </div>
          <div>
            <label class="block text-gray-600 mb-1">East</label>
            <input v-model.number="boundsForm.east" 
                   type="number" 
                   step="0.000001"
                   class="w-full border-gray-300 rounded text-xs" />
          </div>
          <div>
            <label class="block text-gray-600 mb-1">West</label>
            <input v-model.number="boundsForm.west" 
                   type="number" 
                   step="0.000001"
                   class="w-full border-gray-300 rounded text-xs" />
          </div>
        </div>
      </div>
    </div>

    <!-- City Markers Management -->
    <div class="border-t border-gray-200 pt-4">
      <div class="flex justify-between items-center mb-3">
        <h5 class="text-md font-medium text-gray-800">City Markers</h5>
        <button @click="addMarker" 
                type="button"
                class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700">
          Add Marker
        </button>
      </div>

      <div v-if="localCity.markers && localCity.markers.length > 0" class="space-y-3">
        <div v-for="(marker, markerIndex) in localCity.markers" 
             :key="markerIndex" 
             class="bg-white border border-gray-200 rounded-lg p-3">
          <MarkerForm
            :marker="marker"
            :index="markerIndex"
            @update="updateMarker"
            @remove="removeMarker"
          />
        </div>
      </div>
      <div v-else class="text-center py-4 text-gray-500 text-sm">
        No markers added yet. Click "Add Marker" to get started.
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch, computed } from 'vue'
import MarkerForm from './MarkerForm.vue'

const props = defineProps({
  city: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['update', 'remove', 'markers-update'])

// Create a local reactive copy
const localCity = reactive({
  name: { en: '', ar: '', ku: '', ...props.city.name },
  description: { en: '', ar: '', ku: '', ...props.city.description },
  center_coordinates: props.city.center_coordinates || [36.1911, 44.0094],
  zoom_level: props.city.zoom_level || 12,
  bounds: props.city.bounds || null,
  is_active: props.city.is_active !== undefined ? props.city.is_active : true,
  sort_order: props.city.sort_order || 0,
  markers: props.city.markers || []
})

// Separate bounds form for easier management
const boundsForm = reactive({
  north: props.city.bounds?.north || null,
  south: props.city.bounds?.south || null,
  east: props.city.bounds?.east || null,
  west: props.city.bounds?.west || null
})

// Watch bounds form and update city bounds
watch(boundsForm, (newBounds) => {
  if (newBounds.north && newBounds.south && newBounds.east && newBounds.west) {
    localCity.bounds = { ...newBounds }
  }
}, { deep: true })

// Watch for changes and emit updates
watch(localCity, (newValue) => {
  emit('update', props.index, newValue)
}, { deep: true })

const addMarker = () => {
  const newMarker = {
    title: { en: '', ar: '', ku: '' },
    description: { en: '', ar: '', ku: '' },
    coordinates: { 
      lat: localCity.center_coordinates[0], 
      lng: localCity.center_coordinates[1] 
    },
    icon: 'default',
    color: '#008CD3',
    is_active: true,
    sort_order: localCity.markers.length
  }
  
  localCity.markers.push(newMarker)
  emit('markers-update', props.index, localCity.markers)
}

const updateMarker = (markerIndex, markerData) => {
  localCity.markers[markerIndex] = { ...localCity.markers[markerIndex], ...markerData }
  emit('markers-update', props.index, localCity.markers)
}

const removeMarker = (markerIndex) => {
  localCity.markers.splice(markerIndex, 1)
  emit('markers-update', props.index, localCity.markers)
}

const detectBounds = () => {
  if (!localCity.markers || localCity.markers.length === 0) {
    alert('Add some markers first to detect bounds automatically.')
    return
  }

  const lats = localCity.markers.map(m => m.coordinates.lat).filter(lat => lat)
  const lngs = localCity.markers.map(m => m.coordinates.lng).filter(lng => lng)

  if (lats.length === 0 || lngs.length === 0) {
    alert('Markers need valid coordinates to detect bounds.')
    return
  }

  // Add some padding to the bounds
  const padding = 0.001

  boundsForm.north = Math.max(...lats) + padding
  boundsForm.south = Math.min(...lats) - padding
  boundsForm.east = Math.max(...lngs) + padding
  boundsForm.west = Math.min(...lngs) - padding
}
</script>
