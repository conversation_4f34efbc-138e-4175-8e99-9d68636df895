<template>
  <div class="h-96 w-full rounded-lg overflow-hidden border border-gray-300 leaflet-container-wrapper relative">
    <!-- Loading overlay -->
    <div v-if="isLoading" class="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-400">
      <div class="text-center">
        <svg class="animate-spin h-8 w-8 text-blue-500 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <p class="mt-2 text-sm text-gray-600">{{ loadingMessage }}</p>
      </div>
    </div>
    
    <div ref="mapContainer" class="h-full w-full">
      <!-- Map will be rendered here -->
    </div>
    
    <!-- Error message -->
    <div v-if="errorMessage" class="absolute bottom-0 left-0 right-0 bg-red-100 text-red-700 p-2 text-sm">
      {{ errorMessage }}
      <button type="button" @click="resetMap" class="ml-2 text-blue-500 underline">Reset Map</button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import L from 'leaflet'
import 'leaflet/dist/leaflet.css'
import _ from 'lodash'

const props = defineProps({
  mapData: {
    type: Object,
    required: true
  },
  cities: {
    type: Array,
    default: () => []
  },
  markers: {
    type: Array,
    default: () => []
  },
  height: {
    type: String,
    default: '400px'
  }
})

const emit = defineEmits(['marker-click', 'map-bounds-change', 'marker-add', 'marker-move', 'center-change', 'zoom-change'])

const mapContainer = ref(null)
let map = null
let markersLayer = null
let citiesLayer = null
const isLoading = ref(false)
const loadingMessage = ref('Loading map...')
const errorMessage = ref('')
const mapInitialized = ref(false)

// Performance optimizations
const MAX_VISIBLE_MARKERS = 300
let skipMarkersForPerformance = false
let debouncedEmitCenterChange = null
let debouncedEmitZoomChange = null
let debouncedUpdateMapContent = null

// Fix Leaflet's default icon issue
delete L.Icon.Default.prototype._getIconUrl
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
})

onMounted(async () => {
  // Create debounced functions
  debouncedEmitCenterChange = _.debounce((center) => {
    emit('center-change', [center.lat, center.lng])
  }, 800) // Increased from 300ms to 800ms
  
  debouncedEmitZoomChange = _.debounce((zoom) => {
    emit('zoom-change', zoom)
  }, 800) // Increased from 300ms to 800ms
  
  debouncedUpdateMapContent = _.debounce(updateMapContent, 300)
  
  await nextTick()
  initializeMap()
})

onUnmounted(() => {
  cleanupMap()
})

// Use a throttled watch to avoid too frequent updates
watch([() => props.cities, () => props.markers], () => {
  if (!mapInitialized.value) return
  
  // Show loading indicator for large datasets
  const totalMarkers = props.markers.length + 
    props.cities.reduce((total, city) => total + (city.markers?.length || 0), 0)
  
  if (totalMarkers > 50) {
    showLoading('Updating map markers...')
  }
  
  // Use nextTick and setTimeout to avoid blocking the UI
  nextTick(() => {
    setTimeout(() => {
      try {
        debouncedUpdateMapContent()
      } catch (error) {
        handleError('Error updating map content', error)
      }
    }, 0)
  })
}, { deep: true })

// Watch for changes to center coordinates with debounce
watch(() => props.mapData.center_coordinates, (newCenter) => {
  if (!map || !mapInitialized.value) return
  
  try {
    if (newCenter && Array.isArray(newCenter) && newCenter.length >= 2) {
      // Make sure coordinates are valid numbers
      const lat = Number(newCenter[0])
      const lng = Number(newCenter[1])
      
      if (!isNaN(lat) && !isNaN(lng)) {
        map.setView([lat, lng], map.getZoom())
      }
    }
  } catch (error) {
    handleError('Error updating center coordinates', error)
  }
}, { deep: true })

// Watch for changes to zoom level with debounce
watch(() => props.mapData.zoom_level, (newZoom) => {
  if (!map || !mapInitialized.value) return
  
  try {
    if (newZoom !== undefined && newZoom !== null) {
      const zoom = Number(newZoom)
      if (!isNaN(zoom)) {
        map.setZoom(zoom)
      }
    }
  } catch (error) {
    handleError('Error updating zoom level', error)
  }
})

const showLoading = (message = 'Loading...') => {
  loadingMessage.value = message
  isLoading.value = true
}

const hideLoading = () => {
  isLoading.value = false
}

const handleError = (message, error) => {
  console.error(`${message}:`, error)
  errorMessage.value = `${message}. Try resetting the map.`
  hideLoading()
}

const resetMap = () => {
  errorMessage.value = ''
  cleanupMap()
  nextTick(() => {
    initializeMap()
  })
}

const cleanupMap = () => {
  if (map) {
    // Remove all event listeners to prevent memory leaks
    map.off()
    map.remove()
    map = null
  }
  
  // Cancel any pending debounced functions
  if (debouncedEmitCenterChange) debouncedEmitCenterChange.cancel()
  if (debouncedEmitZoomChange) debouncedEmitZoomChange.cancel()
  if (debouncedUpdateMapContent) debouncedUpdateMapContent.cancel()
  
  markersLayer = null
  citiesLayer = null
  mapInitialized.value = false
}

const initializeMap = () => {
  showLoading('Initializing map...')
  
  try {
    if (!mapContainer.value) {
      handleError('Map container not found', new Error('DOM element not ready'))
      return
    }

    const centerCoords = props.mapData.center_coordinates || [36.1911, 44.0094] // Default to Erbil
    
    map = L.map(mapContainer.value, {
      center: [centerCoords[0], centerCoords[1]],
      zoom: props.mapData.zoom_level || 10,
      scrollWheelZoom: true,
      doubleClickZoom: true,
      dragging: true,
      // Add additional options to improve performance
      preferCanvas: true,
      renderer: L.canvas(),
      // Enable smooth animations for better user experience
      zoomAnimation: true,
      markerZoomAnimation: true,
      fadeAnimation: true,
      // Optimize animation duration
      zoomAnimationThreshold: 4
    })

    // Add OpenStreetMap tiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
      maxZoom: 19
    }).addTo(map)

    // Initialize layers
    markersLayer = L.layerGroup().addTo(map)
    citiesLayer = L.layerGroup().addTo(map)

    // Map event listeners with debounce
    map.on('moveend', (e) => {
      // Skip if the event wasn't triggered by user interaction
      if (!e.originalEvent) return;
      
      const bounds = map.getBounds()
      const center = map.getCenter()
      
      // Use debounced function for center change
      debouncedEmitCenterChange(center)
      
      // Emit bounds change with basic details
      emit('map-bounds-change', {
        north: bounds.getNorth(),
        south: bounds.getSouth(),
        east: bounds.getEast(),
        west: bounds.getWest()
      })
    })
    
    // Listen for zoom changes with debounce
    map.on('zoomend', (e) => {
      // Skip if the event wasn't triggered by user interaction
      if (!e.originalEvent) return;
      
      const zoom = map.getZoom()
      debouncedEmitZoomChange(zoom)
    })

    // Double click to add marker (for single map type)
    if (props.mapData.type === 'single') {
      map.on('dblclick', (e) => {
        emit('marker-add', {
          lat: e.latlng.lat,
          lng: e.latlng.lng
        })
      })
    }

    mapInitialized.value = true
    
    // Add content after initialization is complete
    nextTick(() => {
      updateMapContent()
    })
  } catch (error) {
    handleError('Failed to initialize map', error)
  }
}

const updateMapContent = () => {
  if (!map || !mapInitialized.value) return
  
  try {
    // Clear existing layers
    markersLayer.clearLayers()
    citiesLayer.clearLayers()

    // Check if we need to optimize for performance
    const totalMarkers = props.markers.length + 
      props.cities.reduce((total, city) => total + (city.markers?.length || 0), 0)
    
    skipMarkersForPerformance = totalMarkers > MAX_VISIBLE_MARKERS
    
    if (props.mapData.type === 'single') {
      // Add markers for single map - use batch processing for large datasets
      if (skipMarkersForPerformance) {
        // For large datasets, just add a subset of markers
        const visibleMarkers = props.markers.slice(0, MAX_VISIBLE_MARKERS)
        visibleMarkers.forEach((marker, index) => {
          addMarkerToMap(marker, index)
        })
      } else {
        // Process in small batches to avoid UI blocking
        processBatch(props.markers, (marker, index) => {
          addMarkerToMap(marker, index)
        })
      }
    } else if (props.mapData.type === 'multi_city') {
      // Add city boundaries first
      props.cities.forEach((city, cityIndex) => {
        addCityToMap(city, cityIndex)
      })
      
      // Then add markers in batches
      if (skipMarkersForPerformance) {
        // For large datasets, just process a limited number per city
        props.cities.forEach((city, cityIndex) => {
          if (city.markers && city.markers.length > 0) {
            const visibleMarkers = city.markers.slice(0, Math.max(10, Math.floor(MAX_VISIBLE_MARKERS / props.cities.length)))
            visibleMarkers.forEach((marker, markerIndex) => {
              addMarkerToMap(marker, markerIndex, cityIndex)
            })
          }
        })
      } else {
        // Process cities in batches
        processBatch(props.cities, (city, cityIndex) => {
          if (city.markers) {
            processBatch(city.markers, (marker, markerIndex) => {
              addMarkerToMap(marker, markerIndex, cityIndex)
            })
          }
        })
      }
    }
    
    hideLoading()
  } catch (error) {
    handleError('Error updating map content', error)
  }
}

// Process arrays in small batches to avoid UI blocking
const processBatch = (items, callback, batchSize = 10) => {
  if (!items || items.length === 0) return
  
  let index = 0
  
  const processNextBatch = () => {
    const limit = Math.min(index + batchSize, items.length)
    
    for (; index < limit; index++) {
      callback(items[index], index)
    }
    
    if (index < items.length) {
      setTimeout(processNextBatch, 0)
    }
  }
  
  processNextBatch()
}

const addMarkerToMap = (marker, markerIndex, cityIndex = null) => {
  if (!marker.coordinates || !marker.coordinates.lat || !marker.coordinates.lng) return

  // Create custom icon based on icon_type
  const markerIcon = createCustomIcon(marker.icon_type)
  
  const leafletMarker = L.marker([marker.coordinates.lat, marker.coordinates.lng], {
    icon: markerIcon,
    draggable: true
  })

  // Popup content
  const title = marker.title?.en || marker.title || 'Untitled Marker'
  const description = marker.description?.en || marker.description || ''
  
  leafletMarker.bindPopup(`
    <div class="p-2">
      <h3 class="font-semibold text-sm">${title}</h3>
      ${description ? `<p class="text-xs text-gray-600 mt-1">${description}</p>` : ''}
    </div>
  `)

  // Event listeners - use try-catch to prevent errors from breaking the entire map
  leafletMarker.on('click', () => {
    try {
      emit('marker-click', { marker, index: markerIndex, cityIndex })
    } catch (e) {
      console.error('Error handling marker click:', e)
    }
  })

  leafletMarker.on('dragend', (e) => {
    try {
      const newPos = e.target.getLatLng()
      emit('marker-move', {
        markerIndex,
        cityIndex,
        coordinates: { lat: newPos.lat, lng: newPos.lng }
      })
    } catch (e) {
      console.error('Error handling marker move:', e)
    }
  })

  markersLayer.addLayer(leafletMarker)
}

const addCityToMap = (city, cityIndex) => {
  try {
    // Always add a city marker for the center point
    if (city.center_coordinates && Array.isArray(city.center_coordinates) && city.center_coordinates.length >= 2) {
      const cityMarker = L.marker([city.center_coordinates[0], city.center_coordinates[1]], {
        icon: L.icon({
          iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
          iconSize: [25, 41],
          iconAnchor: [12, 41],
          popupAnchor: [1, -34],
          className: 'city-center-marker'
        })
      })

      cityMarker.bindPopup(`
        <div class="p-2">
          <h3 class="font-semibold text-sm">${city.dynamic_title?.en || city.name?.en || city.name || 'Untitled City'}</h3>
          ${city.dynamic_title?.ku ? `<h4 class="font-medium text-sm text-gray-700 mt-1" dir="rtl">${city.dynamic_title.ku}</h4>` : 
            city.name?.ku && city.name.ku !== (city.dynamic_title?.en || city.name?.en || city.name) ? `<h4 class="font-medium text-sm text-gray-700 mt-1" dir="rtl">${city.name.ku}</h4>` : ''}
          ${city.dynamic_description?.en ? `<p class="text-xs text-gray-600 mt-1">${city.dynamic_description.en}</p>` : 
            city.description?.en ? `<p class="text-xs text-gray-600 mt-1">${city.description.en}</p>` : ''}
          ${city.dynamic_description?.ku ? `<p class="text-xs text-gray-600 mt-1" dir="rtl">${city.dynamic_description.ku}</p>` : ''}
          <p class="text-xs text-blue-600 mt-1">City Center</p>
        </div>
      `)

      citiesLayer.addLayer(cityMarker)
    }

    // Add city boundary if bounds are available
    if (city.bounds && typeof city.bounds === 'object') {
      let boundsShape = null
      
      try {
        // Handle different bounds formats
        if (city.bounds.south !== undefined && city.bounds.north !== undefined && 
            city.bounds.west !== undefined && city.bounds.east !== undefined) {
          // Standard bounds format - create rectangle
          const bounds = [
            [city.bounds.south, city.bounds.west],
            [city.bounds.north, city.bounds.east]
          ]
          boundsShape = L.rectangle(bounds, {
            color: '#6366F1',
            weight: 2,
            fillOpacity: 0.1,
            dashArray: '5, 5'
          })
        } else if (Array.isArray(city.bounds) && city.bounds.length >= 4 && typeof city.bounds[0] === 'number') {
          // Simple array format: [south, west, north, east] - create rectangle
          const bounds = [
            [city.bounds[0], city.bounds[1]],
            [city.bounds[2], city.bounds[3]]
          ]
          boundsShape = L.rectangle(bounds, {
            color: '#6366F1',
            weight: 2,
            fillOpacity: 0.1,
            dashArray: '5, 5'
          })
        } else if (Array.isArray(city.bounds) && city.bounds.length > 0 && 
                  typeof city.bounds[0] === 'object' && city.bounds[0].lat !== undefined) {
          // Polygon coordinate points format - create polygon
          const coordinates = city.bounds.map(point => [point.lat, point.lng])
          boundsShape = L.polygon(coordinates, {
            color: '#6366F1',
            weight: 2,
            fillOpacity: 0.1,
            dashArray: '5, 5'
          })
        }
      } catch (e) {
        console.error('Error creating bounds shape:', e)
      }
      
      if (boundsShape) {
        boundsShape.bindPopup(`
          <div class="p-2">
            <h3 class="font-semibold text-sm">${city.dynamic_title?.en || city.name?.en || city.name || 'Untitled City'}</h3>
            ${city.dynamic_title?.ku ? `<h4 class="font-medium text-sm text-gray-700 mt-1" dir="rtl">${city.dynamic_title.ku}</h4>` : 
              city.name?.ku && city.name.ku !== (city.dynamic_title?.en || city.name?.en || city.name) ? `<h4 class="font-medium text-sm text-gray-700 mt-1" dir="rtl">${city.name.ku}</h4>` : ''}
            ${city.dynamic_description?.en ? `<p class="text-xs text-gray-600 mt-1">${city.dynamic_description.en}</p>` : 
              city.description?.en ? `<p class="text-xs text-gray-600 mt-1">${city.description.en}</p>` : ''}
            ${city.dynamic_description?.ku ? `<p class="text-xs text-gray-600 mt-1" dir="rtl">${city.dynamic_description.ku}</p>` : ''}
            <p class="text-xs text-blue-600 mt-1">City Boundary</p>
          </div>
        `)

        citiesLayer.addLayer(boundsShape)
      }
    }
  } catch (error) {
    console.error('Error adding city to map:', error)
  }
}

const createCustomIcon = (iconType = 'default') => {
  try {
    switch (iconType) {
      case 'bim':
        // BIM Logo marker
        return L.icon({
          iconUrl: '/bim_logo.png',
          iconSize: [32, 32],
          iconAnchor: [16, 32],
          popupAnchor: [0, -32],
          className: 'custom-marker-icon bim-marker'
        });
      case 'atm':
        // ATM Icon - use SVG file
        return L.icon({
          iconUrl: '/atm_icon.svg',
          iconSize: [32, 32],
          iconAnchor: [16, 32],
          popupAnchor: [0, -32],
          className: 'custom-marker-icon atm-marker'
        });
      default:
        // Default marker (red)
        return L.icon({
          iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
          iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
          shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
          iconSize: [25, 41],
          iconAnchor: [12, 41],
          popupAnchor: [1, -34],
          shadowSize: [41, 41]
        });
    }
  } catch (error) {
    console.error('Error creating custom icon:', error)
    // Return default icon as fallback
    return L.icon({
      iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
      iconSize: [25, 41],
      iconAnchor: [12, 41],
      popupAnchor: [1, -34],
    });
  }
}

const fitMapToBounds = () => {
  if (!map || !mapInitialized.value) return
  
  try {
    showLoading('Fitting map to bounds...')
    
    const allMarkers = []
    
    if (props.mapData.type === 'single') {
      allMarkers.push(...props.markers.filter(m => m.coordinates?.lat && m.coordinates?.lng)
        .map(marker => [marker.coordinates.lat, marker.coordinates.lng]))
    } else {
      props.cities.forEach(city => {
        if (city.markers) {
          allMarkers.push(...city.markers.filter(m => m.coordinates?.lat && m.coordinates?.lng)
            .map(marker => [marker.coordinates.lat, marker.coordinates.lng]))
        }
        
        // Also include city centers
        if (city.center_coordinates && Array.isArray(city.center_coordinates) && 
            city.center_coordinates.length >= 2) {
          allMarkers.push([city.center_coordinates[0], city.center_coordinates[1]])
        }
      })
    }

    if (allMarkers.length > 0) {
      const bounds = L.latLngBounds(allMarkers)
      map.fitBounds(bounds, { padding: [20, 20] })
      hideLoading()
    } else {
      hideLoading()
    }
  } catch (error) {
    handleError('Error fitting map to bounds', error)
  }
}

// Expose methods to parent component
defineExpose({
  fitMapToBounds,
  getMap: () => map,
  resetMap
})
</script>

<style scoped>
:deep(.custom-marker-icon) {
  background: transparent;
  border: none;
}

:deep(.bim-marker) {
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

:deep(.atm-marker) {
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

:deep(.atm-marker-container) {
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.leaflet-popup-content-wrapper) {
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Fix z-index for leaflet elements to stay below modals */
.leaflet-container-wrapper {
  position: relative;
  z-index: 0;
}

:deep(.leaflet-pane),
:deep(.leaflet-control),
:deep(.leaflet-top),
:deep(.leaflet-bottom) {
  z-index: 200 !important;
}

:deep(.leaflet-tooltip-pane) {
  z-index: 250 !important;
}

:deep(.leaflet-popup-pane) {
  z-index: 300 !important;
}

:deep(.leaflet-control-zoom) {
  z-index: 350 !important;
}
</style>
