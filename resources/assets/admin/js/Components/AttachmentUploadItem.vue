<script setup>
import { computed, ref } from 'vue';
import PDFIcon from '@/Components/Icons/PDFIcon.vue';
import TextButton from '@/Components/TextButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';

const fileRef = ref(null);

const file = ref(null);

const props = defineProps({
    error: {
        type: String,
        defautl: null,
    },
    shortName: {
        type: String,
        defautl: null,
    },
    id: {
        type: String,
        required: false,
    },
    originalUrl: {
        type: String,
        required: false,
    },
    hasUploadedFile: {
        type: Boolean,
        default: false,
    },
    fileName: {
        type: String,
        defautl: null,
    },
});

const hasFile = computed(() => {
    return file.value;
});

const fileName = computed(() => {
    if (props.hasUploadedFile) {
        return props.fileName;
    }
    return hasFile.value ? file.value.name : 'attachment';
});

const emit = defineEmits(['change', 'removeAttachment']);

function fileChanged(shortName) {
    file.value = fileRef.value.files[0];
    emit('change', shortName, fileRef.value.files[0]);
}

function removeFile(shortName) {
    file.value = null;
    fileRef.value.value = "";
    emit('change', shortName, null);
}
</script>
<template>
    <li v-if="!hasUploadedFile" class="p-3 flex items-center">
        <div class="flex-1 flex items-center">
            <PDFIcon class="w-5 mr-3" :class="{'text-red-500': error}" />
            <p class="truncate max-w-[9rem] text-sm text-gray-600" :class="{'text-red-500': error}">
                <span>{{ shortName.toString().toUpperCase() }} - {{ fileName }}</span>
                <span v-if="shortName !== 'en'" class="text-xs"> (Optional) </span>
            </p>
        </div>
        <label :for="`attachments.${shortName}`">
            <TextButton v-if="hasFile" @click="removeFile(shortName)" color="red"
                class="px-3 py-1 rounded uppercase text-xs">Remove</TextButton>
            <SecondaryButton v-if="!hasFile" @click="() => fileRef.click()"
                class="px-3 py-1 rounded bg-gray-100 uppercase text-xs" :class="{'text-red-500 bg-red-50': error}">
                Upload
            </SecondaryButton>
            <input type="file" ref="fileRef" @change="fileChanged(shortName, $event.target.files[0])"
                accept="application/pdf" class="hidden" :id="`attachments-${shortName}`" />
        </label>
    </li>

    <li v-else class="p-3 flex items-center">
        <div class="flex-1 flex items-center">
            <PDFIcon class="w-5 mr-3" />
            <p class="truncate text-sm text-gray-600 max-w-[9rem]">
                <span>{{ shortName.toString().toUpperCase() }} - {{ fileName }}</span>
            </p>
        </div>
        <TextButton @click="emit('removeAttachment', id)" color="red"
            class="px-3 py-1 rounded uppercase text-xs">Remove
        </TextButton>
    </li>
</template>