<script setup>
import { onMounted, ref } from 'vue';

const props = defineProps({
    modelValue: null,
    class: {
        type: String,
        default: ''
    },
    type: {
        type: String,
        default: 'text'
    }
});

defineEmits(['update:modelValue']);

const input = ref(null);

onMounted(() => {
    if (input.value.hasAttribute('autofocus')) {
        input.value.focus();
    }
});
</script>

<template>
    <input 
        :class="[
            'block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm',
            props.class
        ]"
        :value="modelValue" 
        :type="type"
        @input="$emit('update:modelValue', $event.target.value)" 
        ref="input"
    >
</template>
