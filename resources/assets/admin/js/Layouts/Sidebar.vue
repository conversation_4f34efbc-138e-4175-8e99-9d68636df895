<script setup>
import {
  Dialog,
  DialogPanel,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import {
  HomeIcon,
  UsersIcon,
  XMarkIcon as XIcon,
  CogIcon,
  ShieldExclamationIcon,
  ExclamationTriangleIcon,
  PhotoIcon,
  NewspaperIcon,
  MapIcon,
} from '@heroicons/vue/24/outline'
import NavLink from '@/Components/NavLink.vue';
import ApplicationLogo from '@/Components/ApplicationLogo.vue';
import { computed } from 'vue';
import { usePage } from '@inertiajs/inertia-vue3';

const { isSuperAdmin, authPermissions } = usePage().props.value;

const navigation = [
  { name: 'Dashboard', href: '/', icon: HomeIcon, permission: '*' },
  { name: 'News', href: '/publications', icon: NewspaperIcon, permission: 'menu news' },
  { name: 'Projects', href: '/projects', icon: PhotoIcon, permission: 'menu projects' },
  { name: 'Cities', href: '/cities', icon: MapIcon, permission: 'menu projects' },
  { name: 'Project Maps', href: '/project-maps', icon: MapIcon, permission: 'menu projects' },
  { name: 'Job Vacancies', href: '/job-vacancies', icon: NewspaperIcon, permission: 'menu vacancy' },
  { name: 'Slides', href: '/slides', icon: PhotoIcon, permission: '*' },
  { name: 'Gallery', href: '/galleries', icon: PhotoIcon, permission: 'menu gallery' },
  { name: 'Settings', href: '/settings', icon: CogIcon, permission: 'menu setting' },
  { name: 'Users', href: '/users', icon: UsersIcon, },
  { name: 'Role & Permissions', href: '/roles', icon: ShieldExclamationIcon, },
  { name: 'Activity Log', href: '/activity-logs', icon: ExclamationTriangleIcon, },
]
defineProps({
    isOpen: {
        type: Boolean,
    },
});
const emit = defineEmits(['open', 'close']);


const allowedMenuItems = computed(() => {
  return navigation.filter(({permission}) => {
    if(isSuperAdmin) {
      return true;
    }
    return permission === '*' || authPermissions.indexOf(permission) !== -1;
  });
})
</script>
<template>
    <TransitionRoot as="template" :show="isOpen">
      <Dialog as="div" class="relative z-40 md:hidden" @close="emit('close')">
        <TransitionChild as="template" enter="transition-opacity ease-linear duration-300" enter-from="opacity-0" enter-to="opacity-100" leave="transition-opacity ease-linear duration-300" leave-from="opacity-100" leave-to="opacity-0">
          <div class="fixed inset-0 bg-gray-600 bg-opacity-75" />
        </TransitionChild>

        <div class="fixed inset-0 flex z-40">
          <TransitionChild as="template" enter="transition ease-in-out duration-300 transform" enter-from="-translate-x-full" enter-to="translate-x-0" leave="transition ease-in-out duration-300 transform" leave-from="translate-x-0" leave-to="-translate-x-full">
            <DialogPanel class="relative flex-1 flex flex-col max-w-xs w-full pt-5 pb-4 bg-zinc-200">
              <TransitionChild as="template" enter="ease-in-out duration-300" enter-from="opacity-0" enter-to="opacity-100" leave="ease-in-out duration-300" leave-from="opacity-100" leave-to="opacity-0">
                <div class="absolute top-0 right-0 -mr-12 pt-2">
                  <button type="button" class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white" @click="emit('close')">
                    <span class="sr-only">Close sidebar</span>
                    <XIcon class="h-6 w-6 text-white" aria-hidden="true" />
                  </button>
                </div>
              </TransitionChild>
              <div class="flex-shrink-0 flex items-center px-4">
                <ApplicationLogo class="max-w-[7rem]" />
              </div>
              <div class="mt-5 flex-1 h-0 overflow-y-auto">
                <nav class="px-2 space-y-1">
                  <NavLink v-for="item in allowedMenuItems" :key="item.name" :href="item.href" >
                    <component :is="item.icon" :class="[item.current ? 'text-primary' : 'text-primary-500 group-hover:text-primary', 'mr-4 flex-shrink-0 h-6 w-6']" aria-hidden="true" />
                    {{ item.name }}
                  </NavLink>
                </nav>
              </div>
            </DialogPanel>
          </TransitionChild>
          <div class="flex-shrink-0 w-14" aria-hidden="true">
            <!-- Dummy element to force sidebar to shrink to fit close icon -->
          </div>
        </div>
      </Dialog>
    </TransitionRoot>

    <!-- Static sidebar for desktop -->
    <div class="hidden md:flex md:w-64 md:flex-col md:fixed md:inset-y-0">
      <!-- Sidebar component, swap this element with another sidebar if you like -->
      <div class="flex-1 flex flex-col min-h-0 bg-zinc-200">
        <div class="flex items-center h-16 flex-shrink-0 px-4 bg-zinc-200">
            <ApplicationLogo class="max-w-[7rem]" />
        </div>
        <div class="flex-1 flex flex-col overflow-y-auto">
          <nav class="flex-1 px-2 py-4 space-y-1">
            <NavLink v-for="item in allowedMenuItems" :key="item.name" :href="item.href">
              <component :is="item.icon" :class="[item.current ? 'text-primary' : 'text-primary-500 group-hover:text-primary', 'mr-3 flex-shrink-0 h-6 w-6']" aria-hidden="true" />
              {{ item.name }}
            </NavLink>
          </nav>
        </div>
      </div>
    </div>
</template>
