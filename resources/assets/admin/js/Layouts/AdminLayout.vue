<template>
  <div>
    <Sidebar :is-open="sidebarOpen" @open="sidebarOpen = true" @close="sidebarOpen = false"/>
    <div class="md:pl-64 flex flex-col">
      <TopNav @open-sidebar="sidebarOpen = true"/>  

      <main class="flex-1">
        <div class="py-6">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" scroll-region>
            <!-- Start Content -->
            <div class="py-4">
              <slot />
            </div><!-- End Content -->
          </div>
        </div>
      </main>
    </div>
    <Modal />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import Sidebar from './Sidebar.vue'
import TopNav from './TopNav.vue'
import { Modal } from 'momentum-modal'
const sidebarOpen = ref(false)
</script>