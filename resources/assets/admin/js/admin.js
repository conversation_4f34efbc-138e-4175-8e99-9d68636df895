import { createApp, h } from "vue";
import { createInertiaApp } from "@inertiajs/inertia-vue3";
import { InertiaProgress } from "@inertiajs/progress";
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";
import { ZiggyVue } from "../../../../vendor/tightenco/ziggy/dist/vue.m";
import { modal } from "momentum-modal";
import AdminLayout from "@/Layouts/AdminLayout.vue";
import Toast from "vue-toastification"

import '@vuepic/vue-datepicker/dist/main.css'
import "vue-toastification/dist/index.css";
import "../css/admin.css";

import { notifications } from './Plugins/notifications';

const appName =
    window.document.getElementsByTagName("title")[0]?.innerText || "ASCO";

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => {
        return resolvePageComponent(
            `./Pages/${name}.vue`,
            import.meta.glob("./Pages/**/*.vue")
        ).then((page) => {
            if (page.default.layout === undefined) {
                page.default.layout = AdminLayout;
            }
            return page;
        });
    },
    setup({ el, app, props, plugin }) {
        createApp({ render: () => h(app, props) })
            .use(modal, {
                resolve: (name) => {
                    return resolvePageComponent(
                        `./Pages/${name}.vue`,
                        import.meta.glob("./Pages/**/*.vue")
                    )
                }
                    ,
            })
            .use(Toast, { timeout: 3000 })
            .use(notifications)
            .use(ZiggyVue)
            .use(plugin)
            .mount(el);
    },
});

InertiaProgress.init({ color: "#0033A1" });
