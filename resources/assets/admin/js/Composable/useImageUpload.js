import ImageUploader from "quill-image-uploader";
import axios from "axios";

export default (mediaType) => {
    const modules = {
        name: "imageUploader",
        module: ImageUploader,
        options: {
            upload: (file) => {
                return new Promise((resolve, reject) => {
                    const formData = new FormData();
                    formData.append("image", file);
                    formData.append("media_type", mediaType);

                    axios
                        .post(route("admin.media.upload"), formData)
                        .then((res) => {
                            console.log(res);
                            resolve(res.data.data.url);
                        })
                        .catch((err) => {
                            reject("Upload failed");
                            console.error("Error:", err);
                        });
                });
            },
        },
    };
    return {
        modules,
    };
};
