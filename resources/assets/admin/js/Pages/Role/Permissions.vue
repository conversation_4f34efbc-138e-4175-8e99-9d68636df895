<script setup>
import route from 'ziggy';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import { computed } from 'vue';
import { Inertia } from '@inertiajs/inertia';
import { CheckIcon } from '@heroicons/vue/24/outline'

const { role, permissions } = defineProps({
  permissions: {
    type: Array,
    default: [],
  },
  role: {
    type: Object,
    default: () => ({}),
  },
});


const groupedPermissions = computed(() => {
  return permissions.reduce((acc, permission) => {
    const name = permission.name.split(" ")[1];
    acc[name] = acc[name] || [];
    acc[name].push(permission);
    return acc;
  }, {})
})

function hasPermission(permissionId) {
  return !! role.permissions.find(p => p.id == permissionId);
}

function cancel() {
  return Inertia.visit(route('admin.roles.index'));
}

function togglePermission(permission_id) {
  Inertia.put(route('admin.roles.permissions.update' , role.id), {
    permission_id
  }, {
    preserveScroll: true,
    preserveState: false,
  });
}
</script>  
<template>
  <div class="max-w-7xl mx-auto">
    <div class="mb-6 lg:mb-10">
      <h3 class="text-xl font-medium leading-6 text-gray-900">Edit {{ role.name }} permissions</h3>
    </div>
    <div class="mb-6">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div v-for="(group, groupName) in groupedPermissions" :key="groupName" class="bg-white shadow rounded-lg p-4">
          <h3 class="text-lg font-semibold text-gray-700 capitalize mb-3">{{ groupName }}</h3>
          <div class="flex gap-4 flex-wrap">
            <template v-for="{id, name} in group" :key="id">
              <button @click="togglePermission(id)"  :class="['bg-white rounded-lg px-2 py-1 inline-flex text-center text-xs font-semibold capitalize select-none cursor-pointer border shadow-sm', hasPermission(id) ? 'bg-green-50 text-green-600 border-green-200 space-x-2' : 'border-gray-200']" >
                <CheckIcon v-if="hasPermission(id)" class="w-4 h-4" />
                <span>{{ name }}</span>
              </button>
            </template>
          </div>
        </div>
      </div>
    </div>
    <div class="flex gap-4">
      <SecondaryButton class="px-5 py-2.5 text-base" :disabled="true" @click="cancel">Go back</SecondaryButton>
    </div>
  </div>
</template>
    