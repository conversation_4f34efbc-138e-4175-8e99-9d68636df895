<script setup>
import { useForm } from '@inertiajs/inertia-vue3';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import FormInput from '@/Components/FormInput.vue';
import FormError from '@/Components/FormError.vue';
import route from 'ziggy';
import { Inertia } from '@inertiajs/inertia';
import { CheckIcon } from '@heroicons/vue/24/outline';
import { computed } from 'vue';

const props = defineProps({
  role: {
    type: Object,
    required: true,
  },
  permissions: {
    type: Array,
    default: [],
  },
  grouped_permissions: {
    type: Object,
    default: () => ({}),
  },
});

const form = useForm({
  name: props.role.name,
  description: props.role.description || '',
  permissions: props.role.permissions.map(p => p.id),
});

const groupedPermissions = computed(() => {
  return props.grouped_permissions;
});

function hasPermission(permissionId) {
  return form.permissions.includes(permissionId);
}

function togglePermission(permissionId) {
  if (hasPermission(permissionId)) {
    form.permissions = form.permissions.filter(id => id !== permissionId);
  } else {
    form.permissions.push(permissionId);
  }
}

function submit() {
  form.put(route('admin.roles.update', props.role.id), {
    preserveScroll: true,
    preserveState: true,
  });
}

function cancel() {
  Inertia.visit(route('admin.roles.index'));
}
</script>

<template>
  <div class="max-w-7xl mx-auto">
    <div class="mb-6 lg:mb-10">
      <h3 class="text-xl font-medium leading-6 text-gray-900">Edit Role: {{ role.name }}</h3>
      <p class="mt-1 text-sm text-gray-500">
        Update role information and permissions to control user access.
      </p>
    </div>

    <form @submit.prevent="submit" class="space-y-6 lg:space-y-8">
      <div class="flex flex-col lg:flex-row gap-6">
        <!-- Left Column: Role Information -->
        <div class="lg:w-5/12">
          <div class="bg-white shadow rounded-lg p-6 space-y-6">
            <h4 class="text-lg font-medium text-gray-900">Role Information</h4>
            
            <FormInput 
              label="Role Name" 
              v-model="form.name" 
              autofocus 
              :error="form.errors.name"
              placeholder="e.g., Editor, Moderator"
            />
            
            <FormInput 
              label="Description" 
              type="textarea" 
              rows="3"
              v-model="form.description" 
              :error="form.errors.description"
              placeholder="Brief description of this role's purpose and responsibilities"
            />
            
            <!-- Role Stats -->
            <div class="border-t pt-4">
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="text-gray-500">Permissions:</span>
                  <span class="font-medium ml-1">{{ form.permissions.length }}</span>
                </div>
                <div>
                  <span class="text-gray-500">Users:</span>
                  <span class="font-medium ml-1">{{ role.users_count || 0 }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Column: Permissions -->
        <div class="lg:w-7/12">
          <div class="bg-white shadow rounded-lg p-6">
            <h4 class="text-lg font-medium text-gray-900 mb-6">Permissions</h4>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div v-for="(group, groupName) in groupedPermissions" :key="groupName" class="border border-gray-200 rounded-lg p-4">
                <h5 class="text-md font-semibold text-gray-700 capitalize mb-3">{{ groupName }}</h5>
                <div class="space-y-2">
                  <div v-for="permission in group" :key="permission.id" class="flex items-center">
                    <button 
                      type="button"
                      @click="togglePermission(permission.id)"  
                      :class="[
                        'bg-white rounded-md px-3 py-2 inline-flex items-center text-center text-sm font-medium capitalize select-none cursor-pointer border shadow-sm w-full justify-between transition-colors', 
                        hasPermission(permission.id) 
                          ? 'bg-green-50 text-green-700 border-green-200' 
                          : 'text-gray-700 border-gray-200 hover:bg-gray-50'
                      ]"
                    >
                      <span>{{ permission.name }}</span>
                      <CheckIcon v-if="hasPermission(permission.id)" class="w-4 h-4 text-green-600" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <FormError v-if="form.errors.permissions" class="mt-3">{{ form.errors.permissions }}</FormError>
          </div>
        </div>
      </div>

      <div class="flex gap-4">
        <PrimaryButton 
          class="px-5 py-2.5 text-base" 
          :loading="form.processing" 
          :disabled="form.processing"
          @click="submit"
        >
          Update Role
        </PrimaryButton>
        <SecondaryButton 
          class="px-5 py-2.5 text-base" 
          :disabled="form.processing" 
          @click="cancel"
        >
          Cancel
        </SecondaryButton>
      </div>
    </form>
  </div>
</template>
