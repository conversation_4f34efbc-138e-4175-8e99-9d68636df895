<script setup>
import { Inertia } from '@inertiajs/inertia';
import route from 'ziggy';
import PageHeading from '@/Components/PageHeading.vue';
import TextButton from '@/Components/TextButton.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import DeleteDialog from '@/Components/DeleteDialog.vue';
import { ref } from 'vue';

const props = defineProps({
    roles: {
        type: Object,
        default: () => ([])
    },
});

const deletableRowId = ref(null);
const isDeleteDialogOpen = ref(false);

function createRole() {
    Inertia.visit(route('admin.roles.create'));
}

function editRole(item) {
    Inertia.visit(route('admin.roles.edit', item.id));
}

function tryDelete(id) {
    deletableRowId.value = id;
    isDeleteDialogOpen.value = true;
}

function closeDeleteDialog() {
    isDeleteDialogOpen.value = false;
    deletableRowId.value = null;
}
</script>

<template>
    <div>
        <div class="flex justify-between items-center">
            <PageHeading heading="Roles & Permissions" />
            <PrimaryButton @click="createRole" class="ml-4">
                Create New Role
            </PrimaryButton>
        </div>
        
        <div class="mt-8 flex flex-col">
            <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
                    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col"
                                        class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">ID
                                    </th>
                                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">
                                        Name</th>
                                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">
                                        Description</th>
                                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">
                                        Permissions</th>
                                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">
                                        Users</th>
                                    <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">
                                        Created</th>
                                    <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                                        <span class="sr-only">Actions</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                                <tr v-for="item in roles.data" :key="item.id">
                                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                                        {{ item.id }}
                                    </td>
                                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900">
                                        {{ item.name }}
                                    </td>
                                    <td class="py-4 pl-4 pr-3 text-sm text-gray-500 max-w-xs truncate">
                                        {{ item.description || '-' }}
                                    </td>
                                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm text-gray-500">
                                        {{ item.permissions_count }} permissions
                                    </td>
                                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm text-gray-500">
                                        {{ item.users_count }} users
                                    </td>
                                    <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm text-gray-500">
                                        {{ item.created_at }}
                                    </td>
                                    <td
                                        class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6 space-x-2">
                                        <TextButton v-if="item.can_edit" color="blue" @click="editRole(item)">Edit</TextButton>
                                        <TextButton v-if="item.can_delete" color="red" @click="tryDelete(item.id)">Delete</TextButton>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <DeleteDialog 
            row-name="Role" 
            route="admin.roles.destroy" 
            :row-id="deletableRowId" 
            :open="isDeleteDialogOpen" 
            @close="closeDeleteDialog"
        />
    </div>
</template>    