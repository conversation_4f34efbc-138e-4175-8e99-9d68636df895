<script setup>
import { useForm } from '@inertiajs/inertia-vue3';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import route from 'ziggy';
import FormSelect from '@/Components/FormSelect.vue';
import { computed, ref } from 'vue';
import { Inertia } from '@inertiajs/inertia';
import FormError from '@/Components/FormError.vue';
import FormInput from '@/Components/FormInput.vue';
import {
  TabGroup,
  TabList,
  Tab,
  TabPanels,
  TabPanel,
  RadioGroup,
  RadioGroupLabel,
  RadioGroupOption,
  Switch,
} from '@headlessui/vue';

import useLanguageObject from '@/Composable/useLanguageObject';

const props = defineProps({
  programs: {
    type: Array,
    default: [],
  },
});

const types = [
  {
    label: "Stories",
    value: "App\\Models\\Story",
    description: 'Video will be listed in the stories page',
  },
  {
    label: "Programs",
    value: "App\\Models\\Program",
    description: 'Video will be listed in the related program page',
  }
]

const { languages, languageObject } = useLanguageObject();

const form = useForm({
  title: { ...languageObject },
  description: { ...languageObject },
  embed_link: null,
  videoble_id: null,
  videoble_type: 'App\\Models\\Story',
  allow_fullscreen: true,
});

const programOptions = computed(() => {
  return props.programs.map(gov => {
    return { id: gov.id, label: gov.name?.en, };
  });
});

function submit() {
  form
    .post(route('admin.videos.store'), {
      preserveScroll: true,
      preserveState: true,
      onSuccess: () => form.reset(),
    });
}

function cancel() {
  Inertia.visit(route('admin.videos.index'));
}

</script>  
<template>
  <div class="max-w-6xl mx-auto">
    <div class="mb-6 lg:mb-10">
      <h3 class="text-lg font-medium leading-6 text-gray-900">Create a video</h3>
    </div>
    <form @submit.prevent="submit" class="space-y-6 max-w-2xl mx-auto">
      <div>
        <RadioGroup v-model="form.videoble_type">
          <RadioGroupLabel class="sr-only">Type</RadioGroupLabel>
          <div class="space-x-2 flex">
            <RadioGroupOption autofocus as="template" v-for="{label, value, description} in types" :key="label"
              :value="value" v-slot="{ active, checked }">
              <div :class="[
                active
                  ? 'ring-2 ring-white ring-opacity-60 ring-offset-2 ring-offset-primary-100'
                  : '',
                checked ? 'bg-primary-900 bg-opacity-75 text-white ' : 'bg-white ',
              ]" class="relative flex cursor-pointer rounded-lg px-5 py-4 shadow-md focus:outline-none">
                <div class="flex w-full items-center justify-between">
                  <div class="flex items-center">
                    <div class="text-sm">
                      <RadioGroupLabel as="p" :class="checked ? 'text-white' : 'text-gray-900'" class="font-medium">
                        {{ label }}
                      </RadioGroupLabel>
                      <RadioGroupDescription as="span" :class="checked ? 'text-gray-100' : 'text-gray-500'"
                        class="inline">
                        <span> {{ description }}</span>
                      </RadioGroupDescription>
                    </div>
                  </div>
                  <div v-show="checked" class="shrink-0 text-white">
                    <svg class="h-6 w-6" viewBox="0 0 24 24" fill="none">
                      <circle cx="12" cy="12" r="12" fill="#fff" fill-opacity="0.2" />
                      <path d="M7 13l3 3 7-7" stroke="#fff" stroke-width="1.5" stroke-linecap="round"
                        stroke-linejoin="round" />
                    </svg>
                  </div>
                </div>
              </div>
            </RadioGroupOption>
          </div>
        </RadioGroup>
      </div>
      <div>
        <FormSelect v-if="form.videoble_type == 'App\\Models\\Program'" class="flex-1" label="Program (optional)"
          v-model="form.videoble_id" :error="form.errors['videoble_id']" :options="programOptions"
          placeholder="Select a program" />
      </div>
      <div>
        <Switch v-model="form.allow_fullscreen" :class="form.allow_fullscreen ? 'bg-green-900' : 'bg-green-700'"
          class="relative inline-flex h-[24px] w-[56px] shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75">
          <span class="sr-only">Use setting</span>
          <span aria-hidden="true" :class="form.allow_fullscreen ? 'translate-x-9' : 'translate-x-0'"
            class="pointer-events-none inline-block h-[20px] w-[20px] transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out" />
        </Switch>
        <span class="align-top inline-block ml-3">Allow Fullscreen</span>
      </div>
      <div>
        <TabGroup>
          <TabList class="flex space-x-1 rounded-t-xl rounded-tr-xl bg-white p-1 max-w-lg shadow-sm">
            <Tab v-for="{ name } in languages" as="template" :key="name" v-slot="{ selected }">
              <button :class="[
                'w-full rounded-lg py-2.5 text-sm font-medium leading-5 uppercase',
                'ring-white ring-opacity-60 ring-offset-2 ring-offset-primary-400 focus:outline-none focus:ring-2',
                selected
                  ? 'bg-gray-100 text-primary-700 font-bold'
                  : 'text-gray-600 hover:bg-gray-50',
              ]">
                {{ name }}
              </button>
            </Tab>
          </TabList>

          <TabPanels>
            <TabPanel v-for="{ iso_code_name, direction, name} in languages" :key="iso_code_name" :class="[
              'rounded-b-xl rounded-tr-xl bg-white p-4 shadow-sm space-y-5',
              'ring-white ring-opacity-60 focus:outline-none',
              `content content-${direction}`
            ]">
              <FormInput :label="`Title (${name})`" v-model="form.title[iso_code_name]"
                :error="form.errors[`title.${iso_code_name}`]" />

              <FormInput type="textarea" :label="`Description (${name})`" v-model="form.description[iso_code_name]"
                :error="form.errors[`description.${iso_code_name}`]" />
            </TabPanel>
          </TabPanels>
        </TabGroup>
        <div>
          <FormInput label="Embed Link" v-model="form.embed_link" :error="form.errors.embed_link" />
        </div>
        <div class="mt-6 flex gap-4">
          <PrimaryButton class="px-5 py-2.5 text-base" :loading="form.processing" :disabled="form.processing"
            @click="submit">Create</PrimaryButton>
          <SecondaryButton class="px-5 py-2.5 text-base" :disabled="form.processing" @click="cancel">Cancel
          </SecondaryButton>
        </div>
      </div>
    </form>
  </div>
</template>

