<script setup>
import { useForm, usePage } from "@inertiajs/inertia-vue3";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import route from "ziggy";
import FormSelect from "@/Components/FormSelect.vue";
import { computed, ref } from "vue";
import { Inertia } from "@inertiajs/inertia";
import FormError from "@/Components/FormError.vue";
import Label from "@/Components/Label.vue";
import FormInput from "@/Components/FormInput.vue";
import { TabGroup, TabList, Tab, TabPanels, TabPanel } from "@headlessui/vue";
import { QuillEditor } from "@vueup/vue-quill";
import vueFilePond from "vue-filepond";
import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type";
import FilePondPluginImagePreview from "filepond-plugin-image-preview";
import AttachmentUploadItem from "@/Components/AttachmentUploadItem.vue";
import GalleryUploader from "@/Components/GalleryUploader.vue";
import useLanguageObject from "@/Composable/useLanguageObject";
import useImageUpload from "@/Composable/useImageUpload";
import ProjectIconUploader from "@/Components/ProjectIconUploader.vue";

import "@vueup/vue-quill/dist/vue-quill.snow.css";
import "filepond/dist/filepond.min.css";
import "filepond-plugin-image-preview/dist/filepond-plugin-image-preview.min.css";

const props = defineProps({});

const FilePond = vueFilePond(
    FilePondPluginFileValidateType,
    FilePondPluginImagePreview
);

const filePond = ref(null);
const iconUploader = ref(null);
const galleryUploader = ref(null);

const { languages, languageObject } = useLanguageObject();

const form = useForm({
    name: { ...languageObject },
    content: { ...languageObject },
    excerpt: { ...languageObject },
    thumbnail: null,
    sort: 0,
    icon: null,
    gallery: [], // Will store gallery images
});

function submit() {
    const thumbnail = filePond.value.getFile()?.file;

    // Get icon file from our custom uploader
    const iconFile = iconUploader.value?.getFile();

    // Get gallery images from the gallery uploader component
    const galleryFiles = galleryUploader.value
        ? galleryUploader.value.getFiles()
        : [];

    form.transform((data) => ({
        ...data,
        thumbnail,
        icon: iconFile,
        gallery: galleryFiles,
    })).post(route("admin.projects.store"), {
        forceFormData: true,
        preserveScroll: true,
        preserveState: true,
        onSuccess: () => {
            form.reset();
            if (galleryUploader.value) {
                galleryUploader.value.clearPendingUploads();
            }
        },
    });
}

function cancel() {
    Inertia.visit(route("admin.projects.index"));
}

function onAttachmentFileChange(lang, file) {
    form.attachments[lang] = file;
}
const { modules } = useImageUpload("projects");

function handleIconUpdated(data) {
    form.icon = data.file;
}
</script>
<template>
    <div class="max-w-7xl mx-auto">
        <div class="mb-6 lg:mb-10">
            <h3 class="text-lg font-medium leading-6 text-gray-900">
                Add a project
            </h3>
        </div>
        <form @submit.prevent="submit" class="space-y-6 lg:space-y-8">
            <div class="flex flex-col lg:flex-row gap-6">
                <div class="lg:w-9/12">
                    <TabGroup>
                        <TabList
                            class="flex space-x-1 rounded-t-xl rounded-tr-xl bg-white p-1 max-w-lg shadow-sm"
                        >
                            <Tab
                                v-for="{ name } in languages"
                                as="template"
                                :key="name"
                                v-slot="{ selected }"
                            >
                                <button
                                    :class="[
                                        'w-full rounded-lg py-2.5 text-sm font-medium leading-5 uppercase',
                                        'ring-white ring-opacity-60 ring-offset-2 ring-offset-primary-400 focus:outline-none focus:ring-2',
                                        selected
                                            ? 'bg-gray-100 text-primary-700 font-bold'
                                            : 'text-gray-600 hover:bg-gray-50',
                                    ]"
                                >
                                    {{ name }}
                                </button>
                            </Tab>
                        </TabList>

                        <TabPanels>
                            <TabPanel
                                v-for="{
                                    iso_code_name,
                                    direction,
                                    name,
                                } in languages"
                                :key="iso_code_name"
                                :class="[
                                    'rounded-b-xl rounded-tr-xl bg-white p-4 shadow-sm space-y-5',
                                    'ring-white ring-opacity-60 focus:outline-none',
                                    `content content-${direction}`,
                                ]"
                            >
                                <FormInput
                                    label="Sort Order"
                                    type="number"
                                    v-model="form.sort"
                                    :error="form.errors.sort"
                                    class="w-32"
                                />
                                <FormInput
                                    :label="`Name (${name})`"
                                    v-model="form.name[iso_code_name]"
                                    autofocus
                                    :error="
                                        form.errors[`name.${iso_code_name}`]
                                    "
                                />
                                <div
                                    :class="{
                                        'input-error':
                                            !!form.errors[
                                                `content.${iso_code_name}`
                                            ],
                                    }"
                                >
                                    <FormInput
                                        :label="`Excerpt (${name})`"
                                        v-model="form.excerpt[iso_code_name]"
                                        type="textarea"
                                        rows="4"
                                        :error="
                                            form.errors[
                                                `excerpt.${iso_code_name}`
                                            ]
                                        "
                                    />
                                </div>

                                <div
                                    :class="{
                                        'input-error':
                                            !!form.errors[
                                                `content.${iso_code_name}`
                                            ],
                                    }"
                                >
                                    <Label
                                        class="mb-2"
                                        :for="`editor-${direction}`"
                                        >Content ({{ name }})</Label
                                    >
                                    <QuillEditor
                                        :modules="modules"
                                        toolbar="full"
                                        :id="`editor-${direction}`"
                                        v-model:content="
                                            form.content[iso_code_name]
                                        "
                                        theme="snow"
                                        content-type="html"
                                    />
                                    <FormError
                                        class="mt-1"
                                        v-if="
                                            form.errors[
                                                `content.${iso_code_name}`
                                            ]
                                        "
                                        >{{
                                            form.errors[
                                                `content.${iso_code_name}`
                                            ]
                                        }}</FormError
                                    >
                                </div>
                            </TabPanel>
                        </TabPanels>
                    </TabGroup>
                </div>
                <aside class="lg:w-3/12">
                    <div class="mb-8">
                        <Label>Thumbnail</Label>
                        <FilePond
                            class="mt-2 filepond-image-uploader"
                            :class="{ 'input-error': form.errors['thumbnail'] }"
                            label-idle="Drag & Drop thumbnail or <span class='underline'>Browse</span>"
                            ref="filePond"
                            accepted-file-types="image/jpeg, image/png"
                        />
                        <FormError
                            class="mt-1"
                            v-if="form.errors['thumbnail']"
                            >{{ form.errors["thumbnail"] }}</FormError
                        >
                    </div>

                    <div class="mb-8">
                        <ProjectIconUploader
                            ref="iconUploader"
                            :error="form.errors['icon']"
                            @iconUpdated="handleIconUpdated"
                        />
                    </div>
                </aside>
            </div>

            <div class="mt-8">
                <GalleryUploader
                    ref="galleryUploader"
                    :error="form.errors.gallery"
                    @imagesUpdated="(files) => (form.gallery = files)"
                />
            </div>

            <div class="flex gap-4">
                <PrimaryButton
                    class="px-5 py-2.5 text-base"
                    :loading="form.processing"
                    :disabled="form.processing"
                    @click="submit"
                    >Add</PrimaryButton
                >
                <SecondaryButton
                    class="px-5 py-2.5 text-base"
                    :disabled="form.processing"
                    @click="cancel"
                    >Cancel
                </SecondaryButton>
            </div>
        </form>
    </div>
</template>
