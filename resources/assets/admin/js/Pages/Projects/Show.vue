<template>
  <div class="max-w-7xl mx-auto">
    <div class="mb-6 lg:mb-10 flex items-center justify-between">
      <div>
        <h3 class="text-2xl font-medium leading-6 text-gray-900">
          {{ project.name.en || project.name[Object.keys(project.name)[0]] }}
        </h3>
        <p class="mt-1 text-sm text-gray-500">
          Created {{ formatDate(project.created_at) }}
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <Link
          :href="route('admin.projects.edit', project.id)"
          class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          Edit Project
        </Link>
        <Link
          :href="route('admin.projects.index')"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Back to Projects
        </Link>
      </div>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
      <!-- Project Information -->
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Project Information</h3>
      </div>
      <div class="border-t border-gray-200">
        <dl>
          <!-- Project Name -->
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Name</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              <div v-for="(value, lang) in project.name" :key="lang" class="mb-1">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-2">
                  {{ lang.toUpperCase() }}
                </span>
                {{ value }}
              </div>
            </dd>
          </div>

          <!-- Project Excerpt -->
          <div v-if="hasContent(project.excerpt)" class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Excerpt</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              <div v-for="(value, lang) in project.excerpt" :key="lang" class="mb-2" v-if="value">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-2">
                  {{ lang.toUpperCase() }}
                </span>
                <p class="mt-1">{{ value }}</p>
              </div>
            </dd>
          </div>

          <!-- Thumbnail -->
          <div v-if="thumbnail" class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Thumbnail</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              <img :src="thumbnail.original_url" alt="Project thumbnail" class="h-32 w-32 object-cover rounded-lg">
            </dd>
          </div>

          <!-- Project Icon -->
          <div v-if="icon" class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Project Icon</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              <img :src="icon.original_url" alt="Project icon" class="h-16 w-16 object-contain rounded-lg bg-gray-50 p-1">
            </dd>
          </div>
        </dl>
      </div>
    </div>

    <!-- Project Content -->
    <div v-if="hasContent(project.content)" class="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Project Content</h3>
      </div>
      <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <div v-for="(content, lang) in project.content" :key="lang" class="mb-6" v-if="content">
          <h4 class="text-md font-medium text-gray-900 mb-2">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-2">
              {{ lang.toUpperCase() }}
            </span>
            Content
          </h4>
          <div class="prose max-w-none" v-html="content"></div>
        </div>
      </div>
    </div>

    <!-- Gallery -->
    <div v-if="gallery && gallery.length > 0" class="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Gallery ({{ gallery.length }} images)</h3>
      </div>
      <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
        <div class="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4">
          <div v-for="image in gallery" :key="image.id" class="relative">
            <img :src="image.original_url" :alt="image.name" class="h-24 w-full object-cover rounded-lg">
          </div>
        </div>
      </div>
    </div>

    <!-- Project Maps -->
    <div v-if="maps && maps.length > 0" class="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6 flex items-center justify-between">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Project Maps ({{ maps.length }})</h3>
        <Link
          :href="route('admin.project-maps.create', { project_id: project.id })"
          class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <svg class="-ml-0.5 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Add Map
        </Link>
      </div>
      <div class="border-t border-gray-200">
        <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 p-4">
          <div
            v-for="map in maps"
            :key="map.id"
            class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            <div class="flex items-center justify-between mb-2">
              <h4 class="text-lg font-medium text-gray-900">{{ map.name }}</h4>
              <span
                :class="[
                  'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                  map.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                ]"
              >
                {{ map.is_active ? 'Active' : 'Inactive' }}
              </span>
            </div>
            <p v-if="map.description" class="text-sm text-gray-600 mb-3">{{ map.description }}</p>
            <div class="flex items-center justify-between text-sm text-gray-500">
              <div>
                <span class="capitalize">{{ map.type === 'single' ? 'Single Map' : 'Multi-City' }}</span>
                <span v-if="map.type === 'single'" class="ml-2">{{ map.markers_count }} markers</span>
                <span v-if="map.type === 'multi_city'" class="ml-2">{{ map.cities_count }} cities</span>
              </div>
              <div class="flex items-center space-x-2">
                <Link
                  :href="route('admin.project-maps.show', map.id)"
                  class="text-indigo-600 hover:text-indigo-900 font-medium"
                >
                  View
                </Link>
                <Link
                  :href="route('admin.project-maps.edit', map.id)"
                  class="text-indigo-600 hover:text-indigo-900 font-medium"
                >
                  Edit
                </Link>
              </div>
            </div>
            <p class="text-xs text-gray-400 mt-2">Created {{ map.created_at }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State for Maps -->
    <div v-else class="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Project Maps</h3>
      </div>
      <div class="border-t border-gray-200 px-4 py-5 sm:px-6 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m0 0L9 7"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No maps</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by creating a new map for this project.</p>
        <div class="mt-6">
          <Link
            :href="route('admin.project-maps.create', { project_id: project.id })"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Map
          </Link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Link } from '@inertiajs/inertia-vue3';
import route from 'ziggy';

const props = defineProps({
  project: {
    type: Object,
    required: true,
  },
  thumbnail: {
    type: Object,
    default: null,
  },
  icon: {
    type: Object,
    default: null,
  },
  gallery: {
    type: Array,
    default: () => ([]),
  },
  maps: {
    type: Array,
    default: () => ([]),
  },
});

function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
}

function hasContent(obj) {
  if (!obj) return false;
  return Object.values(obj).some(value => value && value.trim().length > 0);
}
</script>

<style scoped>
.prose {
  @apply text-gray-900;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  @apply text-gray-900 font-medium;
}

.prose p {
  @apply mb-4;
}

.prose ul, .prose ol {
  @apply mb-4 ml-6;
}

.prose li {
  @apply mb-1;
}

.prose a {
  @apply text-indigo-600 hover:text-indigo-800;
}
</style>
