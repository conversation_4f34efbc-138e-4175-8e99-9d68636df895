<script setup>
import { useForm } from "@inertiajs/inertia-vue3";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import route from "ziggy";
import { ref } from "vue";
import { Inertia } from "@inertiajs/inertia";
import FormError from "@/Components/FormError.vue";
import Label from "@/Components/Label.vue";
import FormInput from "@/Components/FormInput.vue";
import { TabGroup, TabList, Tab, TabPanels, TabPanel } from "@headlessui/vue";
import { QuillEditor } from "@vueup/vue-quill";
import vueFilePond from "vue-filepond";
import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type";
import FilePondPluginImagePreview from "filepond-plugin-image-preview";
import GalleryUploader from "@/Components/GalleryUploader.vue";
import ProjectIconUploader from "@/Components/ProjectIconUploader.vue";
import useLanguageObject from "@/Composable/useLanguageObject";
import useImageUpload from "@/Composable/useImageUpload";

import "@vueup/vue-quill/dist/vue-quill.snow.css";
import "filepond/dist/filepond.min.css";
import "filepond-plugin-image-preview/dist/filepond-plugin-image-preview.min.css";

const FilePond = vueFilePond(
    FilePondPluginFileValidateType,
    FilePondPluginImagePreview
);

const filePond = ref(null);
const iconUploader = ref(null);
const galleryUploader = ref(null);
const files = ref([]);

const props = defineProps({
    project: {
        type: Object,
        default: () => ({}),
    },
    thumbnail: {
        type: Object,
        default: null,
    },
    icon: {
        type: Object,
        default: null,
    },
    gallery: {
        type: Array,
        default: () => [],
    },
});

const { languages, languageObject } = useLanguageObject();

const form = useForm({
    name: { ...languageObject, ...props.project.name },
    content: { ...languageObject, ...props.project.content },
    excerpt: { ...languageObject, ...props.project.excerpt },
    thumbnail: props.project.thumbnail,
    icon: props.project.icon,
    sort: props.project.sort || 0,
    gallery: [],
    attachments: { ...languageObject },
    thumbnailHasChanged: false,
    iconHasChanged: false,
});

function submit() {
    const thumbnail = filePond.value?.getFile()?.file;

    const iconFile = iconUploader.value?.getFile();
    const iconHasChanged = iconUploader.value?.hasChanged() || false;
    form.iconHasChanged = iconHasChanged;

    const galleryFiles = galleryUploader.value
        ? galleryUploader.value.getFiles()
        : [];

    form.transform((data) => ({
        ...data,
        thumbnail,
        icon: iconFile,
        gallery: galleryFiles,
        _method: "PUT",
    })).post(route("admin.projects.update", props.project.id), {
        forceFormData: true,
        preserveScroll: true,
        preserveState: true,
        onSuccess: () => {
            if (galleryUploader.value) {
                galleryUploader.value.clearPendingUploads();
            }
        },
    });
}

function cancel() {
    Inertia.visit(route("admin.projects.index"));
}
function changeThumbnail() {
    form.thumbnailHasChanged = true;
}

function handleIconUpdated(data) {
    form.icon = data.file;
    form.iconHasChanged = data.hasChanged;
}

function handleImageDeleted(imageId) {
    form.gallery = form.gallery.filter((image) => image.id !== imageId);
}

function handleImagesUpdated(files) {
    form.gallery = files;
}

async function handleExistingImageDescriptionUpdated(data) {
    try {
        await Inertia.patch(route('admin.projects.media.update-description', { media: data.id }), {
            description_en: data.descriptions.description_en,
            description_ku: data.descriptions.description_ku,
            description_ar: data.descriptions.description_ar,
        }, {
            preserveScroll: true,
            preserveState: true,
            onSuccess: () => {
                // Optionally show success message
                console.log('Image description updated successfully');
            },
            onError: (errors) => {
                console.error('Failed to update image description:', errors);
            }
        });
    } catch (error) {
        console.error('Error updating image description:', error);
    }
}

const { modules } = useImageUpload("projects");

</script>
<template>
    <div class="max-w-6xl mx-auto">
        <div class="mb-6 lg:mb-10">
            <h3 class="text-lg font-medium leading-6 text-gray-900">
                Edit project
            </h3>
        </div>
        <form @submit.prevent="submit" class="space-y-6 lg:space-y-8">
            <div class="flex flex-col lg:flex-row gap-6">
                <div class="lg:w-8/12">
                    <TabGroup>
                        <TabList
                            class="flex space-x-1 rounded-t-xl rounded-tr-xl bg-white p-1 max-w-lg shadow-sm"
                        >
                            <Tab
                                v-for="{ name } in languages"
                                as="template"
                                :key="name"
                                v-slot="{ selected }"
                            >
                                <button
                                    :class="[
                                        'w-full rounded-lg py-2.5 text-sm font-medium leading-5 uppercase',
                                        'ring-white ring-opacity-60 ring-offset-2 ring-offset-primary-400 focus:outline-none focus:ring-2',
                                        selected
                                            ? 'bg-gray-100 text-primary-700 font-bold'
                                            : 'text-gray-600 hover:bg-gray-50',
                                    ]"
                                >
                                    {{ name }}
                                </button>
                            </Tab>
                        </TabList>

                        <TabPanels>
                            <TabPanel
                                v-for="{
                                    iso_code_name,
                                    direction,
                                    name,
                                } in languages"
                                :key="iso_code_name"
                                :class="[
                                    'rounded-b-xl rounded-tr-xl bg-white p-4 shadow-sm space-y-5',
                                    'ring-white ring-opacity-60 focus:outline-none',
                                    `content content-${direction}`,
                                ]"
                            >
                              <FormInput
                                    label="Sort Order"
                                    type="number"
                                    v-model="form.sort"
                                    :error="form.errors.sort"
                                    class="w-32"
                                />

                                <FormInput
                                    :label="`Name (${name})`"
                                    v-model="form.name[iso_code_name]"
                                    autofocus
                                    :error="
                                        form.errors[`name.${iso_code_name}`]
                                    "
                                />

                                <div
                                    :class="{
                                        'input-error':
                                            !!form.errors[
                                                `content.${iso_code_name}`
                                            ],
                                    }"
                                >
                                    <FormInput
                                        :label="`Excerpt (${name})`"
                                        v-model="form.excerpt[iso_code_name]"
                                        type="textarea"
                                        rows="4"
                                        :error="
                                            form.errors[
                                                `excerpt.${iso_code_name}`
                                            ]
                                        "
                                    />
                                </div>

                                <div
                                    :class="{
                                        'input-error':
                                            !!form.errors[
                                                `content.${iso_code_name}`
                                            ],
                                    }"
                                >
                                    <Label
                                        class="mb-2"
                                        :for="`editor-${direction}`"
                                        >Content ({{ name }})</Label
                                    >
                                    <QuillEditor
                                        :modules="modules"
                                        toolbar="full"
                                        :id="`editor-${direction}`"
                                        v-model:content="
                                            form.content[iso_code_name]
                                        "
                                        theme="snow"
                                        content-type="html"
                                    />
                                    <FormError
                                        class="mt-1"
                                        v-if="
                                            form.errors[
                                                `content.${iso_code_name}`
                                            ]
                                        "
                                        >{{
                                            form.errors[
                                                `content.${iso_code_name}`
                                            ]
                                        }}</FormError
                                    >
                                </div>
                            </TabPanel>
                        </TabPanels>
                    </TabGroup>
                </div>
                <aside class="lg:w-4/12">
                    <div class="mb-8">
                        <div v-if="props.thumbnail" class="mb-6">
                            <Label>Current Thumbnail</Label>
                            <img
                                :src="props.thumbnail.original_url"
                                class="mt-1 w-full h-48 object-cover border border-white rounded-xl overflow-hidden"
                                alt=""
                            />
                        </div>
                        <Label>Upload Thumbnail</Label>
                        <FilePond
                            class="mt-2 filepond-image-uploader"
                            :class="{ 'input-error': form.errors['thumbnail'] }"
                            label-idle="Drag & Drop thumbnail or <span class='underline'>Browse</span>"
                            ref="filePond"
                            @updatefiles="changeThumbnail"
                            accepted-file-types="image/jpeg, image/png"
                        />
                        <FormError
                            class="mt-1"
                            v-if="form.errors['thumbnail']"
                            >{{ form.errors["thumbnail"] }}</FormError
                        >
                    </div>

                    <div class="mb-8">
                        <ProjectIconUploader
                            ref="iconUploader"
                            :existingIcon="props.icon"
                            :error="form.errors['icon']"
                            @iconUpdated="handleIconUpdated"
                        />
                    </div>
                </aside>
            </div>

            <div class="mt-8">
                <GalleryUploader
                    ref="galleryUploader"
                    :existingImages="gallery"
                    :error="form.errors.gallery"
                    @imagesUpdated="handleImagesUpdated"
                    @imageDeleted="handleImageDeleted"
                    @existingImageDescriptionUpdated="handleExistingImageDescriptionUpdated"
                />
            </div>

            <div class="flex gap-4">
                <PrimaryButton
                    class="px-5 py-2.5 text-base"
                    :loading="form.processing"
                    :disabled="form.processing"
                    @click="submit"
                    >Update</PrimaryButton
                >
                <SecondaryButton
                    class="px-5 py-2.5 text-base"
                    :disabled="form.processing"
                    @click="cancel"
                    >Cancel
                </SecondaryButton>
            </div>
        </form>
    </div>
</template>
