<script setup>
import Modal from '@/Components/Modal.vue';
import { useForm } from '@inertiajs/inertia-vue3';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import route from 'ziggy';
import FormInput from '../../Components/FormInput.vue';
import useLanguageObject from '@/Composable/useLanguageObject';

const { languages, languageObject } = useLanguageObject();

const form = useForm({
  name: { ...languageObject },
});

function submit() {
  form.post(route('admin.categories.store'), {
    preserveScroll: true,
    preserveState: true,
    onSuccess: () => form.reset('name.*'),
  });
}

</script>  
<template>
  <Modal width="2xl" title="Create category">
    <form @submit.prevent="submit" class="space-y-5">
      <template v-for="{ iso_code_name, name} in languages" :key="iso_code_name">
        <FormInput :label="`Name (${name})`" type="text" v-model="form.name[iso_code_name]"
              :error="form.errors[`name.${iso_code_name}`]" />
      </template>
    </form>

    <template #footer="{ close }">
      <PrimaryButton :loading="form.processing" :disabled="form.processing" @click="submit">Create</PrimaryButton>
      <SecondaryButton :disabled="form.processing" @click="close">Close</SecondaryButton>
    </template>
  </Modal>
</template>
