<script setup>
  import Modal from '@/Components/Modal.vue';
  import { useForm } from '@inertiajs/inertia-vue3';
  import PrimaryButton from '@/Components/PrimaryButton.vue';
  import SecondaryButton from '@/Components/SecondaryButton.vue';
  import route from 'ziggy';
  import FormInput from '@/Components/FormInput.vue';
  import useLanguageObject from '@/Composable/useLanguageObject';

const { languages, languageObject } = useLanguageObject();
  
  const props = defineProps({
    category: {
      type: Object,
      default: () => ({}),
    },
  });
  
  const form = useForm({
    name: {
      ...languageObject,
      ...props.category.name,
    },
  })

  function submit() {
    form.put(route('admin.categories.update', props.category.id), {
      preserveScroll: true,
      preserveState: true,
    });
  }
  
  </script>  
  <template>
    <Modal width="2xl" title="Edit Category">
      <form @submit.prevent="submit" class="space-y-5">
        <template v-for="{ iso_code_name, name} in languages" :key="iso_code_name">
          <FormInput :label="`Name (${name})`" type="text" v-model="form.name[iso_code_name]"
                :error="form.errors[`name.${iso_code_name}`]" />
        </template>
      </form>
  
      <template #footer="{ close }">
        <PrimaryButton :loading="form.processing" :disabled="form.processing" @click="submit">Update</PrimaryButton>
        <SecondaryButton @click="close">Close</SecondaryButton>
      </template>
    </Modal>
  </template>
  