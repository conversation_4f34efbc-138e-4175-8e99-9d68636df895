<script setup>
import { Inertia } from '@inertiajs/inertia';
import route from 'ziggy';
import PageHeading from '@/Components/PageHeading.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextButton from '@/Components/TextButton.vue';
import Pagination from '@/Components/Pagination.vue'
import SearchInput from '@/Components/SearchInput.vue';
import { ref, watch } from 'vue';
import { usePage } from '@inertiajs/inertia-vue3';
import DeleteDialog from '@/Components/DeleteDialog.vue';

defineProps({
  categories: {
    type: Object,
    default: () => ({})
  },
});

const isDeleteDialogOpen = ref(false);
const deletableRowId = ref(null);

const search = ref(usePage().props.value.search);

watch(search, (value) => {
  Inertia.visit(route('admin.categories.index'), { data: { 'search': value }, preserveState: true, preserveScroll: true, });
})

function create() {
  Inertia.visit(route('admin.categories.create'), { only: ['modal', 'flash'] });
}

function tryDelete(id) {
  deletableRowId.value = id;
  isDeleteDialogOpen.value = true;
}

function editRow(item) {
  Inertia.visit(route('admin.categories.edit', item.id), { preserveScroll: true, preserveState: true })
}

async function closeDeleteDialog() {
  isDeleteDialogOpen.value = false;
  deletableRowId.value = null;
}
</script>

<template>
  <PageHeading heading="Categories">
    <template #actions>
      <div class="space-x-3">
        <SearchInput placeholder="Search by name" v-model="search"/>
        <PrimaryButton @click="create">Create</PrimaryButton>
      </div>
    </template>
  </PageHeading>

  <div class="mt-8 flex flex-col">
    <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">ID
                </th>

                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Name
                </th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Delete,Edit</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 bg-white">
              <tr v-for="item in categories.data" :key="item.id">
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                  {{ item.id }}
                </td>
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900">
                  {{ item.name }}
                </td>
                <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6 space-x-2">
                  <TextButton color="primary" v-if="item.can_edit" @click="editRow(item)">Edit</TextButton>
                  <TextButton color="red" v-if="item.can_delete" @click="tryDelete(item.id)" class="text-red-500">Delete
                  </TextButton>
                </td>
              </tr>
            </tbody>
          </table>
          <Pagination :pages="categories.meta.links" :links="categories.links"
            :currnet-page="categories.meta.current_page" :from="categories.meta.from" :to="categories.meta.to"
            :total="categories.meta.total" />
        </div>
      </div>
    </div>
  </div>

  <DeleteDialog row-name="Category" route="admin.categories.destroy" :row-id="deletableRowId" :open="isDeleteDialogOpen" @close="closeDeleteDialog"/>
</template>
