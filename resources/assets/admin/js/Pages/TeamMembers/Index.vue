<script setup>
import { ref, watch } from 'vue';
import route from 'ziggy';
import { Inertia } from '@inertiajs/inertia';
import { usePage } from '@inertiajs/inertia-vue3';
import PageHeading from '@/Components/PageHeading.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextButton from '@/Components/TextButton.vue';
import Pagination from '@/Components/Pagination.vue'
import DeleteDialog from '@/Components/DeleteDialog.vue';
import SearchInput from '@/Components/SearchInput.vue';

defineProps({
  teamMembers: {
    type: Object,
    default: () => ({})
  },
  filterOptions: Object,
});

const isDeleteDialogOpen = ref(false);
const deletableRowId = ref(null);

const filters = ref(usePage().props.value.filters);

watch(filters, (value) => {
  Inertia.visit(route('admin.team-members.index'), {
    data: value,
    preserveState: true,
    preserveScroll: true,
    only: ['modal', 'flash', 'teamMembers', 'filters']
  });
}, { deep: true })

function create() {
  Inertia.visit(route('admin.team-members.create'), { only: ['modal', 'flash',] });
}

function tryDelete(id) {
  deletableRowId.value = id;
  isDeleteDialogOpen.value = true;
}

function editRow(item) {
  Inertia.visit(route('admin.team-members.edit', item.id), { preserveScroll: true, preserveState: true })
}

async function closeDeleteDialog() {
  isDeleteDialogOpen.value = false;
  deletableRowId.value = null;
}
</script>

<template>
  <PageHeading heading="Team Members">
    <template #actions>
      <div class="space-x-3 flex items-end">
        <SearchInput placeholder="Search" v-model="filters.search" />
        <PrimaryButton @click="create">Create</PrimaryButton>
      </div>
    </template>
  </PageHeading>

  <div class="mt-8 flex flex-col">
    <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">ID</th>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Name</th>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Department</th>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Role</th>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Email</th>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Created At
                </th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Delete,Edit</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 bg-white">
              <tr v-for="item in teamMembers.data" :key="item.id">
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                  {{ item.id }}
                </td>
                <td class="whitespace-nowrap max-w-[20rem] py-4 pl-4 pr-3 text-sm font-medium text-gray-900">
                  <span class="truncate block">{{ item.name }}</span>
                </td>
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm text-gray-500">
                  {{ item.department }}
                </td>
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900">
                  {{ item.role }}
                </td>
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm text-gray-500">
                  {{ item.email }}
                </td>
                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ item.created_at }}</td>
                <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6 space-x-2">
                  <TextButton color="primary" v-if="item.can_edit" @click="editRow(item)">Edit</TextButton>
                  <TextButton color="red" v-if="item.can_delete" @click="tryDelete(item.id)" class="text-red-500">Delete
                  </TextButton>
                </td>
              </tr>
            </tbody>
          </table>
          <Pagination :pages="teamMembers.meta.links" :links="teamMembers.links"
            :currnet-page="teamMembers.meta.current_page" :from="teamMembers.meta.from" :to="teamMembers.meta.to"
            :total="teamMembers.meta.total" />
        </div>
      </div>
    </div>
  </div>

  <DeleteDialog row-name="Team Member" route="admin.team-members.destroy" :row-id="deletableRowId"
    :open="isDeleteDialogOpen" @close="closeDeleteDialog" />
</template>
