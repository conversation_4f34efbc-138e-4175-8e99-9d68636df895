<script setup>
import { useForm } from "@inertiajs/inertia-vue3";
import PrimaryButton from "@/Components/PrimaryButton.vue";
import SecondaryButton from "@/Components/SecondaryButton.vue";
import route from "ziggy";
import { ref } from "vue";
import { Inertia } from "@inertiajs/inertia";
import FormError from "@/Components/FormError.vue";
import Label from "@/Components/Label.vue";
import FormInput from "@/Components/FormInput.vue";
import vueFilePond from "vue-filepond";
import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type";
import FilePondPluginImagePreview from "filepond-plugin-image-preview";
import useLanguageObject from "@/Composable/useLanguageObject";

import "@vueup/vue-quill/dist/vue-quill.snow.css";
import "filepond/dist/filepond.min.css";
import "filepond-plugin-image-preview/dist/filepond-plugin-image-preview.min.css";

const FilePond = vueFilePond(
    FilePondPluginFileValidateType,
    FilePondPluginImagePreview
);

const langs = useLanguageObject();

const filePond = ref(null);

const form = useForm({
    department: null,
    name: null,
    role: null,
    description: null,
    email: null,
    phone: null,
    photo: null,
});

function submit() {
    const photo = filePond.value.getFile()?.file;
    form.transform((data) => ({
        ...data,
        photo,
    })).post(route("admin.team-members.store"), {
        forceFormData: true,
        preserveScroll: true,
        preserveState: true,
        onSuccess: () => form.reset(),
    });
}

function cancel() {
    Inertia.visit(route("admin.team-members.index"));
}
</script>
<template>
    <div class="max-w-lg mx-auto">
        <div class="mb-6 lg:mb-10">
            <h3 class="text-lg font-medium leading-6 text-gray-900">
                Add a member
            </h3>
        </div>
        <form @submit.prevent="submit" class="space-y-6 lg:space-y-8">
            <div>
                <Label>Photo</Label>
                <FilePond
                    class="mt-2 filepond-image-uploader"
                    :class="{ 'input-error': form.errors.photo }"
                    label-idle="Drag & Drop photo or <span class='underline'>Browse</span>"
                    ref="filePond"
                    accepted-file-types="image/jpeg, image/png"
                />
                <FormError class="mt-1" v-if="form.errors.photo">{{
                    form.errors.photo
                }}</FormError>
            </div>
            <FormInput
                label="Department"
                v-model="form.department"
                autofocus
                :error="form.errors.department"
            />
            <FormInput
                label="Name"
                v-model="form.name"
                :error="form.errors.name"
            />
            <FormInput
                label="Role"
                v-model="form.role"
                :error="form.errors.role"
            />
            <!-- <FormInput
                type="textarea"
                label="Description"
                v-model="form.description"
                :error="form.errors.description"
            /> -->
            <FormInput
                label="Email"
                type="email"
                v-model="form.email"
                :error="form.errors.email"
            />
            <FormInput
                label="Phone"
                v-model="form.phone"
                :error="form.errors.phone"
            />

            <div class="flex gap-4">
                <PrimaryButton
                    class="px-5 py-2.5 text-base"
                    :loading="form.processing"
                    :disabled="form.processing"
                    @click="submit"
                    >Create</PrimaryButton
                >
                <SecondaryButton
                    class="px-5 py-2.5 text-base"
                    :disabled="form.processing"
                    @click="cancel"
                    >Cancel
                </SecondaryButton>
            </div>
        </form>
    </div>
</template>
