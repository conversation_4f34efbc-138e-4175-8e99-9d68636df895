<script setup>
import { Inertia } from '@inertiajs/inertia';
import route from 'ziggy';
import PageHeading from '@/Components/PageHeading.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';

const props = defineProps({
  jobVacancy: {
    type: Object,
    required: true
  },
  submission: {
    type: Object,
    required: true
  },
  cvFile: {
    type: Object,
    default: null
  }
});

function toggleReviewed() {
  Inertia.patch(route('admin.job-vacancies.submissions.toggle-reviewed', [props.jobVacancy.id, props.submission.id]), {}, {
    preserveScroll: true,
    preserveState: true,
  });
}

function goBack() {
  Inertia.visit(route('admin.job-vacancies.submissions.index', props.jobVacancy.id));
}

function downloadCV() {
  if (props.cvFile) {
    window.open(props.cvFile.original_url, '_blank');
  }
}
</script>

<template>
  <PageHeading :heading="`Submission Details`">
    <template #actions>
      <div class="space-x-3 flex items-end">
        <PrimaryButton 
          :class="submission.reviewed ? 'bg-yellow-600 hover:bg-yellow-700' : 'bg-green-600 hover:bg-green-700'"
          @click="toggleReviewed"
        >
          {{ submission.reviewed ? 'Mark as Unread' : 'Mark as Read' }}
        </PrimaryButton>
        <SecondaryButton @click="goBack">
          Back to Submissions
        </SecondaryButton>
      </div>
    </template>
  </PageHeading>

  <div class="mt-8">
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
          Application for: {{ jobVacancy.title }}
        </h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">
          Submitted on {{ submission.created_at }}
        </p>
      </div>
      <div class="border-t border-gray-200">
        <dl>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">
              Full Name
            </dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              {{ submission.full_name }}
            </dd>
          </div>
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">
              Email Address
            </dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              <a :href="`mailto:${submission.email}`" class="text-blue-600 hover:text-blue-800">
                {{ submission.email }}
              </a>
            </dd>
          </div>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">
              Status
            </dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              <span v-if="submission.reviewed" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                Reviewed
              </span>
              <span v-else class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                Pending Review
              </span>
            </dd>
          </div>
          <div v-if="submission.summary" class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">
              Summary
            </dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              <div class="whitespace-pre-wrap">{{ submission.summary }}</div>
            </dd>
          </div>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">
              CV/Resume
            </dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              <div v-if="cvFile" class="flex items-center">
                <svg class="flex-shrink-0 h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
                </svg>
                <span class="ml-2 flex-1 w-0 truncate">
                  {{ cvFile.name }}
                </span>
                <div class="ml-4 flex-shrink-0">
                  <button @click="downloadCV" class="font-medium text-indigo-600 hover:text-indigo-500">
                    Download
                  </button>
                </div>
              </div>
              <div v-else class="text-gray-500">
                No CV uploaded
              </div>
            </dd>
          </div>
        </dl>
      </div>
    </div>
  </div>
</template>
