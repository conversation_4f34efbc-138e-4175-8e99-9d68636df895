<script setup>
import { ref, watch } from 'vue';
import route from 'ziggy';
import { Inertia } from '@inertiajs/inertia';
import { usePage } from '@inertiajs/inertia-vue3';
import PageHeading from '@/Components/PageHeading.vue';
import TextButton from '@/Components/TextButton.vue';
import Pagination from '@/Components/Pagination.vue'
import DeleteDialog from '@/Components/DeleteDialog.vue';
import SearchInput from '@/Components/SearchInput.vue';

const props = defineProps({
  jobVacancy: {
    type: Object,
    required: true
  },
  submissions: {
    type: Object,
    default: () => ({})
  },
  filters: Object,
});

const isDeleteDialogOpen = ref(false);
const deletableRowId = ref(null);

const filters = ref(usePage().props.value.filters);

watch(filters, (value) => {
  Inertia.visit(route('admin.job-vacancies.submissions.index', props.jobVacancy.id), {
    data: value,
    preserveState: true,
    preserveScroll: true,
    only: ['modal', 'flash', 'submissions', 'filters']
  });
}, { deep: true })

function viewSubmission(submission) {
  Inertia.visit(route('admin.job-vacancies.submissions.show', [props.jobVacancy.id, submission.id]));
}

function toggleReviewed(submission) {
  Inertia.patch(route('admin.job-vacancies.submissions.toggle-reviewed', [props.jobVacancy.id, submission.id]), {}, {
    preserveScroll: true,
    preserveState: true,
  });
}

function tryDelete(id) {
  deletableRowId.value = id;
  isDeleteDialogOpen.value = true;
}

function closeDeleteDialog() {
  isDeleteDialogOpen.value = false;
  deletableRowId.value = null;
}

function goBack() {
  Inertia.visit(route('admin.job-vacancies.index'));
}
</script>

<template>
  <PageHeading :heading="`Submissions for: ${jobVacancy.title}`">
    <template #actions>
      <div class="space-x-3 flex items-end">
        <SearchInput placeholder="Search submissions" v-model="filters.search" />
        <button @click="goBack" class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
          Back to Vacancies
        </button>
      </div>
    </template>
  </PageHeading>

  <div class="mt-8 flex flex-col">
    <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">ID</th>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Full Name</th>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Email</th>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">CV</th>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Status</th>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Submitted At</th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 bg-white">
              <tr v-for="submission in submissions.data" :key="submission.id">
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                  {{ submission.id }}
                </td>
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900">
                  {{ submission.full_name }}
                </td>
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm text-gray-500">
                  {{ submission.email }}
                </td>
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm text-gray-500">
                  <span v-if="submission.has_cv" class="text-green-600">✓ Uploaded</span>
                  <span v-else class="text-red-600">✗ No CV</span>
                </td>
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm">
                  <span v-if="submission.reviewed" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                    Reviewed
                  </span>
                  <span v-else class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                    Pending
                  </span>
                </td>
                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">{{ submission.created_at }}</td>
                <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6 space-x-2">
                  <TextButton color="primary" @click="viewSubmission(submission)">View</TextButton>
                  <TextButton 
                    :color="submission.reviewed ? 'yellow' : 'green'" 
                    @click="toggleReviewed(submission)"
                  >
                    {{ submission.reviewed ? 'Mark Unread' : 'Mark Read' }}
                  </TextButton>
                  <TextButton color="red" @click="tryDelete(submission.id)" class="text-red-500">Delete</TextButton>
                </td>
              </tr>
            </tbody>
          </table>
          <Pagination :pages="submissions.meta.links" :links="submissions.links"
            :currnet-page="submissions.meta.current_page" :from="submissions.meta.from" :to="submissions.meta.to"
            :total="submissions.meta.total" />
        </div>
      </div>
    </div>
  </div>

  <DeleteDialog 
    row-name="Submission" 
    :route="`admin.job-vacancies.submissions.destroy`" 
    :route-params="[jobVacancy.id, deletableRowId]"
    :row-id="deletableRowId"
    :open="isDeleteDialogOpen" 
    @close="closeDeleteDialog" 
  />
</template>
