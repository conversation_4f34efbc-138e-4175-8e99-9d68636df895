<script setup>
import { useForm } from '@inertiajs/inertia-vue3';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import route from 'ziggy';
import { ref } from 'vue';
import { Inertia } from '@inertiajs/inertia';
import FormError from '@/Components/FormError.vue';
import Label from '@/Components/Label.vue';
import FormInput from '@/Components/FormInput.vue';
import { TabGroup, TabList, Tab, TabPanels, TabPanel } from '@headlessui/vue';
import vueFilePond from "vue-filepond";
import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type";
import FilePondPluginImagePreview from "filepond-plugin-image-preview";
import DeleteDialog from '@/Components/DeleteDialog.vue';
import useLanguageObject from '@/Composable/useLanguageObject';

import "filepond/dist/filepond.min.css";
import "filepond-plugin-image-preview/dist/filepond-plugin-image-preview.min.css";

const FilePond = vueFilePond(
  FilePondPluginFileValidateType,
  FilePondPluginImagePreview
);

const filePond = ref(null);
const files = ref([])

const props = defineProps({
  slide: {
    type: Object,
    default: () => ({}),
  },
  thumbnail: {
    type: Object,
    default: null,
  },
});

const { languages, languageObject } = useLanguageObject();

const form = useForm({
  title: { ...languageObject, ...props.slide.title },
  description: { ...languageObject, ...props.slide.description },
  link_text: { ...languageObject, ...props.slide.link_text },
  link: props.slide.link,
  thumbnail: props.thumbnail,
  thumbnailHasChanged: false,
});


function submit() {
  const thumbnail = filePond.value.getFile()?.file;
  form
    .transform((data) => ({
      ...data,
      thumbnail,
      _method: 'PUT'
    }))
    .post(route('admin.slides.update', props.slide.id), {
      forceFormData: true,
      preserveScroll: true,
      preserveState: true,
    });
}

function cancel() {
  Inertia.visit(route('admin.slides.index'));
}

function changeThumbnail() {
  form.thumbnailHasChanged = true;
}
</script>  
<template>
  <div class="max-w-6xl mx-auto">
    <div class="mb-6 lg:mb-10">
      <h3 class="text-lg font-medium leading-6 text-gray-900">Edit slide</h3>
    </div>
    <form @submit.prevent="submit" class="space-y-6 lg:space-y-8">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-12">
        <FormInput label="Link" v-model="form.link" autofocus :error="form.errors.link" placeholder="https://gulanpark-wtcerbil.com" />
      </div>
      <div class="flex flex-col lg:flex-row gap-6">
        <div class="lg:w-8/12">
          <TabGroup>
            <TabList class="flex space-x-1 rounded-t-xl rounded-tr-xl bg-white p-1 max-w-lg shadow-sm">
              <Tab v-for="{ name } in languages" as="template" :key="name" v-slot="{ selected }">
                <button :class="[
                  'w-full rounded-lg py-2.5 text-sm font-medium leading-5 uppercase',
                  'ring-white ring-opacity-60 ring-offset-2 ring-offset-primary-400 focus:outline-none focus:ring-2',
                  selected
                    ? 'bg-gray-100 text-primary-700 font-bold'
                    : 'text-gray-600 hover:bg-gray-50',
                ]">
                  {{ name }}
                </button>
              </Tab>
            </TabList>

            <TabPanels>
              <TabPanel v-for="{ iso_code_name, direction, name} in languages" :key="iso_code_name" :class="[
                'rounded-b-xl rounded-tr-xl bg-white p-4 shadow-sm space-y-5',
                'ring-white ring-opacity-60 focus:outline-none',
                `content content-${direction}`
              ]">
                <FormInput :label="`Title (${name})`" v-model="form.title[iso_code_name]"
                  :error="form.errors[`title.${iso_code_name}`]" />

                  <FormInput :label="`Link Text (${name})`" v-model="form.link_text[iso_code_name]"
                  :error="form.errors[`link_text.${iso_code_name}`]" />

                 <div :class="{ 'input-error' : !! form.errors[`content.${iso_code_name}`] }">
                  <FormInput 
                    :label="`Description (${name})`" 
                    v-model="form.description[iso_code_name]" type="textarea" rows="4"
                    :error="form.errors[`description.${iso_code_name}`]"
                   />
                </div>
              </TabPanel>
            </TabPanels>
          </TabGroup>

        </div>
        <aside class="lg:w-4/12">
          <div class="mb-8">
            <div v-if="props.thumbnail" class="mb-6">
              <Label>Current Image</Label>
              <img :src="props.thumbnail.original_url"
                class="mt-1 w-full h-48 object-cover border border-white rounded-xl overflow-hidden" alt="">
            </div>
            <Label>Upload Image</Label>
            <FilePond class="mt-2 filepond-image-uploader" :class="{'input-error': form.errors['thumbnail'] }"
              label-idle="Drag & Drop thumbnail or <span class='underline'>Browse</span>" ref="filePond"
              @updatefiles="changeThumbnail" accepted-file-types="image/jpeg, image/png" />
            <FormError class="mt-1" v-if="form.errors['thumbnail']">{{ form.errors['thumbnail'] }}</FormError>
          </div>
        </aside>
      </div>

      <div class="flex gap-4">
        <PrimaryButton class="px-5 py-2.5 text-base" :loading="form.processing" :disabled="form.processing"
          @click="submit">Update</PrimaryButton>
        <SecondaryButton class="px-5 py-2.5 text-base" :disabled="form.processing" @click="cancel">Cancel
        </SecondaryButton>
      </div>
    </form>
  </div>
</template>  