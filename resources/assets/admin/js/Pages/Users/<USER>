<script setup>
  import { useForm } from '@inertiajs/inertia-vue3';
  import PrimaryButton from '@/Components/PrimaryButton.vue';
  import SecondaryButton from '@/Components/SecondaryButton.vue';
  import route from 'ziggy';
  import FormInput from '@/Components/FormInput.vue';
  import FormSelect from '@/Components/FormSelect.vue';
  import { Inertia } from '@inertiajs/inertia';
  
  const { user, roles } = defineProps({
    user: {
      type: Object,
      default: () => ({})
    },
    roles: {
      type: Array,
      default: [],
    },
  });

  const form = useForm({ 
    ...user.data, 
    password: null, 
    password_confirmation: null,
    role_id: user.data.role_id || null,
  });
  
  function submit() {
    form
    .transform(data => ({
      id: user.data.id,
      ...data,
    }))
    .put(route('admin.users.update', user.data.id), {
      preserveScroll: true,
      preserveState: true,
    });
  }
  
  function cancel() {
    return Inertia.visit(route('admin.users.index'));
  }
  </script>  
  <template>
    <div class="max-w-xl mx-auto">
      <div class="mb-6 lg:mb-10">
        <h3 class="text-lg font-medium leading-6 text-gray-900">Edit user</h3>
      </div>
      <form @submit.prevent="submit" class="space-y-5">
        <FormInput autofocus label="Name" type="text" v-model="form.name" :error="form.errors[`name`]" />
        <FormInput label="Email" type="email" v-model="form.email" :error="form.errors[`email`]" />
        <FormInput label="Password" type="password" v-model="form.password" :error="form.errors[`password`]" />
        <FormInput label="Password confirmation" type="password" v-model="form.password_confirmation" :error="form.errors[`password`]" />
        
        <FormSelect 
          label="Role" 
          v-model="form.role_id" 
          :error="form.errors[`role_id`]"
          :options="roles.map(role => ({ value: role.id, label: role.name }))"
          placeholder="Select a role..."
        />
  
        <div class="flex gap-4">
          <PrimaryButton class="px-5 py-2.5 text-base" :loading="form.processing" :disabled="form.processing"
            @click="submit">Update</PrimaryButton>
          <SecondaryButton class="px-5 py-2.5 text-base" :disabled="form.processing" @click="cancel">Cancel
          </SecondaryButton>
        </div>
      </form>
    </div>
  </template>  