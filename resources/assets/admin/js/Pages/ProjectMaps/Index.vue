<template>
  <div>
    <Head title="Project Maps" />

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 bg-white border-b border-gray-200">
            
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
              <div>
                <h2 class="text-2xl font-bold text-gray-900">Project Maps</h2>
                <p class="text-gray-600 mt-1">Manage maps for your projects</p>
              </div>
              <Link
                :href="route('admin.project-maps.create')"
                class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition duration-150 ease-in-out"
              >
                <svg class="w-5 h-5 inline-block mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                </svg>
                Create Map
              </Link>
            </div>

            <!-- Filters -->
            <div class="mb-6 flex flex-col sm:flex-row gap-4">
              <div class="flex-1">
                <SearchInput
                  v-model="filters.search"
                  placeholder="Search maps..."
                  @search="search"
                />
              </div>
              <div class="sm:w-48">
                <select
                  v-model="filters.project_id"
                  @change="search"
                  class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                >
                  <option value="">All Projects</option>
                  <option v-for="project in projects" :key="project.id" :value="project.id">
                    {{ project.name }}
                  </option>
                </select>
              </div>
            </div>

            <!-- Maps Grid -->
            <div v-if="maps.data.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
              <div
                v-for="map in maps.data"
                :key="map.id"
                class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200"
              >
                <!-- Map Preview -->
                <div class="h-48 bg-gray-100 rounded-t-lg relative overflow-hidden">
                  <ProjectMapPreview
                    :map-data="map"
                    :cities="map.cities || []"
                    :markers="map.markers || []"
                    class="h-full"
                  />
                  <div class="absolute top-2 right-2">
                    <span
                      :class="[
                        'px-2 py-1 rounded-full text-xs font-medium',
                        map.is_active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      ]"
                    >
                      {{ map.is_active ? 'Active' : 'Inactive' }}
                    </span>
                  </div>
                </div>

                <!-- Map Info -->
                <div class="p-4">
                  <div class="flex justify-between items-start mb-2">
                    <h3 class="text-lg font-semibold text-gray-900 truncate">
                      {{ map.title || 'Untitled Map' }}
                    </h3>
                    <span
                      :class="[
                        'px-2 py-1 rounded text-xs font-medium ml-2',
                        map.type === 'single'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-purple-100 text-purple-800'
                      ]"
                    >
                      {{ map.type === 'single' ? 'Single Map' : 'Multi-City' }}
                    </span>
                  </div>
                  
                  <p class="text-sm text-gray-600 mb-3">
                    Project: {{ map.project?.name || 'Unknown Project' }}
                  </p>

                  <div class="flex justify-between items-center text-sm text-gray-500 mb-4">
                    <span v-if="map.type === 'single'">
                      {{ map.markers_count || 0 }} markers
                    </span>
                    <span v-else>
                      {{ map.cities_count || 0 }} cities
                    </span>
                    <span>
                      Updated {{ formatDate(map.updated_at) }}
                    </span>
                  </div>

                  <!-- Actions -->
                  <div class="flex justify-between items-center">
                    <div class="flex space-x-2">
                      <Link
                        :href="route('admin.project-maps.edit', map.id)"
                        class="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                      >
                        Edit
                      </Link>
                      <Link
                        :href="route('admin.project-maps.show', map.id)"
                        class="text-green-600 hover:text-green-900 text-sm font-medium"
                      >
                        View
                      </Link>
                    </div>
                    
                    <div class="flex space-x-2">
                      <button
                        @click="deleteMap(map)"
                        class="text-red-600 hover:text-red-900 text-sm font-medium"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div v-else class="text-center py-12">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m0 0L9 7" />
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">No maps found</h3>
              <p class="mt-1 text-sm text-gray-500">Get started by creating your first project map.</p>
              <div class="mt-6">
                <Link
                  :href="route('admin.project-maps.create')"
                  class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                >
                  <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                  </svg>
                  Create Map
                </Link>
              </div>
            </div>

            <!-- Pagination -->
            <div v-if="maps.data.length > 0" class="mt-6">
              <Pagination :links="maps.links" />
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Delete Dialog -->
    <DeleteDialog 
      row-name="Project Map" 
      route="admin.project-maps.destroy" 
      :row-id="deletableRowId"
      :open="isDeleteDialogOpen" 
      @close="closeDeleteDialog" 
    />
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Head, Link } from '@inertiajs/inertia-vue3'
import SearchInput from '@/Components/SearchInput.vue'
import Pagination from '@/Components/Pagination.vue'
import ProjectMapPreview from '@/Components/ProjectMapPreview.vue'
import DeleteDialog from '@/Components/DeleteDialog.vue'
import { Inertia } from '@inertiajs/inertia';

const router = Inertia;

const props = defineProps({
  maps: {
    type: Object,
    required: true
  },
  projects: {
    type: Array,
    default: () => []
  },
  filters: {
    type: Object,
    default: () => ({})
  }
})

const filters = reactive({
  search: props.filters.search || '',
  project_id: props.filters.project_id || ''
})

// Delete dialog state
const isDeleteDialogOpen = ref(false)
const deletableRowId = ref(null)

const search = () => {
  router.get(route('admin.project-maps.index'), filters, {
    preserveState: true,
    replace: true
  })
}

const deleteMap = (map) => {
  deletableRowId.value = map.id
  isDeleteDialogOpen.value = true
}

const closeDeleteDialog = () => {
  isDeleteDialogOpen.value = false
  deletableRowId.value = null
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString()
}
</script>
