<template>
  <div>
    <Head title="Edit Project Map"></Head>

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 bg-white border-b border-gray-200">
            
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
              <div>
                <h2 class="text-2xl font-bold text-gray-900">Edit Project Map</h2>
                <p class="text-gray-600 mt-1">Update your project map configuration</p>
              </div>
              <Link
                :href="route('admin.project-maps.index')"
                class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition duration-150 ease-in-out"
              >
                Back to Maps
              </Link>
            </div>

            <!-- Form -->
            <form @submit.prevent="submit" class="space-y-6">
              
              <!-- Basic Information -->
              <div class="bg-gray-50 p-6 rounded-lg">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  
                  <!-- Project Selection -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Project *</label>
                    <select
                      v-model="form.project_id"
                      required
                      class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                      :class="{ 'border-red-300': form.errors.project_id }"
                    >
                      <option value="">Select a project...</option>
                      <option 
                        v-for="project in projects" 
                        :key="project.id" 
                        :value="Number(project.id)"
                      >
                        {{ project.name }}
                      </option>
                    </select>
                    <div v-if="form.errors.project_id" class="text-red-600 text-sm mt-1">
                      {{ form.errors.project_id }}
                    </div>
                  </div>

                  <!-- Map Type -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Map Type *</label>
                    <select
                      v-model="form.type"
                      @change="onMapTypeChange"
                      required
                      class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                      :class="{ 'border-red-300': form.errors.type }"
                    >
                      <option value="single">Single Map (Multiple Markers)</option>
                      <option value="multi_city">Multi-City Maps</option>
                    </select>
                    <div v-if="form.errors.type" class="text-red-600 text-sm mt-1">
                      {{ form.errors.type }}
                    </div>
                  </div>

                  <!-- Map Title -->
                  <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Map Name</label>
                    <input
                      v-model="form.name"
                      type="text"
                      placeholder="Enter map name..."
                      class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                      :class="{ 'border-red-300': form.errors.name }"
                    />
                    <div v-if="form.errors.name" class="text-red-600 text-sm mt-1">
                      {{ form.errors.name }}
                    </div>
                  </div>

                  <!-- Center Coordinates -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Center Latitude *</label>
                    <input
                      v-model.number="form.center_coordinates[0]"
                      type="number"
                      step="0.000000000000001"
                      required
                      placeholder="36.1911"
                      @change="updateMapCenter"
                      class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                      :class="{ 'border-red-300': form.errors.center_coordinates }"
                    />
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Center Longitude *</label>
                    <input
                      v-model.number="form.center_coordinates[1]"
                      type="number"
                      step="0.000000000000001"
                      required
                      placeholder="44.0094"
                      @change="updateMapCenter"
                      class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                      :class="{ 'border-red-300': form.errors.center_coordinates }"
                    />
                  </div>

                  <!-- Zoom Level -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Default Zoom Level</label>
                    <input
                      v-model.number="form.zoom_level"
                      type="number"
                      min="1"
                      max="20"
                      @change="updateMapZoom"
                      class="w-full border-gray-300 rounded-md shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                    />
                  </div>

                  <!-- Status -->
                  <div class="flex items-center">
                    <label class="flex items-center">
                      <input
                        v-model="form.is_active"
                        type="checkbox"
                        class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                      />
                      <span class="ml-2 text-sm text-gray-700">Active</span>
                    </label>
                  </div>
                </div>
              </div>

              <!-- Map Preview & Configuration -->
              <div class="bg-gray-50 p-6 rounded-lg">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Map Preview & Configuration</h3>
                
                <!-- Map Preview -->
                <div class="mb-6">
                  <ProjectMapPreview
                    ref="mapPreview"
                    :map-data="form"
                    :cities="selectedCitiesData"
                    :markers="form.type === 'single' ? markers : []"
                    @marker-click="onMarkerClick"
                    @marker-add="onMarkerAdd"
                    @marker-move="onMarkerMove"
                    @map-bounds-change="onMapBoundsChange"
                    @center-change="onCenterChange"
                    @zoom-change="onZoomChange"
                    class="h-96 border border-gray-300 rounded-lg"
                  />
                </div>

                <!-- Single Map Markers -->
                <div v-if="form.type === 'single'" class="space-y-4">
                  <div class="flex justify-between items-center">
                    <h4 class="text-md font-medium text-gray-800">Markers</h4>
                    <div class="flex space-x-2">
                      <button
                        type="button"
                        @click="openAddMarkerModal"
                        class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm"
                      >
                        Add Marker
                      </button>
                      <button
                        type="button"
                        @click="triggerImportFile"
                        class="bg-blue-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm"
                      >
                        Import Markers
                      </button>
                      <button
                        type="button"
                        @click="showJsonFormatHelp"
                        class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-md text-sm"
                      >
                        ?
                      </button>
                      <input 
                        type="file" 
                        ref="fileInput" 
                        @change="importMarkersFromFile" 
                        accept=".json" 
                        class="hidden" 
                      />
                    </div>
                  </div>
                  
                  <div v-if="markers.length > 0" class="space-y-4">
                    <div v-for="(marker, index) in markers" :key="index" 
                      class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                      <div class="flex justify-between items-start mb-4">
                        <h4 class="text-lg font-medium text-gray-900">
                          {{ marker.title?.en || `Marker ${index + 1}` }}
                        </h4>
                        <div class="flex space-x-2">
                          <button 
                            type="button" 
                            @click="openEditMarkerModal(index)" 
                            class="text-blue-600 hover:text-blue-800 text-sm">
                            Edit
                          </button>
                          <button 
                            type="button" 
                            @click="removeMarker(index)" 
                            class="text-red-600 hover:text-red-800 text-sm">
                            Remove
                          </button>
                        </div>
                      </div>
                      <div class="grid grid-cols-2 gap-4">
                        <div>
                          <span class="text-sm text-gray-500">Position:</span>
                          <p class="text-sm">
                            {{ typeof marker.coordinates.lat === 'number' ? marker.coordinates.lat.toFixed(6) : Number(marker.coordinates.lat).toFixed(6) }}, 
                            {{ typeof marker.coordinates.lng === 'number' ? marker.coordinates.lng.toFixed(6) : Number(marker.coordinates.lng).toFixed(6) }}
                          </p>
                        </div>
                        <div>
                          <span class="text-sm text-gray-500">Icon:</span>
                          <div class="flex items-center mt-1">
                            <span class="text-sm capitalize mr-2">{{ marker.icon_type }}</span>
                            <div class="w-5 h-5 inline-flex">
                              <img v-if="marker.icon_type === 'bim'" src="/bim_logo.png" alt="BIM Logo" class="w-5 h-5 object-contain rounded-full" />
                              <img v-else-if="marker.icon_type === 'atm'" src="/atm_icon.svg" alt="ATM Icon" class="w-5 h-5 object-contain" />
                              <svg v-else width="20" height="20" viewBox="0 0 25 41" xmlns="http://www.w3.org/2000/svg" class="scale-75 origin-top-left">
                                <path fill="#EF4444" stroke="#fff" stroke-width="2" d="M12.5 0C19.4 0 25 5.6 25 12.5c0 3.4-1.4 6.5-3.6 8.7L12.5 41 3.6 21.2C1.4 19 0 15.9 0 12.5 0 5.6 5.6 0 12.5 0z"/>
                                <circle fill="#fff" cx="12.5" cy="12.5" r="6"/>
                              </svg>
                            </div>
                          </div>
                        </div>
                        <div class="col-span-2">
                          <span class="text-sm text-gray-500">Description:</span>
                          <p class="text-sm">{{ marker.description?.en || 'No description' }}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-else class="text-center py-8 text-gray-500">
                    No markers added yet. Double-click on the map or use "Add Marker" button.
                  </div>
                </div>

                <!-- Multi-City Maps -->
                <div v-if="form.type === 'multi_city'" class="space-y-6">
                  <!-- City Selection -->
                  <div>
                    <CitySelector
                      v-model="form.selected_city_ids"
                      :available-cities="availableCities"
                      @cities-loaded="onCitiesLoaded"
                    />
                  </div>
                  
                  <!-- Selected Cities with Dynamic Descriptions -->
                  <div v-if="selectedCitiesData.length > 0">
                    <div class="mb-4">
                      <h4 class="text-md font-medium text-gray-800 mb-2">Selected Cities & Customizations</h4>
                      <p class="text-sm text-gray-600">
                        Add custom titles and descriptions for each city that will be displayed on the map.
                      </p>
                    </div>
                    
                    <div class="space-y-4">
                      <div 
                        v-for="(city, index) in selectedCitiesData" 
                        :key="city.id" 
                        class="bg-white border-2 border-blue-200 rounded-lg p-5 shadow-sm"
                      >
                        <!-- City Header -->
                        <div class="flex items-center justify-between mb-4">
                          <div class="flex items-center space-x-3">
                            <div class="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-1 rounded-full">
                              City {{ index + 1 }}
                            </div>
                            <h6 class="text-lg font-semibold text-gray-900">
                              {{ city.name }}
                            </h6>
                          </div>
                          <div class="flex items-center space-x-3">
                            <div class="flex items-center space-x-4 text-xs text-gray-500">
                              <span class="bg-gray-100 px-2 py-1 rounded">Zoom: {{ city.zoom_level }}</span>
                              <span 
                                :class="city.is_active ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'" 
                                class="px-2 py-1 rounded font-medium"
                              >
                                {{ city.is_active ? 'Active' : 'Inactive' }}
                              </span>
                            </div>
                            <button
                              type="button"
                              @click="removeCity(city.id)"
                              class="text-red-600 hover:text-red-800 hover:bg-red-50 p-1.5 rounded-md transition-colors duration-200"
                              title="Remove this city"
                            >
                              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                              </svg>
                            </button>
                          </div>
                        </div>
                        
                        <!-- Kurdish Name if Available -->
                        <div v-if="city.name.ku && city.name.ku !== city.name.en" class="mb-4">
                          <p class="text-sm text-gray-600" dir="rtl">{{ city.name.ku }}</p>
                        </div>
                        
                        <!-- Title Inputs -->
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4">
                          <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">
                              Custom English Title
                              <span class="text-gray-400 font-normal">(Optional)</span>
                            </label>
                            <input
                              :value="getCityTitle(city.id, 'en')"
                              @input="updateCityTitle(city.id, 'en', $event.target.value)"
                              type="text"
                              :placeholder="`Custom title for ${city.name.en || 'this city'}...`"
                              class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 text-sm"
                            />
                            <p class="text-xs text-gray-500">
                              Leave empty to use the default city name: "{{ city.name.en }}"
                            </p>
                          </div>
                          
                          <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">
                              Custom Kurdish Title
                              <span class="text-gray-400 font-normal">(Optional)</span>
                            </label>
                            <input
                              :value="getCityTitle(city.id, 'ku')"
                              @input="updateCityTitle(city.id, 'ku', $event.target.value)"
                              type="text"
                              placeholder="ناونیشانی کوردی بۆ ئەم شارە..."
                              class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 text-sm"
                              dir="rtl"
                            />
                            <p class="text-xs text-gray-500" dir="rtl">
                              بۆ بەکارهێنانی ناوی بنەڕەتی شارەکە بەتاڵی بهێڵەوە: "{{ city.name.ku || city.name.en }}"
                            </p>
                          </div>

                          <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">
                              Custom Arabic Title
                              <span class="text-gray-400 font-normal">(Optional)</span>
                            </label>
                            <input
                              :value="getCityTitle(city.id, 'ar')"
                              @input="updateCityTitle(city.id, 'ar', $event.target.value)"
                              type="text"
                              placeholder="العنوان العربي لهذه المدينة..."
                              class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 text-sm"
                              dir="rtl"
                            />
                          </div>
                        </div>

                        <!-- Description Inputs -->
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-4">
                          <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">
                              English Description
                              <span class="text-gray-400 font-normal">(Optional)</span>
                            </label>
                            <textarea
                              :value="getCityDescription(city.id, 'en')"
                              @input="updateCityDescription(city.id, 'en', $event.target.value)"
                              rows="4"
                              :placeholder="`Enter English description for ${city.name.en || 'this city'}...`"
                              class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 text-sm resize-none"
                            ></textarea>
                            <p class="text-xs text-gray-500">
                              This description will appear in English when users click on this city.
                            </p>
                          </div>
                          
                          <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">
                              Kurdish Description
                              <span class="text-gray-400 font-normal">(Optional)</span>
                            </label>
                            <textarea
                              :value="getCityDescription(city.id, 'ku')"
                              @input="updateCityDescription(city.id, 'ku', $event.target.value)"
                              rows="4"
                              placeholder="وەسفی کوردی بۆ ئەم شارە بنووسە..."
                              class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 text-sm resize-none"
                              dir="rtl"
                            ></textarea>
                            <p class="text-xs text-gray-500" dir="rtl">
                              ئەم وەسفە لە کاتی کلیک کردن لەسەر شارەکە دەردەکەوێت.
                            </p>
                          </div>
                          <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">
                              Arabic Description
                              <span class="text-gray-400 font-normal">(Optional)</span>
                            </label>
                            <textarea
                              :value="getCityDescription(city.id, 'ar')"
                              @input="updateCityDescription(city.id, 'ar', $event.target.value)"
                              rows="4"
                              placeholder="وصف عربي لهذه المدينة..."
                              class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 text-sm resize-none"
                              dir="rtl"
                            ></textarea>
                            <p class="text-xs text-gray-500" dir="rtl">
                              This description will appear in Arabic when users click on this city.
                            </p>
                          </div>
                        </div>
                        
                        <!-- Preview of Current Customizations -->
                        <div v-if="getCityTitle(city.id, 'en') || getCityTitle(city.id, 'ku') || getCityDescription(city.id, 'en') || getCityDescription(city.id, 'ku')" class="mt-4 pt-4 border-t border-gray-200">
                          <h7 class="text-xs font-medium text-gray-700 mb-2 block">Preview:</h7>
                          <div class="bg-gray-50 p-3 rounded text-sm space-y-1">
                            <!-- Title Preview -->
                            <div v-if="getCityTitle(city.id, 'en') || getCityTitle(city.id, 'ku')" class="space-y-1">
                              <div v-if="getCityTitle(city.id, 'en')" class="text-gray-700">
                                <strong>Title (EN):</strong> {{ getCityTitle(city.id, 'en') }}
                              </div>
                              <div v-if="getCityTitle(city.id, 'ku')" class="text-gray-700" dir="rtl">
                                <strong>Title (KU):</strong> {{ getCityTitle(city.id, 'ku') }}
                              </div>
                            </div>
                            <!-- Description Preview -->
                            <div v-if="getCityDescription(city.id, 'en') || getCityDescription(city.id, 'ku')" class="space-y-1">
                              <div v-if="getCityDescription(city.id, 'en')" class="text-gray-700">
                                <strong>Description (EN):</strong> {{ getCityDescription(city.id, 'en') }}
                              </div>
                              <div v-if="getCityDescription(city.id, 'ku')" class="text-gray-700" dir="rtl">
                                <strong>Description (KU):</strong> {{ getCityDescription(city.id, 'ku') }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Empty State -->
                  <div v-if="form.selected_city_ids.length === 0" class="text-center py-12 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
                    <div class="space-y-3">
                      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      <div>
                        <h3 class="text-sm font-medium text-gray-900">No cities selected</h3>
                        <p class="text-sm text-gray-500 mt-1">Select cities from the dropdown above to create your multi-city map.</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Form Actions -->
              <div class="flex justify-end space-x-4">
                <Link
                  :href="route('admin.project-maps.index')"
                  class="bg-gray-300 hover:bg-gray-400 text-gray-700 font-medium py-2 px-6 rounded-lg transition duration-150 ease-in-out"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  :disabled="form.processing"
                  class="bg-indigo-600 hover:bg-indigo-700 disabled:opacity-50 text-white font-medium py-2 px-6 rounded-lg transition duration-150 ease-in-out"
                >
                  {{ form.processing ? 'Updating...' : 'Update Map' }}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Marker Modal -->
    <MarkerModal
      v-model="showMarkerModal"
      :marker="currentMarker"
      :index="currentMarkerIndex"
      @save="saveMarker"
    />
  </div>
</template>

<script setup>
import { ref, reactive, computed, nextTick, onMounted } from 'vue'
import { Head, Link, useForm } from '@inertiajs/inertia-vue3'
import ProjectMapPreview from '@/Components/ProjectMapPreview.vue'
import CitySelector from '@/Components/CitySelector.vue'
import MarkerModal from '@/Components/MarkerModal.vue'

const props = defineProps({
  projectMap: {
    type: Object,
    required: true
  },
  projects: {
    type: Array,
    required: true
  },
  availableCities: {
    type: Array,
    default: () => []
  }
})

// Ensure we're consistently accessing the data
const map = props.projectMap;
console.log('Project map data:', map);

// Get project ID ensuring it's a number
let projectId = '';
if (map.data?.project_id) {
  projectId = Number(map.data.project_id);
} else if (map.project_id) {
  projectId = Number(map.project_id);
} else if (map.data?.project?.id) {
  projectId = Number(map.data.project.id);
} else if (map.project?.id) {
  projectId = Number(map.project.id);
}

// Initialize form with existing data - using optional chaining to safely access properties
const form = useForm({
  project_id: projectId,
  type: map.data?.type || map.type || 'single',
  name: map.data?.name || map.name || '',
  center_coordinates: map.data?.center_coordinates || map.center_coordinates || map.data?.default_center || map.default_center || [36.1911, 44.0094],
  zoom_level: map.data?.zoom_level || map.zoom_level || map.data?.default_zoom || map.default_zoom || 10,
  bounds: map.data?.bounds || map.bounds || null,
  is_active: typeof map.data?.is_active !== 'undefined' ? map.data.is_active : (typeof map.is_active !== 'undefined' ? map.is_active : true),
  selected_city_ids: getSelectedCityIds(),
  city_titles: initializeCityTitles(),
  city_descriptions: initializeCityDescriptions(),
})

console.log({ projjjjj: props.projectMap })

// Get selected city IDs from customizations data
function getSelectedCityIds() {
  const customizations = map.data?.customizations || map.customizations || [];
  return customizations.map(customization => customization.project_city_id) || [];
}

// Initialize city titles from existing customizations
function initializeCityTitles() {
  const titles = {}
  
  // Get customizations data
  const customizations = map.data?.customizations || map.customizations || [];
  
  // First, initialize all cities from customizations with empty titles
  customizations.forEach(customization => {
    if (customization.project_city_id) {
      titles[customization.project_city_id] = { en: '', ku: '', ar: '' };
    }
  });
  
  // Then, populate with existing custom titles
  customizations.forEach(customization => {
    if (customization.custom_title && customization.project_city_id) {
      titles[customization.project_city_id] = customization.custom_title;
      console.log(`City ${customization.project_city_id} has custom title:`, customization.custom_title);
    }
  });
  
  console.log('Initialized city titles:', titles);
  return titles;
}

// Initialize city descriptions from existing customizations
function initializeCityDescriptions() {
  const descriptions = {}
  
  // Get customizations data
  const customizations = map.data?.customizations || map.customizations || [];
  
  // First, initialize all cities from customizations with empty descriptions
  customizations.forEach(customization => {
    if (customization.project_city_id) {
      descriptions[customization.project_city_id] = { en: '', ku: '', ar: '' };
    }
  });
  
  // Then, populate with existing custom descriptions
  customizations.forEach(customization => {
    if (customization.custom_description && customization.project_city_id) {
      descriptions[customization.project_city_id] = customization.custom_description;
      console.log(`City ${customization.project_city_id} has custom description:`, customization.custom_description);
    }
  });
  
  console.log('Initialized city descriptions:', descriptions);
  return descriptions;
}

// Computed property to get selected cities data with dynamic titles and descriptions
const selectedCitiesData = computed(() => {
  if (form.type !== 'multi_city' || !form.selected_city_ids.length) {
    return []
  }
  
  // Get the list of cities and add customization data
  return props.availableCities.filter(city => 
    form.selected_city_ids.includes(city.id)
  ).map(city => {
    // Get the customizations from the form data
    const customTitle = form.city_titles[city.id] || { en: '', ku: '', ar: '' };
    const customDescription = form.city_descriptions[city.id] || { en: '', ku: '', ar: '' };
    
    console.log(`City ${city.id} customizations:`, {
      title: customTitle,
      description: customDescription
    });
    
    return {
      ...city,
      // Add dynamic titles
      dynamic_title: {
        en: customTitle.en || '',
        ku: customTitle.ku || '',
        ar: customTitle.ar || ''
      },
      // Add dynamic descriptions
      dynamic_description: {
        en: customDescription.en || '',
        ku: customDescription.ku || '',
        ar: customDescription.ar || ''
      }
    };
  })
})

// Initialize markers from existing data - create clean copies to avoid proxy issues
const markersData = map.data?.markers || map.markers || []
const markers = ref(markersData.map(marker => ({
  title: marker.title || { en: '', ku: '', ar: '' },
  description: marker.description || { en: '', ku: '', ar: '' },
  coordinates: {
    lat: Number(marker.coordinates?.lat || 36.1911),
    lng: Number(marker.coordinates?.lng || 44.0094)
  },
  icon_type: marker.icon_type || 'default',
  is_active: marker.is_active !== undefined ? marker.is_active : true
})))
const showMarkerModal = ref(false)
const fileInput = ref(null) // Reference for file input
const currentMarker = ref({
  title: { en: '', ku: '', ar: '' },
  description: { en: '', ku: '', ar: '' },
  coordinates: { lat: 36.1911, lng: 44.0094 },
  icon_type: 'default',
  is_active: true
})
const currentMarkerIndex = ref(-1)

const isMapInteracting = ref(false);
let mapInteractionTimeout = null;

// 1. First, add a preventFormSubmission flag to disable submission during map interactions

const onMapTypeChange = () => {
  // Clear data when switching map types
  if (form.type === 'single') {
    form.selected_city_ids = []
    form.city_titles = {}
    form.city_descriptions = {}
  } else {
    markers.value = []
  }
}

// Get city title for specific language
const getCityTitle = (cityId, language) => {
  return form.city_titles[cityId]?.[language] || ''
}

// Update city title for specific language
const updateCityTitle = (cityId, language, value) => {
  if (!form.city_titles[cityId]) {
    form.city_titles[cityId] = { en: '', ku: '', ar: '' }
  }
  form.city_titles[cityId][language] = value
}

// Get city description for specific language
const getCityDescription = (cityId, language) => {
  return form.city_descriptions[cityId]?.[language] || ''
}

// Update city description for specific language
const updateCityDescription = (cityId, language, value) => {
  if (!form.city_descriptions[cityId]) {
    form.city_descriptions[cityId] = { en: '', ku: '', ar: '' }
  }
  form.city_descriptions[cityId][language] = value
}

// Remove a city from the selected cities
const removeCity = (cityId) => {
  // Remove from selected city IDs
  const index = form.selected_city_ids.indexOf(cityId)
  if (index > -1) {
    form.selected_city_ids.splice(index, 1)
  }
  
  // Remove any existing titles for this city
  if (form.city_titles[cityId]) {
    delete form.city_titles[cityId]
  }
  
  // Remove any existing descriptions for this city
  if (form.city_descriptions[cityId]) {
    delete form.city_descriptions[cityId]
  }
}

const onCitiesLoaded = (cities) => {
  // Handle cities loaded event if needed
  console.log('Available cities loaded:', cities)
}

const openAddMarkerModal = () => {
  currentMarker.value = {
    title: { en: '', ku: '', ar: '' },
    description: { en: '', ku: '', ar: '' },
    coordinates: { 
      lat: form.center_coordinates[0], 
      lng: form.center_coordinates[1] 
    },
    icon_type: 'default',
    is_active: true
  }
  currentMarkerIndex.value = -1
  showMarkerModal.value = true
}

const openEditMarkerModal = (index) => {
  currentMarker.value = JSON.parse(JSON.stringify(markers.value[index]))
  currentMarkerIndex.value = index
  showMarkerModal.value = true
}

const saveMarker = (data) => {
  // Ensure coordinates are numbers
  const markerData = {
    ...data,
    coordinates: {
      lat: Number(data.coordinates.lat),
      lng: Number(data.coordinates.lng)
    }
  }
  
  if (data.index >= 0) {
    // Edit existing marker
    markers.value[data.index] = markerData
  } else {
    // Add new marker
    markers.value.push(markerData)
  }
}

const removeMarker = (index) => {
  markers.value.splice(index, 1)
}

const onMarkerClick = (data) => {
  if (form.type === 'single' && data.index !== undefined) {
    openEditMarkerModal(data.index)
  }
}

const onMarkerAdd = (coordinates) => {
  if (form.type === 'single') {
    currentMarker.value = {
      title: { en: 'New Marker', ku: '', ar: '' },
      description: { en: '', ku: '', ar: '' },
      coordinates: {
        lat: Number(coordinates.lat),
        lng: Number(coordinates.lng)
      },
      icon_type: 'default',
      is_active: true
    }
    currentMarkerIndex.value = -1
    showMarkerModal.value = true
  }
}

const onMarkerMove = (data) => {
  if (data.markerIndex !== null && data.markerIndex !== undefined) {
    // Ensure coordinates are numbers
    markers.value[data.markerIndex].coordinates = {
      lat: Number(data.coordinates.lat),
      lng: Number(data.coordinates.lng)
    }
  }
}

// 4. Update the onMapBoundsChange handler
const onMapBoundsChange = (bounds) => {
  // Set the flag to prevent form submission
  isMapInteracting.value = true;
  
  form.bounds = bounds;
  
  // Clear any existing timeout
  if (mapInteractionTimeout) {
    clearTimeout(mapInteractionTimeout);
  }
  
  // Reset the flag after a delay
  mapInteractionTimeout = setTimeout(() => {
    isMapInteracting.value = false;
  }, 500);
}

// Force update of the map center when coordinates are changed
const updateMapCenter = () => {
  // Create a new array to trigger the watcher in ProjectMapPreview
  form.center_coordinates = [...form.center_coordinates]
}

// Force update of the map zoom when zoom level is changed
const updateMapZoom = () => {
  // This will trigger the zoom watcher in ProjectMapPreview
  form.zoom_level = Number(form.zoom_level)
}

// Handle center change from map
const onCenterChange = (newCenter) => {
  if (Array.isArray(newCenter) && newCenter.length === 2) {
    // Set the flag to prevent form submission
    isMapInteracting.value = true;
    
    // Update the form values when the map is moved
    form.center_coordinates = [
      Number(newCenter[0]),
      Number(newCenter[1])
    ]
    
    // Clear any existing timeout
    if (mapInteractionTimeout) {
      clearTimeout(mapInteractionTimeout);
    }
    
    // Reset the flag after a delay
    mapInteractionTimeout = setTimeout(() => {
      isMapInteracting.value = false;
    }, 500);
  }
}

// Update the zoom change handler similarly
const onZoomChange = (newZoom) => {
  if (typeof newZoom === 'number') {
    // Set the flag to prevent form submission
    isMapInteracting.value = true;
    
    // Update the form value when the map zoom changes
    form.zoom_level = newZoom;
    
    // Clear any existing timeout
    if (mapInteractionTimeout) {
      clearTimeout(mapInteractionTimeout);
    }
    
    // Reset the flag after a delay
    mapInteractionTimeout = setTimeout(() => {
      isMapInteracting.value = false;
    }, 500);
  }
}

// Submit function - create clean data object for submission
const submit = () => {
  // Don't submit if the map is currently being interacted with
  if (isMapInteracting.value) {
    console.log('Form submission prevented during map interaction');
    return;
  }
  
  // Create clean, serializable markers array
  const cleanMarkers = form.type === 'single' ? markers.value.map(marker => ({
    title: marker.title,
    description: marker.description,
    coordinates: {
      lat: Number(marker.coordinates.lat),
      lng: Number(marker.coordinates.lng)
    },
    icon_type: marker.icon_type,
    is_active: marker.is_active
  })) : []
  
  // Create a clean, serializable data object
  const submitData = {
    project_id: form.project_id,
    type: form.type,
    name: form.name,
    center_coordinates: [...form.center_coordinates],
    zoom_level: Number(form.zoom_level),
    bounds: form.bounds,
    is_active: Boolean(form.is_active),
    selected_city_ids: [...form.selected_city_ids],
    city_titles: JSON.parse(JSON.stringify(form.city_titles)),
    city_descriptions: JSON.parse(JSON.stringify(form.city_descriptions)),
    markers: cleanMarkers
  }

  console.log('Submitting data:', submitData);

  // Use transform to send the clean data
  form.transform(() => submitData)
    .patch(route('admin.project-maps.update', { project_map: map.data?.id || map.id }))
}

// Trigger the file input when the import button is clicked
const triggerImportFile = () => {
  fileInput.value.click()
}

// Show help for JSON format
const showJsonFormatHelp = () => {
  const jsonFormatExample = [
    {
      "title": {
        "en": "BIM Headquarters",
        "ku": "BIM سەرەکی",
        "ar": "المقر الرئيسي لبيم"
      },
      "coordinates": {
        "lat": 36.1911,
        "lng": 44.0094
      },
      "description": {
        "en": "This is our main office",
        "ku": "",
        "ar": "هذا هو مكتبنا الرئيسي"
      },
      "icon_type": "bim",
      "is_active": true
    },
    {
      "title": "ATM Location",
      "lat": 36.2042,
      "lng": 44.0086,
      "description": "24/7 ATM available here",
      "icon_type": "atm"
    }
  ]
  
  alert(`Expected JSON Format (can be an array or single object):\n${JSON.stringify(jsonFormatExample, null, 2)}`)
}

// Process the JSON file and import the markers
const importMarkersFromFile = (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const importedData = JSON.parse(e.target.result)
      
      // Process the imported data
      if (Array.isArray(importedData)) {
        // Process array of markers
        const processedMarkers = importedData.map(markerData => processMarkerData(markerData))
        markers.value = [...markers.value, ...processedMarkers]
        alert(`Successfully imported ${processedMarkers.length} markers`)
      } else if (typeof importedData === 'object') {
        // Process single marker
        const processedMarker = processMarkerData(importedData)
        markers.value.push(processedMarker)
        alert('Successfully imported 1 marker')
      }
      
      // Reset file input
      event.target.value = ''
    } catch (error) {
      alert('Error parsing JSON file: ' + error.message)
      console.error('Error parsing JSON file:', error)
      event.target.value = ''
    }
  }
  
  reader.onerror = () => {
    alert('Error reading the file')
    event.target.value = ''
  }
  
  reader.readAsText(file)
}

// Process marker data from imported JSON
const processMarkerData = (data) => {
  // Check if marker has either title or coordinates
  if (!data.title && (!data.coordinates && (!data.lat && !data.lng))) {
    throw new Error('Marker data must include at least a title and coordinates')
  }
  
  // Process coordinates - either from coordinates object or direct lat/lng
  const coordinates = {
    lat: Number(data.coordinates?.lat ?? data.lat ?? 36.1911),
    lng: Number(data.coordinates?.lng ?? data.lng ?? 44.0094)
  }
  
  // Ensure lat/lng are valid numbers
  if (isNaN(coordinates.lat) || isNaN(coordinates.lng)) {
    throw new Error('Coordinates must be valid numbers')
  }
  
  // Process title - handle either string or object format
  let title = { en: '', ku: '', ar: '' }
  
  if (typeof data.title === 'string') {
    title.en = data.title
  } else if (data.title && typeof data.title === 'object') {
    title.en = data.title.en || ''
    title.ku = data.title.ku || ''
    title.ar = data.title.ar || ''
  }
  
  // If title is empty, use "Imported Marker" as default
  if (!title.en) {
    title.en = 'Imported Marker'
  }
  
  // Process description - handle either string or object format
  let description = { en: '', ku: '', ar: '' }
  
  if (typeof data.description === 'string') {
    description.en = data.description
  } else if (data.description && typeof data.description === 'object') {
    description.en = data.description.en || ''
    description.ku = data.description.ku || ''
    description.ar = data.description.ar || ''
  }
  
  // Return formatted marker data
  return {
    title,
    description,
    coordinates,
    icon_type: data.icon_type || 'default',
    is_active: data.is_active !== undefined ? data.is_active : true
  }
}
</script>
