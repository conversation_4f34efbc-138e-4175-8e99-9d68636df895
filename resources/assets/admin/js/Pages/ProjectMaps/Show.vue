<template>
  <div>
    <Head :title="`${map.title || 'Project Map'} - View`" />

    <div class="py-12">
      <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
        <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
          <div class="p-6 bg-white border-b border-gray-200">
            
            <!-- Header -->
            <div class="flex justify-between items-center mb-6">
              <div>
                <h2 class="text-2xl font-bold text-gray-900">{{ map.data?.title || 'Project Map' }}</h2>
                <p class="text-gray-600 mt-1">{{ map.data?.project?.name || 'Unknown Project' }}</p>
              </div>
              <div class="flex space-x-3">
                <Link
                  :href="route('admin.project-maps.edit', {project_map: map.data?.id || map.id})"
                  class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition duration-150 ease-in-out"
                >
                  Edit Map
                </Link>
                <Link
                  :href="route('admin.project-maps.index')"
                  class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-lg transition duration-150 ease-in-out"
                >
                  Back to Maps
                </Link>
              </div>
            </div>

            <!-- Map Information -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide">Map Type</h3>
                <p class="mt-2 text-lg font-semibold text-gray-900">
                  {{ map.data?.type === 'single' ? 'Single Map' : 'Multi-City Map' }}
                </p>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide">Status</h3>
                <p class="mt-2">
                  <span
                    :class="[
                      'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                      map.data?.is_active
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    ]"
                  >
                    {{ map.data?.is_active ? 'Active' : 'Inactive' }}
                  </span>
                </p>
              </div>
              <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-sm font-medium text-gray-500 uppercase tracking-wide">
                  {{ map.data?.type === 'single' ? 'Markers' : 'Cities' }}
                </h3>
                <p class="mt-2 text-lg font-semibold text-gray-900">
                  {{ map.data?.type === 'single' ? (map.data?.markers?.length || 0) : (map.data?.cities?.length || 0) }}
                </p>
              </div>
            </div>

            <!-- Map Display -->
            <div class="mb-8">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Map Preview</h3>
              <div class="h-96 rounded-lg overflow-hidden border border-gray-300">
                <ProjectMapPreview
                  :map-data="map.data"
                  :cities="map.data?.cities || []"
                  :markers="map.data?.markers || []"
                  class="h-full"
                  @marker-click="showMarkerDetails"
                />
              </div>
            </div>

            <!-- Single Map Content -->
            <div v-if="map.data?.type === 'single' && map.data?.markers && map.data.markers.length > 0" class="mb-8">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Markers</h3>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div
                  v-for="(marker, index) in map.data.markers"
                  :key="index"
                  class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div class="flex items-start justify-between mb-2">
                    <h4 class="font-medium text-gray-900">
                      {{ marker.title?.en || marker.title || `Marker ${index + 1}` }}
                    </h4>
                    <div class="w-4 h-4 rounded-full" :style="{ backgroundColor: marker.color }"></div>
                  </div>
                  <p v-if="marker.description?.en" class="text-sm text-gray-600 mb-2">
                    {{ marker.description.en }}
                  </p>
                  <div class="flex justify-between items-center text-xs text-gray-500">
                    <span>
                      {{ marker.coordinates.lat?.toFixed(6) }}, {{ marker.coordinates.lng?.toFixed(6) }}
                    </span>
                    <span
                      :class="[
                        'px-2 py-1 rounded-full',
                        marker.is_active
                          ? 'bg-green-100 text-green-700'
                          : 'bg-red-100 text-red-700'
                      ]"
                    >
                      {{ marker.is_active ? 'Active' : 'Inactive' }}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Multi-City Content -->
            <div v-if="map.data?.type === 'multi_city' && map.data?.cities && map.data.cities.length > 0" class="mb-8">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Cities</h3>
              <div class="space-y-6">
                <div
                  v-for="(city, cityIndex) in map.data.cities"
                  :key="cityIndex"
                  class="bg-gray-50 border border-gray-200 rounded-lg p-6"
                >
                  <div class="flex justify-between items-start mb-4">
                    <div>
                      <h4 class="text-lg font-medium text-gray-900">
                        {{ city.name?.en || city.name || `City ${cityIndex + 1}` }}
                      </h4>
                      <p v-if="city.description?.en" class="text-sm text-gray-600 mt-1">
                        {{ city.description.en }}
                      </p>
                    </div>
                    <span
                      :class="[
                        'px-2 py-1 text-xs font-medium rounded-full',
                        city.is_active
                          ? 'bg-green-100 text-green-700'
                          : 'bg-red-100 text-red-700'
                      ]"
                    >
                      {{ city.is_active ? 'Active' : 'Inactive' }}
                    </span>
                  </div>

                  <!-- City Details -->
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div class="text-sm">
                      <span class="font-medium text-gray-700">Center:</span>
                      <span class="text-gray-600 ml-1">
                        {{ city.center_coordinates?.[0]?.toFixed(6) }}, 
                        {{ city.center_coordinates?.[1]?.toFixed(6) }}
                      </span>
                    </div>
                    <div class="text-sm">
                      <span class="font-medium text-gray-700">Zoom Level:</span>
                      <span class="text-gray-600 ml-1">{{ city.zoom_level }}</span>
                    </div>
                    <div class="text-sm">
                      <span class="font-medium text-gray-700">Markers:</span>
                      <span class="text-gray-600 ml-1">{{ city.markers?.length || 0 }}</span>
                    </div>
                  </div>

                  <!-- City Markers -->
                  <div v-if="city.markers && city.markers.length > 0">
                    <h5 class="text-md font-medium text-gray-800 mb-3">City Markers</h5>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <div
                        v-for="(marker, markerIndex) in city.markers"
                        :key="markerIndex"
                        class="bg-white border border-gray-200 rounded p-3"
                      >
                        <div class="flex items-start justify-between mb-1">
                          <h6 class="text-sm font-medium text-gray-900">
                            {{ marker.title?.en || marker.title || `Marker ${markerIndex + 1}` }}
                          </h6>
                          <div class="w-3 h-3 rounded-full" :style="{ backgroundColor: marker.color }"></div>
                        </div>
                        <p v-if="marker.description?.en" class="text-xs text-gray-600 mb-1">
                          {{ marker.description.en }}
                        </p>
                        <div class="text-xs text-gray-500">
                          {{ marker.coordinates.lat?.toFixed(6) }}, {{ marker.coordinates.lng?.toFixed(6) }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Empty State -->
            <div v-if="(!map.data?.markers || map.data.markers.length === 0) && (!map.data?.cities || map.data.cities.length === 0)">
              <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m0 0L9 7" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No content found</h3>
                <p class="mt-1 text-sm text-gray-500">
                  This map doesn't have any {{ map.data?.type === 'single' ? 'markers' : 'cities' }} yet.
                </p>
                <div class="mt-6">
                  <Link
                    :href="route('admin.project-maps.edit', {project_map: map.data?.id || map.id})"
                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                  >
                    Edit Map
                  </Link>
                </div>
              </div>
            </div>

            <!-- Metadata -->
            <div class="border-t border-gray-200 pt-6 mt-8">
              <dl class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                <div>
                  <dt class="font-medium text-gray-500">Created</dt>
                  <dd class="text-gray-900">{{ formatDate(map.data?.created_at || map.created_at) }}</dd>
                </div>
                <div>
                  <dt class="font-medium text-gray-500">Last Updated</dt>
                  <dd class="text-gray-900">{{ formatDate(map.data?.updated_at || map.updated_at) }}</dd>
                </div>
                <div>
                  <dt class="font-medium text-gray-500">Center Coordinates</dt>
                  <dd class="text-gray-900">
                    {{ map.data?.center_coordinates?.[0]?.toFixed(6) }}, {{ map.data?.center_coordinates?.[1]?.toFixed(6) }}
                  </dd>
                </div>
                <div>
                  <dt class="font-medium text-gray-500">Default Zoom</dt>
                  <dd class="text-gray-900">{{ map.data?.zoom_level }}</dd>
                </div>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Marker Detail Modal -->
    <div
      v-if="selectedMarker"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click="closeMarkerDetails"
    >
      <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4" @click.stop>
        <div class="flex justify-between items-start mb-4">
          <h3 class="text-lg font-medium text-gray-900">
            {{ selectedMarker.title?.en || selectedMarker.title || 'Marker Details' }}
          </h3>
          <button
            @click="closeMarkerDetails"
            class="text-gray-400 hover:text-gray-600"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="space-y-3">
          <div v-if="selectedMarker.description?.en">
            <h4 class="text-sm font-medium text-gray-700">Description</h4>
            <p class="text-sm text-gray-600">{{ selectedMarker.description.en }}</p>
          </div>
          
          <div>
            <h4 class="text-sm font-medium text-gray-700">Coordinates</h4>
            <p class="text-sm text-gray-600">
              {{ selectedMarker.coordinates.lat?.toFixed(6) }}, {{ selectedMarker.coordinates.lng?.toFixed(6) }}
            </p>
          </div>
          
          <div class="flex items-center space-x-4">
            <div>
              <h4 class="text-sm font-medium text-gray-700">Color</h4>
              <div class="flex items-center space-x-2">
                <div class="w-4 h-4 rounded-full" :style="{ backgroundColor: selectedMarker.color }"></div>
                <span class="text-sm text-gray-600">{{ selectedMarker.color }}</span>
              </div>
            </div>
            <div>
              <h4 class="text-sm font-medium text-gray-700">Status</h4>
              <span
                :class="[
                  'px-2 py-1 text-xs font-medium rounded-full',
                  selectedMarker.is_active
                    ? 'bg-green-100 text-green-700'
                    : 'bg-red-100 text-red-700'
                ]"
              >
                {{ selectedMarker.is_active ? 'Active' : 'Inactive' }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Head, Link } from '@inertiajs/inertia-vue3'
import ProjectMapPreview from '@/Components/ProjectMapPreview.vue'

const props = defineProps({
  map: {
    type: Object,
    required: true
  }
})

const selectedMarker = ref(null)

const showMarkerDetails = (data) => {
  selectedMarker.value = data.marker
}

const closeMarkerDetails = () => {
  selectedMarker.value = null
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
