<script setup>
import { useForm } from '@inertiajs/inertia-vue3';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import route from 'ziggy';
import { Inertia } from '@inertiajs/inertia';
import FormError from '@/Components/FormError.vue';
import Label from '@/Components/Label.vue';
import FormInput from '@/Components/FormInput.vue';
import { QuillEditor } from '@vueup/vue-quill';
import useImageUpload from '@/Composable/useImageUpload';
import { Switch } from '@headlessui/vue';

import '@vueup/vue-quill/dist/vue-quill.snow.css';

import useLanguageObject from '@/Composable/useLanguageObject';
import { TabGroup, TabList, Tab, TabPanels, TabPanel } from '@headlessui/vue';

const { languages, languageObject } = useLanguageObject();

const form = useForm({
  title: { ...languageObject },
  location: { ...languageObject },
  description: { ...languageObject },
  closed: false,
});


function submit() {
  form
    .transform((data) => ({
      ...data,
    }))
    .post(route('admin.job-vacancies.store'), {
      forceFormData: true,
      preserveScroll: true,
      preserveState: true,
      onSuccess: () => form.reset(),
    });
}

function cancel() {
  Inertia.visit(route('admin.job-vacancies.index'));
}


const { modules } = useImageUpload('job-vacancies')
</script>  
<template>
  <div class="max-w-5xl mx-auto">
    <div class="mb-6 lg:mb-10">
      <h3 class="text-lg font-medium leading-6 text-gray-900">Add a new vacancy</h3>
    </div>
    <form @submit.prevent="submit" class="space-y-6 lg:space-y-8">
      <div class="flex flex-col lg:flex-row gap-6">
        <div class="rounded-b-xl rounded-t-xl bg-white p-4 shadow-sm space-y-5 ring-white ring-opacity-60 focus:outline-none">
          <TabGroup>
            <TabList class="flex space-x-4 mb-4 border-b">
              <Tab v-for="lang in languages" :key="lang.id" v-slot="{ selected }" as="template">
                <button :class="[
                  'px-4 py-2 text-sm font-medium rounded-t-lg focus:outline-none',
                  selected ? 'bg-primary-500 text-white' : 'text-gray-500 hover:text-gray-700',
                ]">
                  {{ lang.name }}
                </button>
              </Tab>
            </TabList>
            <TabPanels>
              <TabPanel v-for="lang in languages" :key="lang.id">
                <div class="space-y-6">
                  <FormInput
                    :label="'Job Title in ' + lang.name"
                    v-model="form.title[lang.iso_code_name]"
                    :error="form.errors['title.' + lang.iso_code_name]"
                    :required="lang.iso_code_name === 'en'"
                  />
                  <FormInput
                    :label="'Location in ' + lang.name"
                    v-model="form.location[lang.iso_code_name]"
                    :error="form.errors['location.' + lang.iso_code_name]"
                  />
                  <div :class="{ 'input-error': !!form.errors['description.' + lang.iso_code_name] }">
                    <Label class="mb-2" :for="'editor-' + lang.iso_code_name">Job Description in {{ lang.name }}</Label>
                    <QuillEditor 
                      :modules="modules" 
                      toolbar="full" 
                      :id="'editor-' + lang.iso_code_name"
                      v-model:content="form.description[lang.iso_code_name]"
                      theme="snow" 
                      content-type="html"
                    />
                    <FormError class="mt-1" v-if="form.errors['description.' + lang.iso_code_name]">
                      {{ form.errors['description.' + lang.iso_code_name] }}
                    </FormError>
                  </div>
                </div>
              </TabPanel>
            </TabPanels>
          </TabGroup>
          <div class="flex items-center">
            <Label class="mr-3">Closed</Label>
            <Switch v-model="form.closed" :class="form.closed ? 'bg-red-600' : 'bg-zinc-300'"
            class="relative inline-flex h-[24px] w-[56px] shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus-visible:ring-2 focus-visible:ring-white focus-visible:ring-opacity-75">
            <span class="sr-only">Use setting</span>
            <span aria-hidden="true" :class="form.closed ? 'translate-x-8' : 'translate-x-0'"
              class="pointer-events-none inline-block h-[20px] w-[20px] transform rounded-full bg-white shadow-lg ring-0 transition duration-200 ease-in-out" />
          </Switch>
          </div>

        </div>
      </div>

      <div class="flex gap-4">
        <PrimaryButton class="px-5 py-2.5 text-base" :loading="form.processing" :disabled="form.processing"
          @click="submit">Create</PrimaryButton>
        <SecondaryButton class="px-5 py-2.5 text-base" :disabled="form.processing" @click="cancel">Cancel
        </SecondaryButton>
      </div>
    </form>
  </div>
</template>

