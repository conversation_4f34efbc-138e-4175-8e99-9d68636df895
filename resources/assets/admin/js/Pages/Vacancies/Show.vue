<script setup>
import { Inertia } from '@inertiajs/inertia';
import route from 'ziggy';
import PageHeading from '@/Components/PageHeading.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';

const props = defineProps({
  vacancy: {
    type: Object,
    required: true
  },
  submissionsCount: {
    type: Number,
    default: 0
  },
  unreadSubmissionsCount: {
    type: Number,
    default: 0
  }
});

function goBack() {
  Inertia.visit(route('admin.job-vacancies.index'));
}

function goToSubmissions() {
  Inertia.visit(route('admin.job-vacancies.submissions.index', props.vacancy.id));
}

function editVacancy() {
  Inertia.visit(route('admin.job-vacancies.edit', props.vacancy.id));
}
</script>

<template>
  <PageHeading :heading="`Job Vacancy Details`">
    <template #actions>
      <div class="space-x-3 flex items-end">
        <PrimaryButton v-if="vacancy.can_edit" @click="editVacancy">
          Edit Vacancy
        </PrimaryButton>
        <SecondaryButton @click="goBack">
          Back to Vacancies
        </SecondaryButton>
      </div>
    </template>
  </PageHeading>

  <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
    <!-- Job Details Card -->
    <div class="md:col-span-2 bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6 flex justify-between items-center">
        <div>
          <h3 class="text-lg leading-6 font-medium text-gray-900">
            {{ vacancy.title }}
          </h3>
          <p class="mt-1 max-w-2xl text-sm text-gray-500">
            Posted on {{ vacancy.created_at }}
          </p>
        </div>
        <div>
          <span v-if="vacancy.closed" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
            Closed
          </span>
          <span v-else class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
            Open
          </span>
        </div>
      </div>
      <div class="border-t border-gray-200">
        <dl>
          <div v-if="vacancy.location" class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">
              Location
            </dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              {{ vacancy.location }}
            </dd>
          </div>
          <div class="bg-white px-4 py-5 sm:px-6">
            <dt class="text-sm font-medium text-gray-500 mb-2">
              Description
            </dt>
            <dd class="mt-1 text-sm text-gray-900">
              <div class="prose max-w-none" v-html="vacancy.description"></div>
            </dd>
          </div>
        </dl>
      </div>
    </div>

    <!-- Submissions Card -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
          Submissions
        </h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500">
          Applications received for this position
        </p>
      </div>
      <div class="border-t border-gray-200 px-4 py-5 sm:p-6">
        <div class="flex flex-col space-y-4">
          <div class="bg-blue-50 p-4 rounded-lg">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3 flex-1 md:flex md:justify-between">
                <p class="text-sm text-blue-700">
                  Total Submissions: <span class="font-bold">{{ submissionsCount }}</span>
                </p>
              </div>
            </div>
          </div>
          
          <div v-if="unreadSubmissionsCount > 0" class="bg-yellow-50 p-4 rounded-lg">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3 flex-1 md:flex md:justify-between">
                <p class="text-sm text-yellow-700">
                  Unread Submissions: <span class="font-bold">{{ unreadSubmissionsCount }}</span>
                </p>
              </div>
            </div>
          </div>
          
          <div class="mt-4">
            <button 
              @click="goToSubmissions" 
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              View All Submissions
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
