<template>
  <div class="max-w-7xl mx-auto">
    <div class="mb-6 lg:mb-10 flex items-center justify-between">
      <div>
        <h3 class="text-2xl font-medium leading-6 text-gray-900">
          City
        </h3>
        <p class="mt-1 text-sm text-gray-500">
          City details and boundary information
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <Link
          :href="route('admin.cities.edit', city.id)"
          class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
          Edit City
        </Link>
        <Link
          :href="route('admin.cities.index')"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Back to Cities
        </Link>
      </div>
    </div>

    <!-- Map Display - Full Width -->
    <div class="bg-white shadow rounded-lg p-6 mb-8">
      <h4 class="text-lg font-medium text-gray-900 mb-4">City Boundary Map</h4>
      <div class="h-[500px] border border-gray-300 rounded-lg overflow-hidden">
        <CityDisplayMap
          :center="city.center_coordinates"
          :zoom="city.zoom_level || 8"
          :bounds="city.bounds || []"
          :readonly="true"
          height="500px"
        />
      </div>
    </div>

    <!-- City Information - Single Column -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Basic Info -->
      <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">City Information</h3>
        </div>
        <div class="border-t border-gray-200">
          <dl>
            <!-- City Name -->
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Name</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <div v-for="(value, lang) in city.name" :key="lang" class="mb-1" v-if="value">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-2">
                    {{ lang.toUpperCase() }}
                  </span>
                  {{ value }}
                </div>
              </dd>
            </div>

            <!-- Description -->
            <div v-if="hasContent(city.description)" class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Description</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <div v-for="(value, lang) in city.description" :key="lang" class="mb-2" v-if="value">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 mr-2">
                    {{ lang.toUpperCase() }}
                  </span>
                  <p class="mt-1">{{ value }}</p>
                </div>
              </dd>
            </div>

            <!-- Status -->
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Status</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <span
                  :class="[
                    'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                    city.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  ]"
                >
                  {{ city.is_active ? 'Active' : 'Inactive' }}
                </span>
              </dd>
            </div>

            <!-- Center Coordinates -->
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Center Coordinates</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <p>Latitude: {{ city.center_coordinates[0].toFixed(6) }}</p>
                <p>Longitude: {{ city.center_coordinates[1].toFixed(6) }}</p>
              </dd>
            </div>

            <!-- Zoom Level -->
            <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Default Zoom Level</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {{ city.zoom_level || 10 }}
              </dd>
            </div>

            <!-- Sort Order -->
            <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt class="text-sm font-medium text-gray-500">Sort Order</dt>
              <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {{ city.sort_order || 0 }}
              </dd>
            </div>
          </dl>
        </div>
      </div>

      <!-- Boundary Information -->
      <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900">Boundary Information</h3>
        </div>
        <div class="border-t border-gray-200 px-4 py-5 sm:px-6">
          <div v-if="city.bounds && city.bounds.length > 0" class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <dt class="text-sm font-medium text-gray-500">Boundary Points</dt>
                <dd class="mt-1 text-lg font-semibold text-gray-900">{{ city.bounds.length }}</dd>
              </div>
              <div>
                <dt class="text-sm font-medium text-gray-500">Approximate Area</dt>
                <dd class="mt-1 text-lg font-semibold text-gray-900">{{ calculatedArea }} km²</dd>
              </div>
            </div>
            
            <!-- Boundary coordinates preview -->
            <div class="mt-4">
              <dt class="text-sm font-medium text-gray-500 mb-2">Boundary Coordinates (Sample)</dt>
              <div class="bg-gray-50 rounded-md p-3 max-h-32 overflow-y-auto">
                <div v-for="(point, index) in city.bounds.slice(0, 5)" :key="index" class="text-xs text-gray-600">
                  <template v-if="point && typeof point === 'object' && point.lat && point.lng">
                    Point {{ index + 1 }}: {{ point.lat.toFixed(6) }}, {{ point.lng.toFixed(6) }}
                  </template>
                  <template v-else-if="Array.isArray(point) && point.length >= 2">
                    Point {{ index + 1 }}: {{ point[0].toFixed(6) }}, {{ point[1].toFixed(6) }}
                  </template>
                  <template v-else>
                    Point {{ index + 1 }}: Invalid coordinate data
                  </template>
                </div>
                <div v-if="city.bounds.length > 5" class="text-xs text-gray-500 mt-1">
                  ... and {{ city.bounds.length - 5 }} more points
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m0 0L9 7"></path>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">No boundary defined</h3>
            <p class="mt-1 text-sm text-gray-500">This city doesn't have a geographical boundary defined.</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Creation Info -->
    <div class="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6">
        <h3 class="text-lg leading-6 font-medium text-gray-900">Metadata</h3>
      </div>
      <div class="border-t border-gray-200">
        <dl>
          <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Created</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              {{ formatDate(city.created_at) }}
            </dd>
          </div>
          <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
            <dt class="text-sm font-medium text-gray-500">Last Updated</dt>
            <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
              {{ formatDate(city.updated_at) }}
            </dd>
          </div>
        </dl>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Link } from '@inertiajs/inertia-vue3';
import { computed } from 'vue';
import CityDisplayMap from '@/Components/CityDisplayMap.vue';
import route from 'ziggy';

const props = defineProps({
  city: {
    type: Object,
    required: true,
  },
});

const city = props.city.data
console.log({ city })

const calculatedArea = computed(() => {
  if (!city.bounds || city.bounds.length < 3) return '0';
  
  // Simple area calculation (this is a rough estimate)
  let area = 0;
  const coords = city.bounds.map(point => {
    if (point && typeof point === 'object' && point.lat && point.lng) {
      return [point.lat, point.lng];
    } else if (Array.isArray(point) && point.length >= 2) {
      return [point[0], point[1]];
    } else {
      return [0, 0]; // fallback
    }
  });
  
  for (let i = 0; i < coords.length; i++) {
    const j = (i + 1) % coords.length;
    area += coords[i][0] * coords[j][1];
    area -= coords[j][0] * coords[i][1];
  }
  
  area = Math.abs(area) / 2;
  
  // Convert to km² (very rough approximation)
  const kmArea = area * 111 * 111; // 1 degree ≈ 111 km
  return kmArea.toFixed(2);
});

function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
}

function hasContent(obj) {
  if (!obj) return false;
  return Object.values(obj).some(value => value && value.trim().length > 0);
}
</script>
