<template>
  <div class="max-w-7xl mx-auto">
    <div class="mb-6 lg:mb-10 flex items-center justify-between">
      <div>
        <h3 class="text-2xl font-medium leading-6 text-gray-900">Cities Management</h3>
        <p class="mt-1 text-sm text-gray-500">
          Manage cities with their geographical boundaries for use in project maps.
        </p>
      </div>
      <div class="flex items-center space-x-3">
        <Link
          :href="route('admin.cities.create')"
          class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
          </svg>
          Add City
        </Link>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="mb-6">
      <div class="bg-white p-4 rounded-lg shadow">
        <div class="flex items-center space-x-4">
          <div class="flex-1">
            <input
              v-model="searchQuery"
              @input="search"
              type="text"
              placeholder="Search cities..."
              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            />
          </div>
          <button
            @click="clearSearch"
            v-if="searchQuery"
            class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Clear
          </button>
        </div>
      </div>
    </div>

    <!-- Cities Grid -->
    <div v-if="cities.data.length > 0" class="bg-white shadow overflow-hidden sm:rounded-md">
      <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3 p-4">
        <div
          v-for="city in cities.data"
          :key="city.id"
          class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
        >
          <div class="flex items-center justify-between mb-2">
            <h4 class="text-lg font-medium text-gray-900">
              {{ city.name.en || city.name[Object.keys(city.name)[0]] }}
            </h4>
            <span
              :class="[
                'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                city.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
              ]"
            >
              {{ city.is_active ? 'Active' : 'Inactive' }}
            </span>
          </div>

          <!-- City Description -->
          <p v-if="city.description && city.description.en" class="text-sm text-gray-600 mb-3">
            {{ city.description.en }}
          </p>
         
          <!-- Languages -->
          <div class="mb-3">
            <div class="flex flex-wrap gap-1">
              <span
                v-for="(value, lang) in city.name"
                :key="lang"
                v-if="value"
                class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800"
              >
                {{ lang.toUpperCase() }}
              </span>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex items-center justify-between">
            <div class="text-xs text-gray-400">
              Created {{ formatDate(city.created_at) }}
            </div>
            <div class="flex items-center space-x-2">
              <Link
                :href="route('admin.cities.show', city.id)"
                class="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
              >
                View
              </Link>
              <Link
                :href="route('admin.cities.edit', city.id)"
                class="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
              >
                Edit
              </Link>
              <button
                @click="deleteCity(city)"
                class="text-red-600 hover:text-red-900 text-sm font-medium"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="cities.meta.links" class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div class="flex items-center justify-between">
          <div class="flex-1 flex justify-between sm:hidden">
            <Link
              v-if="cities.prev_page_url"
              :href="cities.prev_page_url"
              class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Previous
            </Link>
            <Link
              v-if="cities.next_page_url"
              :href="cities.next_page_url"
              class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            >
              Next
            </Link>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing {{ cities.from }} to {{ cities.to }} of {{ cities.total }} results
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <template v-for="link in cities.meta.links" :key="link.label">
                  <Link
                    v-if="link.url"
                    :href="link.url"
                    :class="[
                      'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                      link.active
                        ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                    ]"
                    v-html="link.label"
                  />
                  <span
                    v-else
                    :class="[
                      'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                      'bg-white border-gray-300 text-gray-300 cursor-not-allowed'
                    ]"
                    v-html="link.label"
                  />
                </template>
              </nav>
            </div>
          </div>
        </div>
      </div>

    </div>

    <!-- Empty State -->
    <div v-else class="bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No cities</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by creating a new city.</p>
        <div class="mt-6">
          <Link
            :href="route('admin.cities.create')"
            class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <svg class="-ml-1 mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add City
          </Link>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Dialog -->
    <div v-if="showDeleteDialog" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
          <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
          </div>
          <h3 class="text-lg font-medium text-gray-900 mt-2">Delete City</h3>
          <div class="mt-2 px-7 py-3">
            <p class="text-sm text-gray-500">
              Are you sure you want to delete "{{ cityToDelete?.name?.en }}"? This action cannot be undone.
            </p>
          </div>
          <div class="items-center px-4 py-3">
            <button
              @click="confirmDelete"
              class="px-4 py-2 bg-red-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300"
            >
              Delete
            </button>
            <button
              @click="cancelDelete"
              class="mt-3 px-4 py-2 bg-white text-gray-500 text-base font-medium rounded-md w-full shadow-sm border border-gray-300 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-300"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Link } from '@inertiajs/inertia-vue3';
import { Inertia } from '@inertiajs/inertia';
import { ref } from 'vue';
import route from 'ziggy';

const props = defineProps({
  cities: {
    type: Object,
    required: true,
  },
  filters: {
    type: Object,
    default: () => ({}),
  },
});

const searchQuery = ref(props.filters.search || '');
const showDeleteDialog = ref(false);
const cityToDelete = ref(null);

function search() {
  Inertia.get(route('admin.cities.index'), { search: searchQuery.value }, {
    preserveState: true,
    replace: true,
  });
}

function clearSearch() {
  searchQuery.value = '';
  Inertia.get(route('admin.cities.index'), {}, {
    preserveState: true,
    replace: true,
  });
}

function deleteCity(city) {
  cityToDelete.value = city;
  showDeleteDialog.value = true;
}

function confirmDelete() {
  if (cityToDelete.value) {
    Inertia.delete(route('admin.cities.destroy', cityToDelete.value.id));
  }
  showDeleteDialog.value = false;
  cityToDelete.value = null;
}

function cancelDelete() {
  showDeleteDialog.value = false;
  cityToDelete.value = null;
}

function formatDate(dateString) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}
</script>
