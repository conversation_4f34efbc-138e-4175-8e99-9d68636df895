<template>
  <div class="max-w-7xl mx-auto">
    <div class="mb-6 lg:mb-10">
      <h3 class="text-lg font-medium leading-6 text-gray-900">Edit City</h3>
      <p class="mt-1 text-sm text-gray-500">
        Update city information and modify its geographical boundary.
      </p>
    </div>

    <form @submit.prevent="submit" class="space-y-6">
      <!-- City Information -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">City Information</h3>
          
          <!-- Name Fields -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                City Name (English) <span class="text-red-500">*</span>
              </label>
              <input 
                v-model="form.name.en" 
                type="text"
                required
                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                :class="{ 'border-red-300': errors['name.en'] }"
              />
              <p v-if="errors['name.en']" class="mt-1 text-sm text-red-600">{{ errors['name.en'] }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">City Name (Kurdish)</label>
              <input 
                v-model="form.name.ku" 
                type="text"
                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                :class="{ 'border-red-300': errors['name.ku'] }"
              />
              <p v-if="errors['name.ku']" class="mt-1 text-sm text-red-600">{{ errors['name.ku'] }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">City Name (Arabic)</label>
              <input 
                v-model="form.name.ar" 
                type="text"
                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                :class="{ 'border-red-300': errors['name.ar'] }"
              />
              <p v-if="errors['name.ar']" class="mt-1 text-sm text-red-600">{{ errors['name.ar'] }}</p>
            </div>
          </div>

          <!-- Description Fields -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Description (English)</label>
              <textarea 
                v-model="form.description.en" 
                rows="3"
                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                :class="{ 'border-red-300': errors['description.en'] }"
              ></textarea>
              <p v-if="errors['description.en']" class="mt-1 text-sm text-red-600">{{ errors['description.en'] }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Description (Kurdish)</label>
              <textarea 
                v-model="form.description.ku" 
                rows="3"
                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                :class="{ 'border-red-300': errors['description.ku'] }"
              ></textarea>
              <p v-if="errors['description.ku']" class="mt-1 text-sm text-red-600">{{ errors['description.ku'] }}</p>
            </div>
             <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Description (ar)</label>
              <textarea 
                v-model="form.description.ar" 
                rows="3"
                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                :class="{ 'border-red-300': errors['description.ar'] }"
              ></textarea>
              <p v-if="errors['description.ar']" class="mt-1 text-sm text-red-600">{{ errors['description.ar'] }}</p>
            </div>
          </div>

          <!-- Map Settings -->
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Center Latitude <span class="text-red-500">*</span>
              </label>
              <input 
                v-model="form.center_coordinates[0]" 
                type="text"
                inputmode="decimal"
                required
                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                :class="{ 'border-red-300': errors['center_coordinates.0'] }"
              />
              <p v-if="errors['center_coordinates.0']" class="mt-1 text-sm text-red-600">{{ errors['center_coordinates.0'] }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Center Longitude <span class="text-red-500">*</span>
              </label>
              <input 
                v-model="form.center_coordinates[1]" 
                type="text"
                inputmode="decimal"
                required
                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                :class="{ 'border-red-300': errors['center_coordinates.1'] }"
              />
              <p v-if="errors['center_coordinates.1']" class="mt-1 text-sm text-red-600">{{ errors['center_coordinates.1'] }}</p>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Zoom Level</label>
              <input 
                v-model.number="form.zoom_level" 
                type="number"
                min="1"
                max="20"
                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                :class="{ 'border-red-300': errors.zoom_level }"
              />
              <p v-if="errors.zoom_level" class="mt-1 text-sm text-red-600">{{ errors.zoom_level }}</p>
            </div>
          </div>

          <!-- Status and Sort Order -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="flex items-center">
              <input 
                v-model="form.is_active" 
                id="is_active"
                type="checkbox" 
                class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
              />
              <label for="is_active" class="ml-2 text-sm text-gray-700">Active</label>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
              <input 
                v-model.number="form.sort_order" 
                type="number"
                min="0"
                class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                :class="{ 'border-red-300': errors.sort_order }"
              />
              <p v-if="errors.sort_order" class="mt-1 text-sm text-red-600">{{ errors.sort_order }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Map and Boundary Drawing -->
      <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
            City Boundary Markers
            <span class="text-sm font-normal text-gray-500">(Click to add, drag to move)</span>
          </h3>
          <div class="mb-3 p-3 bg-blue-50 rounded-md text-sm text-blue-800">
            <strong>Add Markers:</strong> Click anywhere on the map<br>
            <strong>Move Markers:</strong> Drag any marker to reposition<br>
            <strong>Delete Markers:</strong> Right-click on a marker<br>
            <strong>Polygon:</strong> Automatically displays when 3+ markers are placed
          </div>
          
          <div class="h-96 border border-gray-300 rounded-lg overflow-hidden">
            <SimpleMarkerMap
              :center="form.center_coordinates"
              :zoom="form.zoom_level"
              :bounds="form.bounds"
              @center-changed="updateCenter"
              @zoom-changed="updateZoom"
              @bounds-changed="updateBounds"
            />
          </div>
          
          <p v-if="errors.bounds" class="mt-2 text-sm text-red-600">{{ errors.bounds }}</p>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-between">
        <button
          type="button"
          @click="confirmDelete"
          class="bg-red-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          Delete City
        </button>
        
        <div class="flex space-x-3">
          <Link
            :href="route('admin.cities.index')"
            class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Cancel
          </Link>
          <button
            type="submit"
            :disabled="processing"
            class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            <svg v-if="processing" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ processing ? 'Updating...' : 'Update City' }}
          </button>
        </div>
      </div>
    </form>
  </div>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { Link } from '@inertiajs/inertia-vue3'
import { Inertia } from '@inertiajs/inertia'
import SimpleMarkerMap from '../../Components/SimpleMarkerMap.vue'

const props = defineProps({
  city: {
    type: Object,
    required: true
  },
  errors: {
    type: Object,
    default: () => ({})
  }
})

const processing = ref(false)

const form = reactive({
  name: {
    en: props.city.data?.name?.en || '',
    ku: props.city.data?.name?.ku || '',
    ar: props.city.data?.name?.ar || ''
  },
  description: {
    en: props.city.data?.description?.en || '',
    ku: props.city.data?.description?.ku || '',
    ar: props.city.data?.description?.ar || '',
  },
  center_coordinates: props.city.data?.center_coordinates || [36.1911, 44.0094],
  zoom_level: props.city.data?.zoom_level || 12,
  bounds: props.city.data?.bounds || [],
  is_active: props.city.data?.is_active !== undefined ? props.city.data.is_active : true,
  sort_order: props.city.data?.sort_order || 0
})

const updateBounds = (bounds) => {
  form.bounds = bounds
}

const updateCenter = (center) => {
  form.center_coordinates = center
}

const updateZoom = (zoom) => {
  form.zoom_level = zoom
}

const submit = () => {
  processing.value = true
  
  // Create a copy of the form data with parsed float values for coordinates
  const formData = { ...form };
  formData.center_coordinates = [
    parseFloat(form.center_coordinates[0]),
    parseFloat(form.center_coordinates[1])
  ];
  
  Inertia.put(route('admin.cities.update', props.city.data?.id || props.city.id), formData, {
    onFinish: () => {
      processing.value = false
    },
    onSuccess: () => {
      // Redirect will be handled by Laravel
    }
  })
}

const confirmDelete = () => {
  if (confirm('Are you sure you want to delete this city? This action cannot be undone.')) {
    Inertia.delete(route('admin.cities.destroy', props.city.data?.id || props.city.id))
  }
}
</script>
