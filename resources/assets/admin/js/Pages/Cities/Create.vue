<template>
  <div class="max-w-7xl mx-auto">
    <div class="mb-6 lg:mb-10">
      <h3 class="text-lg font-medium leading-6 text-gray-900">Add New City</h3>
      <p class="mt-1 text-sm text-gray-500">
        Create a new city by defining its name, center point, and drawing its geographical boundary.
      </p>
    </div>

    <form @submit.prevent="submit" class="space-y-6 lg:space-y-8">
      <!-- Map Section - Full Width -->
      <div class="bg-white shadow rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
          <h4 class="text-lg font-medium text-gray-900">
            City Boundary Markers
            <span class="text-sm font-normal text-gray-500">(Click to add, drag to move)</span>
          </h4>
          <div class="flex items-center space-x-4">
            <!-- Quick Zoom Controls -->
            <div class="flex items-center space-x-2">
              <label class="text-sm font-medium text-gray-700">Zoom:</label>
              <input 
                v-model.number="form.zoom_level" 
                @input="updateMapZoom"
                type="number"
                min="1"
                max="20"
                class="w-16 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm"
              />
            </div>
          </div>
        </div>
        
        <div class="mb-4 p-3 bg-blue-50 rounded-md text-sm text-blue-800">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <strong>Add Markers:</strong> Click anywhere on the map<br>
              <strong>Move Markers:</strong> Drag any marker to reposition
            </div>
            <div>
              <strong>Delete Markers:</strong> Right-click on a marker<br>
              <strong>Total Markers:</strong> {{ form.bounds?.length || 0 }} markers (polygon shows with 3+)
            </div>
          </div>
        </div>
        
        <!-- Large Map Container -->
        <div class="h-[600px] border border-gray-300 rounded-lg overflow-hidden">
          <SimpleMarkerMap
            ref="mapComponent"
            :center="form.center_coordinates"
            :zoom="form.zoom_level"
            :bounds="form.bounds"
            @center-changed="updateCenter"
            @zoom-changed="updateZoom"
            @bounds-changed="updateBounds"
          />
        </div>
      </div>

      <!-- Form Fields - Two Column Layout -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Left Column: City Information -->
        <div class="space-y-6">
          <!-- City Information -->
          <div class="bg-white shadow rounded-lg p-6">
            <h4 class="text-lg font-medium text-gray-900 mb-4">City Information</h4>
            
            <!-- Name Fields -->
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  City Name (English) <span class="text-red-500">*</span>
                </label>
                <input 
                  v-model="form.name.en" 
                  type="text"
                  required
                  class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  :class="{ 'border-red-300': form.errors['name.en'] }"
                />
                <p v-if="form.errors['name.en']" class="mt-1 text-sm text-red-600">{{ form.errors['name.en'] }}</p>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">City Name (Arabic)</label>
                <input 
                  v-model="form.name.ar" 
                  type="text"
                  class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  :class="{ 'border-red-300': form.errors['name.ar'] }"
                />
                <p v-if="form.errors['name.ar']" class="mt-1 text-sm text-red-600">{{ form.errors['name.ar'] }}</p>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">City Name (Kurdish)</label>
                <input 
                  v-model="form.name.ku" 
                  type="text"
                  class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  :class="{ 'border-red-300': form.errors['name.ku'] }"
                />
                <p v-if="form.errors['name.ku']" class="mt-1 text-sm text-red-600">{{ form.errors['name.ku'] }}</p>
              </div>
            </div>
          </div>

          <!-- Description -->
          <div class="bg-white shadow rounded-lg p-6">
            <h4 class="text-lg font-medium text-gray-900 mb-4">Description</h4>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Description (English)</label>
                <textarea 
                  v-model="form.description.en" 
                  rows="3"
                  class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  :class="{ 'border-red-300': form.errors['description.en'] }"
                ></textarea>
                <p v-if="form.errors['description.en']" class="mt-1 text-sm text-red-600">{{ form.errors['description.en'] }}</p>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Description (Arabic)</label>
                <textarea 
                  v-model="form.description.ar" 
                  rows="3"
                  class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  :class="{ 'border-red-300': form.errors['description.ar'] }"
                ></textarea>
                <p v-if="form.errors['description.ar']" class="mt-1 text-sm text-red-600">{{ form.errors['description.ar'] }}</p>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Description (Kurdish)</label>
                <textarea 
                  v-model="form.description.ku" 
                  rows="3"
                  class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  :class="{ 'border-red-300': form.errors['description.ku'] }"
                ></textarea>
                <p v-if="form.errors['description.ku']" class="mt-1 text-sm text-red-600">{{ form.errors['description.ku'] }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Column: Settings & Boundary Info -->
        <div class="space-y-6">
          <!-- Map Settings -->
          <div class="bg-white shadow rounded-lg p-6">
            <h4 class="text-lg font-medium text-gray-900 mb-4">Map Settings</h4>
            <div class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Center Latitude <span class="text-red-500">*</span>
                  </label>
                  <input 
                    v-model="form.center_coordinates[0]" 
                    @input="updateMapCenter"
                    type="text"
                    inputmode="decimal"
                    required
                    class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                    :class="{ 'border-red-300': form.errors['center_coordinates.0'] }"
                  />
                  <p v-if="form.errors['center_coordinates.0']" class="mt-1 text-sm text-red-600">{{ form.errors['center_coordinates.0'] }}</p>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Center Longitude <span class="text-red-500">*</span>
                  </label>
                  <input 
                    v-model="form.center_coordinates[1]" 
                    @input="updateMapCenter"
                    type="text"
                    inputmode="decimal"
                    required
                    class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                    :class="{ 'border-red-300': form.errors['center_coordinates.1'] }"
                  />
                  <p v-if="form.errors['center_coordinates.1']" class="mt-1 text-sm text-red-600">{{ form.errors['center_coordinates.1'] }}</p>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Default Zoom Level</label>
                <div class="flex items-center space-x-4">
                  <input 
                    v-model.number="form.zoom_level" 
                    @input="updateMapZoom"
                    type="range"
                    min="1"
                    max="20"
                    class="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <input 
                    v-model.number="form.zoom_level" 
                    @input="updateMapZoom"
                    type="number"
                    min="1"
                    max="20"
                    class="w-16 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                    :class="{ 'border-red-300': form.errors.zoom_level }"
                  />
                </div>
                <p v-if="form.errors.zoom_level" class="mt-1 text-sm text-red-600">{{ form.errors.zoom_level }}</p>
              </div>

              <div class="flex items-center">
                <input
                  id="is_active"
                  v-model="form.is_active"
                  type="checkbox"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label for="is_active" class="ml-2 block text-sm text-gray-900">
                  Active
                </label>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                <input 
                  v-model.number="form.sort_order" 
                  type="number"
                  min="0"
                  class="w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500"
                  :class="{ 'border-red-300': form.errors.sort_order }"
                />
                <p v-if="form.errors.sort_order" class="mt-1 text-sm text-red-600">{{ form.errors.sort_order }}</p>
              </div>
            </div>
          </div>

          <!-- Boundary Info -->
          <div class="bg-white shadow rounded-lg p-6">
            <h4 class="text-lg font-medium text-gray-900 mb-4">Boundary Information</h4>
            <div v-if="form.bounds && form.bounds.length > 0" class="space-y-4">
              <div class="grid grid-cols-2 gap-4">
                <div class="text-center p-4 bg-blue-50 rounded-lg">
                  <div class="text-2xl font-bold text-blue-600">{{ form.bounds.length }}</div>
                  <div class="text-sm text-blue-800">Boundary Points</div>
                </div>
                <div class="text-center p-4 bg-green-50 rounded-lg">
                  <div class="text-2xl font-bold text-green-600">{{ boundaryComplete ? 'Yes' : 'No' }}</div>
                  <div class="text-sm text-green-800">Complete Polygon</div>
                </div>
              </div>
              
              <div class="mt-4 space-y-2">
                <button
                  type="button"
                  @click="clearBoundary"
                  class="w-full px-4 py-2 border border-red-300 text-red-700 text-sm rounded-md hover:bg-red-50 transition-colors"
                >
                  <svg class="inline-block w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                  Clear Boundary
                </button>
                
                <!-- Import JSON Data Buttons -->
                <button
                  type="button"
                  @click="pasteJSONData"
                  class="w-full px-4 py-2 border border-blue-300 text-blue-700 text-sm rounded-md hover:bg-blue-50 transition-colors"
                >
                  <svg class="inline-block w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  Paste JSON Coordinates
                </button>
                
                <div class="relative">
                  <input
                    type="file"
                    accept=".json"
                    @change="handleFileUpload"
                    class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    id="json-file-input"
                  />
                  <button
                    type="button"
                    class="w-full px-4 py-2 border border-green-300 text-green-700 text-sm rounded-md hover:bg-green-50 transition-colors"
                  >
                    <svg class="inline-block w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    Import from JSON File
                  </button>
                </div>
              </div>
            </div>
            
            <div v-else class="space-y-4">
              <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m0 0L9 7"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No boundary drawn</h3>
                <p class="mt-1 text-sm text-gray-500">Use the map above to start drawing the city boundary, or import coordinates from JSON.</p>
              </div>
              
              <!-- Import Options -->
              <div class="space-y-2">
                <button
                  type="button"
                  @click="pasteJSONData"
                  class="w-full px-4 py-2 border border-blue-300 text-blue-700 text-sm rounded-md hover:bg-blue-50 transition-colors"
                >
                  <svg class="inline-block w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  Paste JSON Coordinates
                </button>
                
                <div class="relative">
                  <input
                    type="file"
                    accept=".json"
                    @change="handleFileUpload"
                    class="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    id="json-file-input-empty"
                  />
                  <button
                    type="button"
                    class="w-full px-4 py-2 border border-green-300 text-green-700 text-sm rounded-md hover:bg-green-50 transition-colors"
                  >
                    <svg class="inline-block w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    Import from JSON File
                  </button>
                </div>
              </div>
              
              <!-- JSON Format Help -->
              <div class="mt-4 p-3 bg-gray-50 rounded-md text-xs text-gray-600">
                <p class="font-medium mb-2">Supported JSON formats:</p>
                <div class="space-y-1">
                  <p>• <code>[[lng, lat], [lng, lat], ...]</code></p>
                  <p>• <code>[[[lng, lat], [lng, lat], ...]]</code> (nested)</p>
                  <p class="text-gray-500 italic">Coordinates should be in [longitude, latitude] format</p>
                </div>
              </div>
            </div>
            <p v-if="form.errors.bounds" class="mt-2 text-sm text-red-600">{{ form.errors.bounds }}</p>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="flex justify-end space-x-4">
        <button
          type="button"
          :disabled="form.processing"
          @click="cancel"
          class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          :disabled="form.processing || (form.bounds && form.bounds.length < 3)"
          class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          <svg v-if="form.processing" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          {{ form.processing ? 'Creating...' : 'Create City' }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { useForm } from '@inertiajs/inertia-vue3'
import SimpleMarkerMap from '@/Components/SimpleMarkerMap.vue'
import { computed, ref, nextTick } from 'vue'

const mapComponent = ref(null)

const form = useForm({
  name: {
    en: '',
    ar: '',
    ku: ''
  },
  description: {
    en: '',
    ar: '',
    ku: ''
  },
  center_coordinates: [36.1911, 44.0094], // Default to Erbil
  zoom_level: 6,
  bounds: [],
  is_active: true,
  sort_order: 0
})

const boundaryComplete = computed(() => {
  return form.bounds && form.bounds.length >= 1 // At least 1 marker for a city center
})

const updateBounds = (bounds) => {
  form.bounds = bounds
}

const updateCenter = (center) => {
  form.center_coordinates = center
}

const updateZoom = (zoom) => {
  form.zoom_level = zoom
}

// Update map when center coordinates are changed via inputs
const updateMapCenter = () => {
  if (!mapComponent.value) return
  
  try {
    // Get the Leaflet map instance from the SimpleMarkerMap component
    const leafletMap = mapComponent.value.getMap?.()
      
    if (leafletMap) {
      // Parse coordinates as floats to ensure they're valid numbers
      const lat = parseFloat(form.center_coordinates[0]);
      const lng = parseFloat(form.center_coordinates[1]);
      
      // Only update if we have valid coordinates
      if (!isNaN(lat) && !isNaN(lng)) {
        leafletMap.setView([lat, lng], form.zoom_level);
      }
    }
  } catch (error) {
    console.error('Error updating map center:', error);
  }
}

// Update map when zoom level is changed via inputs
const updateMapZoom = () => {
  if (!mapComponent.value) return
  
  try {
    // Get the Leaflet map instance from the SimpleMarkerMap component
    const leafletMap = mapComponent.value.getMap?.()
      
    if (leafletMap) {
      leafletMap.setZoom(form.zoom_level)
    }
  } catch (error) {
    console.error('Error updating map zoom:', error);
  }
}

const clearBoundary = () => {
  try {
    form.bounds = [];
    // Clear all markers from the map component
    if (mapComponent.value && mapComponent.value.clearMarkers) {
      mapComponent.value.clearMarkers();
    }
  } catch (error) {
    console.error('Error clearing boundary:', error);
  }
}

// Function to import and convert JSON coordinates
const importBoundaryFromJSON = (jsonData) => {
  try {
    let coordinates = jsonData;
    
    // Handle different JSON formats
    if (Array.isArray(jsonData) && jsonData.length > 0) {
      // If it's a nested array (like the example), get the first polygon
      if (Array.isArray(jsonData[0]) && Array.isArray(jsonData[0][0])) {
        coordinates = jsonData[0];
        console.log('Using nested array format, extracted first polygon');
      }
      // If it's already a flat array of coordinate pairs
      else if (Array.isArray(jsonData[0]) && jsonData[0].length === 2) {
        coordinates = jsonData;
        console.log('Using flat array format');
      }
    }
    
    // Check if we have a large dataset
    const isLargeDataset = coordinates.length > 500;
    if (isLargeDataset) {
      console.log(`Processing large dataset (${coordinates.length} points). This may take a moment...`);
    }
    
    // For large datasets, process in chunks to prevent UI freezing
    if (isLargeDataset) {
      // First, show a processing message
      const processingMsg = `Processing ${coordinates.length} points. Please wait...`;
      alert(processingMsg);
      
      // Process coordinates in smaller batches using setTimeout
      setTimeout(() => {
        processCoordinates(coordinates);
      }, 100);
      
      return []; // Return empty initially, will be updated by processCoordinates
    } else {
      // For small datasets, process immediately
      return processCoordinates(coordinates);
    }
  } catch (error) {
    console.error('Error importing boundary from JSON:', error);
    alert('Error importing boundary data. Please check the JSON format.');
    return [];
  }
}

// Helper function to process coordinates in chunks
const processCoordinates = (coordinates) => {
  try {
    // Convert [lng, lat] pairs to {lat, lng} objects
    const convertedBounds = coordinates.map(coord => {
      if (Array.isArray(coord) && coord.length >= 2) {
        return {
          lat: parseFloat(coord[1]), // lat is second in [lng, lat] format
          lng: parseFloat(coord[0])  // lng is first in [lng, lat] format
        };
      }
      return coord;
    }).filter(coord => coord && coord.lat && coord.lng); // Filter out invalid coordinates
    
    console.log(`Converted ${convertedBounds.length} valid points out of ${coordinates.length}`);
    
    if (convertedBounds.length === 0) {
      throw new Error('No valid coordinates found in the JSON data');
    }
    
    // Create a completely new array to ensure Vue detects the change
    form.bounds = [...convertedBounds];
    
    // Force the map component to refresh
    if (mapComponent.value) {
      nextTick(() => {
        if (mapComponent.value.updateFromProps) {
          mapComponent.value.updateFromProps(form.bounds);
        }
      });
    }
    
    // Calculate center point from bounds
    if (convertedBounds.length > 0) {
      const latSum = convertedBounds.reduce((sum, point) => sum + point.lat, 0);
      const lngSum = convertedBounds.reduce((sum, point) => sum + point.lng, 0);
      const centerLat = latSum / convertedBounds.length;
      const centerLng = lngSum / convertedBounds.length;
      
      // Update center coordinates
      form.center_coordinates = [centerLat, centerLng];
      
      // Update map center
      updateMapCenter();
    }
    
    if (convertedBounds.length > 300) {
      alert(`Successfully imported ${convertedBounds.length} boundary points! The map may be slow to respond with large datasets.`);
    } else {
      alert(`Successfully imported ${convertedBounds.length} boundary points!`);
    }
    
    return convertedBounds;
  } catch (error) {
    console.error('Error processing coordinates:', error);
    alert('Error processing coordinates. Please check the JSON format.');
    return [];
  }
}

// Function to handle file upload
const handleFileUpload = (event) => {
  const file = event.target.files[0];
  if (!file) return;
  
  const reader = new FileReader();
  reader.onload = (e) => {
    try {
      const jsonData = JSON.parse(e.target.result);
      const imported = importBoundaryFromJSON(jsonData);
      
      if (imported.length > 0) {
        alert(`Successfully imported ${imported.length} boundary points!`);
      } else {
        alert('No valid boundary points found in the file.');
      }
    } catch (error) {
      console.error('Error parsing JSON file:', error);
      alert('Error parsing JSON file. Please ensure it contains valid JSON data.');
    }
  };
  reader.readAsText(file);
}

// Function to paste JSON data directly
const pasteJSONData = () => {
  const jsonText = prompt('Paste your JSON coordinate data:');
  if (!jsonText) return;
  
  try {
    const jsonData = JSON.parse(jsonText);
    const imported = importBoundaryFromJSON(jsonData);
    
    if (imported.length > 0) {
      alert(`Successfully imported ${imported.length} boundary points!`);
    } else {
      alert('No valid boundary points found in the data.');
    }
  } catch (error) {
    console.error('Error parsing JSON data:', error);
    alert('Error parsing JSON data. Please ensure it contains valid JSON.');
  }
}

const submit = () => {
  // Convert string coordinates to numbers before submission
  const formData = { ...form };
  formData.center_coordinates = [
    parseFloat(form.center_coordinates[0]),
    parseFloat(form.center_coordinates[1])
  ];
  
  form.post(route('admin.cities.store'), {
    preserveScroll: true,
    onSuccess: () => {
      // Redirect will be handled by Laravel
    }
  })
}

const cancel = () => {
  window.location.href = route('admin.cities.index')
}
</script>
