<script setup>
import { useForm, usePage } from '@inertiajs/inertia-vue3';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import route from 'ziggy';
import FormSelect from '@/Components/FormSelect.vue';
import { computed, ref } from 'vue';
import { Inertia } from '@inertiajs/inertia';
import FormError from '@/Components/FormError.vue';
import Label from '@/Components/Label.vue';
import FormInput from '@/Components/FormInput.vue';
import { TabGroup, TabList, Tab, TabPanels, TabPanel } from '@headlessui/vue';
import { QuillEditor } from '@vueup/vue-quill';
import vueFilePond from "vue-filepond";
import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type";
import FilePondPluginImagePreview from "filepond-plugin-image-preview";
import AttachmentUploadItem from '@/Components/AttachmentUploadItem.vue';
import useLanguageObject from '@/Composable/useLanguageObject';
import useImageUpload from '@/Composable/useImageUpload';

import '@vueup/vue-quill/dist/vue-quill.snow.css';
import "filepond/dist/filepond.min.css";
import "filepond-plugin-image-preview/dist/filepond-plugin-image-preview.min.css";

const FilePond = vueFilePond(
  FilePondPluginFileValidateType,
  FilePondPluginImagePreview
);

const filePond = ref(null);

const props = defineProps({
  programs: {
    type: Array,
    default: [],
  },
  experts: {
    type: Array,
    default: [],
  },
});

const { languages, languageObject } = useLanguageObject();

const form = useForm({
  program_id: null,
  expert_id: null,
  title: { ...languageObject },
  content: { ...languageObject },
  excerpt: { ...languageObject },
  thumbnail: null,
  attachments: { ...languageObject },
});

const programsOptions = computed(() => {
  return props.programs.map(prog => {
    return { id: prog.id, label: prog.name?.ku, };
  });
});

const expertOptions = computed(() => {
  return props.experts.map(prog => {
    return { id: prog.id, label: prog.name?.ku, };
  });
});

function submit() {
  const thumbnail = filePond.value.getFile()?.file;
  form
    .transform((data) => ({
      ...data,
      thumbnail,
    }))
    .post(route('admin.publications.store'), {
      forceFormData: true,
      preserveScroll: true,
      preserveState: true,
      onSuccess: () => form.reset(),
    });
}

function cancel() {
  Inertia.visit(route('admin.publications.index'));
}

function onAttachmentFileChange(lang, file) {
  form.attachments[lang] = file;
} 
const { modules } = useImageUpload('publications')
</script>  
<template>
  <div class="max-w-7xl mx-auto">
    <div class="mb-6 lg:mb-10">
      <h3 class="text-lg font-medium leading-6 text-gray-900">Create a publication</h3>
    </div>
    <form @submit.prevent="submit" class="space-y-6 lg:space-y-8">
      <div class="flex flex-col lg:flex-row gap-6">
        <div class="lg:w-9/12">
          <TabGroup>
            <TabList class="flex space-x-1 rounded-t-xl rounded-tr-xl bg-white p-1 max-w-lg shadow-sm">
              <Tab v-for="{ name } in languages" as="template" :key="name" v-slot="{ selected }">
                <button :class="[
                  'w-full rounded-lg py-2.5 text-sm font-medium leading-5 uppercase',
                  'ring-white ring-opacity-60 ring-offset-2 ring-offset-primary-400 focus:outline-none focus:ring-2',
                  selected
                    ? 'bg-gray-100 text-primary-700 font-bold'
                    : 'text-gray-600 hover:bg-gray-50',
                ]">
                  {{ name }}
                </button>
              </Tab>
            </TabList>

            <TabPanels>
              <TabPanel v-for="{ iso_code_name, direction, name} in languages" :key="iso_code_name" :class="[
                'rounded-b-xl rounded-tr-xl bg-white p-4 shadow-sm space-y-5',
                'ring-white ring-opacity-60 focus:outline-none',
                `content content-${direction}`
              ]">

                <FormInput :label="`Title (${name})`" v-model="form.title[iso_code_name]" autofocus
                  :error="form.errors[`title.${iso_code_name}`]" />

                 <div :class="{ 'input-error' : !! form.errors[`content.${iso_code_name}`] }">
                  <FormInput 
                    :label="`Excerpt (${name})`" 
                    v-model="form.excerpt[iso_code_name]" type="textarea" rows="4"
                    :error="form.errors[`excerpt.${iso_code_name}`]"
                   />
                </div>

                <div :class="{ 'input-error' : !! form.errors[`content.${iso_code_name}`] }">
                  <Label class="mb-2" :for="`editor-${direction}`">Content ({{ name }})</Label>
                  <QuillEditor :modules="modules" toolbar="full" :id="`editor-${direction}`" v-model:content="form.content[iso_code_name]" theme="snow"
                    content-type="html" />
                  <FormError class="mt-1" v-if="form.errors[`content.${iso_code_name}`]">{{
                  form.errors[`content.${iso_code_name}`] }}</FormError>
                </div>
              </TabPanel>
            </TabPanels>
          </TabGroup>

        </div>
        <aside class="lg:w-3/12">
          <div class="mb-8">
            <Label>Thumbnail</Label>
            <FilePond class="mt-2 filepond-image-uploader" :class="{'input-error': form.errors['thumbnail'] }"
              label-idle="Drag & Drop thumbnail or <span class='underline'>Browse</span>" ref="filePond"
              accepted-file-types="image/jpeg, image/png" />
            <FormError class="mt-1" v-if="form.errors['thumbnail']">{{ form.errors['thumbnail'] }}</FormError>
          </div>
          <div>
            <p class="mb-1 text-sm font-medium text-gray-700">Attachments (PDF)</p>
            <div class="overflow-hidden bg-white shadow sm:rounded-md">
              <ul role="list" class="divide-y divide-gray-200">
                <AttachmentUploadItem v-for="{iso_code_name} in languages" :key="iso_code_name"
                  :short-name="iso_code_name" :has-uploaded-file="false" id=""
                  :error="form.errors[`attachments.${iso_code_name}`]" @change="onAttachmentFileChange" />
              </ul>
            </div>
            <template v-for="{iso_code_name} in languages" :key="iso_code_name">
              <FormError class="mt-1" v-if="form.errors[`attachments.${iso_code_name}`]">{{
              form.errors[`attachments.${iso_code_name}`] }}</FormError>
            </template>
          </div>
        </aside>
      </div>

      <div class="flex gap-4">
        <PrimaryButton class="px-5 py-2.5 text-base" :loading="form.processing" :disabled="form.processing"
          @click="submit">Create</PrimaryButton>
        <SecondaryButton class="px-5 py-2.5 text-base" :disabled="form.processing" @click="cancel">Cancel
        </SecondaryButton>
      </div>
    </form>
  </div>
</template>

