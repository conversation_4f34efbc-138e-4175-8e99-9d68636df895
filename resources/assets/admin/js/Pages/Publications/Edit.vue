<script setup>
import { useForm } from '@inertiajs/inertia-vue3';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import route from 'ziggy';
import FormSelect from '@/Components/FormSelect.vue';
import { computed, ref } from 'vue';
import { Inertia } from '@inertiajs/inertia';
import FormError from '@/Components/FormError.vue';
import Label from '@/Components/Label.vue';
import FormInput from '@/Components/FormInput.vue';
import { TabGroup, TabList, Tab, TabPanels, TabPanel } from '@headlessui/vue';
import { QuillEditor } from '@vueup/vue-quill';
import vueFilePond from "vue-filepond";
import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type";
import FilePondPluginImagePreview from "filepond-plugin-image-preview";
import DeleteDialog from '@/Components/DeleteDialog.vue';
import AttachmentUploadItem from '@/Components/AttachmentUploadItem.vue';
import useLanguageObject from '@/Composable/useLanguageObject';
import useImageUpload from '@/Composable/useImageUpload';

import '@vueup/vue-quill/dist/vue-quill.snow.css';
import "filepond/dist/filepond.min.css";
import "filepond-plugin-image-preview/dist/filepond-plugin-image-preview.min.css";

const FilePond = vueFilePond(
  FilePondPluginFileValidateType,
  FilePondPluginImagePreview
);

const filePond = ref(null);
const files = ref([])

const props = defineProps({
  publication: {
    type: Object,
    default: () => ({}),
  },
  thumbnail: {
    type: Object,
    default: null,
  },
  attachments: {
    type: Array,
    default: () => ([]),
  },
  programs: {
    type: Array,
    default: [],
  },
  experts: {
    type: Array,
    default: [],
  },
});

const { languages, languageObject } = useLanguageObject();

const programsOptions = computed(() => {
  return props.programs.map(prog => {
    return { id: prog.id, label: prog.name?.ku, };
  });
});

const expertOptions = computed(() => {
  return props.experts.map(prog => {
    return { id: prog.id, label: prog.name?.ku, };
  });
});


const form = useForm({
  program_id: props.publication.program_id,
  expert_id: props.publication.expert_id,
  title: { ...languageObject, ...props.publication.title },
  content: { ...languageObject, ...props.publication.content },
  excerpt: { ...languageObject, ...props.publication.excerpt },
  thumbnail: props.publication.thumbnail,
  attachments: { ...languageObject },
  thumbnailHasChanged: false,
});


function submit() {
  const thumbnail = filePond.value.getFile()?.file;
  form
    .transform((data) => ({
      ...data,
      thumbnail,
      _method: 'PUT'
    }))
    .post(route('admin.publications.update', props.publication.id), {
      forceFormData: true,
      preserveScroll: true,
      preserveState: true,
    });
}

function cancel() {
  Inertia.visit(route('admin.publications.index'));
}

const attachmentList = computed(() => {
  const map = {};
  Object.values(languages).forEach(({ iso_code_name }) => {
    map[iso_code_name] = {
      id: '',
      originalUrl: '',
      shortName: iso_code_name,
      fileName: '',
      hasUploadedFile: false,
      error: form.errors[`attachment.${iso_code_name}`],
    };
  });

  props.attachments.forEach(({ id, file_name, original_url, custom_properties }) => {
    map[custom_properties.lang] = {
      id: id,
      originalUrl: original_url,
      shortName: custom_properties.lang,
      fileName: file_name,
      hasUploadedFile: true,
      error: null,
    };
  });

  return map;
})
function onAttachmentFileChange(lang, file) {
  form.attachments[lang] = file;
}

const openDeleteAttachmentDialog = ref(false);
const deletableAttachmentId = ref(false);

function showDeleteAttachmentDialog(id) {
  openDeleteAttachmentDialog.value = true;
  deletableAttachmentId.value = id;
}

function closeDeleteAttachmentDialog() {
  openDeleteAttachmentDialog.value = false;
  deletableAttachmentId.value = null;
}
function changeThumbnail() {
  form.thumbnailHasChanged = true;
}
const { modules } = useImageUpload('publications')
</script>  
<template>
  <div class="max-w-6xl mx-auto">
    <div class="mb-6 lg:mb-10">
      <h3 class="text-lg font-medium leading-6 text-gray-900">Edit publication</h3>
    </div>
    <form @submit.prevent="submit" class="space-y-6 lg:space-y-8">
      <div class="flex flex-col lg:flex-row gap-6">
        <div class="lg:w-8/12">
          <TabGroup>
            <TabList class="flex space-x-1 rounded-t-xl rounded-tr-xl bg-white p-1 max-w-lg shadow-sm">
              <Tab v-for="{ name } in languages" as="template" :key="name" v-slot="{ selected }">
                <button :class="[
                  'w-full rounded-lg py-2.5 text-sm font-medium leading-5 uppercase',
                  'ring-white ring-opacity-60 ring-offset-2 ring-offset-primary-400 focus:outline-none focus:ring-2',
                  selected
                    ? 'bg-gray-100 text-primary-700 font-bold'
                    : 'text-gray-600 hover:bg-gray-50',
                ]">
                  {{ name }}
                </button>
              </Tab>
            </TabList>

            <TabPanels>
              <TabPanel v-for="{ iso_code_name, direction, name} in languages" :key="iso_code_name" :class="[
                'rounded-b-xl rounded-tr-xl bg-white p-4 shadow-sm space-y-5',
                'ring-white ring-opacity-60 focus:outline-none',
                `content content-${direction}`
              ]">

                <FormInput :label="`Title (${name})`" v-model="form.title[iso_code_name]" autofocus
                  :error="form.errors[`title.${iso_code_name}`]" />

                 <div :class="{ 'input-error' : !! form.errors[`content.${iso_code_name}`] }">
                  <FormInput 
                    :label="`Excerpt (${name})`" 
                    v-model="form.excerpt[iso_code_name]" type="textarea" rows="4"
                    :error="form.errors[`excerpt.${iso_code_name}`]"
                   />
                </div>

                <div :class="{ 'input-error' : !! form.errors[`content.${iso_code_name}`] }">
                  <Label class="mb-2" :for="`editor-${direction}`">Content ({{ name }})</Label>
                  <QuillEditor :modules="modules" toolbar="full" :id="`editor-${direction}`" v-model:content="form.content[iso_code_name]" theme="snow"
                    content-type="html" />
                  <FormError class="mt-1" v-if="form.errors[`content.${iso_code_name}`]">{{
                  form.errors[`content.${iso_code_name}`] }}</FormError>
                </div>
              </TabPanel>
            </TabPanels>
          </TabGroup>

        </div>
        <aside class="lg:w-4/12">
          <div class="mb-8">
            <div v-if="props.thumbnail" class="mb-6">
              <Label>Current Thumbnail</Label>
              <img :src="props.thumbnail.original_url"
                class="mt-1 w-full h-48 object-cover border border-white rounded-xl overflow-hidden" alt="">
            </div>
            <Label>Upload Thumbnail</Label>
            <FilePond class="mt-2 filepond-image-uploader" :class="{'input-error': form.errors['thumbnail'] }"
              label-idle="Drag & Drop thumbnail or <span class='underline'>Browse</span>" ref="filePond"
              @updatefiles="changeThumbnail" accepted-file-types="image/jpeg, image/png" />
            <FormError class="mt-1" v-if="form.errors['thumbnail']">{{ form.errors['thumbnail'] }}</FormError>
          </div>
          <div>
            <p class="mb-1 text-sm font-medium text-gray-700">Attachments (PDF)</p>
            <div class="overflow-hidden bg-white shadow sm:rounded-md">
              <ul role="list" class="divide-y divide-gray-200 overflow-x-auto">
                <AttachmentUploadItem v-for="attachment in attachmentList" :key="attachment.shortName"
                  v-bind="attachment" @change="onAttachmentFileChange"
                  @remove-attachment="showDeleteAttachmentDialog" />
              </ul>
            </div>
            <template v-for="{iso_code_name} in languages" :key="iso_code_name">
              <FormError class="mt-1" v-if="form.errors[`attachments.${iso_code_name}`]">{{
              form.errors[`attachments.${iso_code_name}`] }}</FormError>
            </template>
          </div>
        </aside>
      </div>

      <div class="flex gap-4">
        <PrimaryButton class="px-5 py-2.5 text-base" :loading="form.processing" :disabled="form.processing"
          @click="submit">Update</PrimaryButton>
        <SecondaryButton class="px-5 py-2.5 text-base" :disabled="form.processing" @click="cancel">Cancel
        </SecondaryButton>
      </div>
    </form>
  </div>

  <DeleteDialog preserve-state :open="openDeleteAttachmentDialog" @close="closeDeleteAttachmentDialog"
    route="admin.publications.destroyMedia" row-name="Attachment" :row-id="deletableAttachmentId" />
</template>  