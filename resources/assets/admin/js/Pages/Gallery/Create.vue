<script setup>
import Modal from '@/Components/Modal.vue';
import { useForm } from '@inertiajs/inertia-vue3';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import SecondaryButton from '@/Components/SecondaryButton.vue';
import route from 'ziggy';
import FormInput from '@/Components/FormInput.vue';
import { computed } from 'vue';
import FormError from '@/Components/FormError.vue';

const form = useForm({
  caption: '',
  photo: null,
});

const imgPreview = computed(() => {
  if (!form.photo) return null;
  console.log(form.photo);
  return URL.createObjectURL(form.photo);
});

function submit() {
  form.post(route('admin.galleries.store'), {
    preserveScroll: true,
    preserveState: true,
    onSuccess: () => form.reset('name.*'),
  });
}

</script>  
<template>
  <Modal width="2xl" title="Upload a new photo">
    <form @submit.prevent="submit" class="space-y-5">
      <FormInput type="text" name="caption" label="Caption (optional)" v-model="form.caption" :error="form.errors.caption" />

      <div>
        <label for="photo" class="mb-2 inline-block text-neutral-700 dark:text-neutral-200">Photo</label>
        <input
          class="relative m-0 block w-full min-w-0 flex-auto rounded border border-solid border-neutral-300 bg-clip-padding px-3 py-[0.32rem] text-base font-normal text-neutral-700 transition duration-300 ease-in-out file:-mx-3 file:-my-[0.32rem] file:overflow-hidden file:rounded-none file:border-0 file:border-solid file:border-inherit file:bg-neutral-100 file:px-3 file:py-[0.32rem] file:text-neutral-700 file:transition file:duration-150 file:ease-in-out file:[border-inline-end-width:1px] file:[margin-inline-end:0.75rem] hover:file:bg-neutral-200 focus:border-primary focus:text-neutral-700 focus:shadow-te-primary focus:outline-none dark:border-neutral-600 dark:text-neutral-200 dark:file:bg-neutral-700 dark:file:text-neutral-100 dark:focus:border-primary"
          type="file" id="photo" @change="(e) => form.photo = e.target.files[0]" />
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-300" id="file_input_help">PNG, JPG or GIF (MAX. 2MB).
        </p>
        <FormError v-if="form.errors.photo">{{ form.errors.photo }}</FormError>
      </div>

      <img v-if="imgPreview" class="max-w-[300px] w-full" :src="imgPreview" alt="" role="presentation">
    </form>

    <template #footer="{ close }">
      <PrimaryButton :loading="form.processing" :disabled="form.processing" @click="submit">Upload</PrimaryButton>
      <SecondaryButton :disabled="form.processing" @click="close">Close</SecondaryButton>
    </template>
  </Modal>
</template>
