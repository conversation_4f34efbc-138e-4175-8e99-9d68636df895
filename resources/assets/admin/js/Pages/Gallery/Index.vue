<script setup>
import { ref, watch } from 'vue';
import route from 'ziggy';
import { Inertia } from '@inertiajs/inertia';
import { usePage } from '@inertiajs/inertia-vue3';
import PageHeading from '@/Components/PageHeading.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import TextButton from '@/Components/TextButton.vue';
import Pagination from '@/Components/Pagination.vue'
import DeleteDialog from '@/Components/DeleteDialog.vue';
import FormSelect from '@/Components/FormSelect.vue';
import SearchInput from '@/Components/SearchInput.vue';
import { PencilIcon, TrashIcon } from '@heroicons/vue/24/outline';

defineProps({
  galleries: {
    type: Object,
    default: () => ({})
  },
  filterOptions: Object,
});

const isDeleteDialogOpen = ref(false);
const deletableRowId = ref(null);

const filters = ref(usePage().props.value.filters);

watch(filters, (value) => {
  Inertia.visit(route('admin.galleries.index'), {
    data: value,
    preserveState: true,
    preserveScroll: true,
    only: ['modal', 'flash', 'galleries', 'filters']
  });
}, { deep: true })

function upload() {
  Inertia.visit(route('admin.galleries.create'), { only: ['modal', 'flash',] });
}

function tryDelete(id) {
  deletableRowId.value = id;
  isDeleteDialogOpen.value = true;
}

function editRow(item) {
  Inertia.visit(route('admin.galleries.edit', item.id), { preserveScroll: true, preserveState: true })
}

async function closeDeleteDialog() {
  isDeleteDialogOpen.value = false;
  deletableRowId.value = null;
}
</script>

<template>
  <PageHeading heading="Galleries">
    <template #actions>
      <div class="space-x-3 flex items-end">
        <SearchInput placeholder="Search" v-model="filters.search" />
        <PrimaryButton @click="upload">Upload new photo</PrimaryButton>
      </div>
    </template>
  </PageHeading>

  <div class="mt-8 flex flex-col">
    <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg bg-white">
          <ul role="list"
            class="p-4 lg:p-8 grid grid-cols-2 gap-x-4 gap-y-8 sm:grid-cols-3 sm:gap-x-6 lg:grid-cols-4 xl:gap-x-8">
            <li v-for="gallery in galleries.data" :key="gallery.id" class="relative">
              <div
                class="relative block overflow-hidden rounded-lg bg-gray-100">
                <img :src="gallery.thumbnail" alt=""
                  class="w-[150x] pointer-events-none object-cover" />
              </div>
              <div class="flex items-start justify-between mt-3 ">
                <div class="flex-1">
                  <p class="pointer-events-none block truncate text-sm font-medium text-zinc-900">{{ gallery.caption
                  }}
                  </p>
                  <p class="pointer-events-none block text-sm font-medium text-gray-500">{{ gallery.created_at }}</p>
                </div>
                <div class="flex-shrink-0">
                  <button type="button" class="inline-flex items-center justify-center w-7 h-7 focus-within:ring-2 focus-within:ring-primary-500 focus-within:ring-offset-2 focus-within:ring-offset-gray-100">
                    <TrashIcon class="w-5 h-5 text-zinc-400 hover:text-red-500" aria-hidden="true" @click="tryDelete(gallery.id)" />
                  </button>
                  <button type="button" class="inline-flex items-center justify-center w-7 h-7 focus-within:ring-2 focus-within:ring-primary-500 focus-within:ring-offset-2 focus-within:ring-offset-gray-100">
                    <PencilIcon class="w-5 h-5 text-zinc-400 hover:text-red-500" aria-hidden="true" @click="editRow(gallery)" />
                  </button>
                </div>
              </div>
            </li>
          </ul>
          <Pagination :pages="galleries.meta.links" :links="galleries.links" :currnet-page="galleries.meta.current_page"
            :from="galleries.meta.from" :to="galleries.meta.to" :total="galleries.meta.total" />
        </div>
      </div>
    </div>
  </div>

  <DeleteDialog row-name="Gallery" route="admin.galleries.destroy" :row-id="deletableRowId" :open="isDeleteDialogOpen"
    @close="closeDeleteDialog" />
</template>
