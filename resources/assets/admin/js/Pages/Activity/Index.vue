<script setup>
import { Inertia } from '@inertiajs/inertia';
import route from 'ziggy';
import PageHeading from '@/Components/PageHeading.vue';
import TextButton from '@/Components/TextButton.vue';
import Pagination from '@/Components/Pagination.vue'
import SearchInput from '@/Components/SearchInput.vue';
import { reactive, ref, watch } from 'vue';
import { usePage } from '@inertiajs/inertia-vue3';
import SlideOver from '../../Components/SlideOver.vue';

defineProps({
  activities: {
    type: Object,
    default: () => ({})
  },
});

const search = ref(usePage().props.value.search);

watch(search, (value) => {
  Inertia.visit(route('admin.activities.index'), { data: { 'search': value }, preserveState: true, preserveScroll: true, });
})

const changeSlideOver = reactive({
  show: false,
  changes: {},
  action: null,
  model: null,
  date: null,
  time: null,
});

function showChangesSlideOver(item) {
  changeSlideOver.show = true;
  changeSlideOver.changes = item.changes;
  changeSlideOver.action = item.action;
  changeSlideOver.model = item.model;
  changeSlideOver.date = item.created_at;
  changeSlideOver.time = item.time;
}

function closeChangesSlideOver() {
  changeSlideOver.show = false;
  changeSlideOver.changes = {};
  changeSlideOver.action = null;
  changeSlideOver.model = null;
  changeSlideOver.date = null;
  changeSlideOver.time = null;
}
</script>
  
<template>
  <PageHeading heading="Activities">
    <template #actions>
      <div class="space-x-3">
        <!-- <SearchInput placeholder="Search by name" v-model="search" /> -->
      </div>
    </template>
  </PageHeading>

  <div class="mt-8 flex flex-col">
    <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
      <div class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">User
                </th>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Action
                </th>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Model
                </th>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900">Date
                </th>
                <th scope="col" class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                  <span class="sr-only">View Changes</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 bg-white">
              <tr v-for="item in activities.data" :key="item.id">
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                  {{ item.causer }}
                </td>
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900">
                  <span :class="[
                      'text-xs uppercase border font-semibold inline-block px-2 py-0.5 rounded',
                      {'text-red-500 border-red-500 bg-red-50': item.action == 'deleted'},
                      {'text-green-500 border-green-500 bg-green-50': item.action == 'created'},
                      {'text-blue-500 border-blue-500 bg-blue-50': item.action == 'updated'},
                  ]">
                    {{ item.action }}
                  </span>
                </td>
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900">
                  {{ item.model }}
                </td>
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900">
                  {{ item.created_at }} <span class="inline-block py-0.5 px-1 text-xs border rounded bg-gray-50">{{
                    item.time }}</span>
                </td>
                <td class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6 space-x-2">
                  <TextButton color="gray" @click="showChangesSlideOver(item)">View Changes</TextButton>
                </td>
              </tr>
            </tbody>
          </table>
          <Pagination :pages="activities.meta.links" :links="activities.links"
            :currnet-page="activities.meta.current_page" :from="activities.meta.from" :to="activities.meta.to"
            :total="activities.meta.total" />
        </div>
      </div>
    </div>
  </div>

  <SlideOver 
    :changes="changeSlideOver.changes" 
    :model="changeSlideOver.model"
    :action="changeSlideOver.action"
    :open="changeSlideOver.show" 
    :date="changeSlideOver.date"
    :time="changeSlideOver.time"
    @close="closeChangesSlideOver"
  />
</template>  