<script setup>
import SettingInput from './SettingInput.vue';
import PageHeading from '@/Components/PageHeading.vue';

const { settings } = defineProps({
  settings: {
    type: Object,
    default: null,
  },
});
</script>  
<template>
  <div class="max-w-lg mx-auto">
    <div class="mb-6 lg:mb-10">
      <PageHeading heading="Edit settings" description="All settings can be updated without submitting."/>
    </div>
    <div>
      <div class="bg-white shadow-sm mb-6 p-4 rounded-lg">
        <h3 class="font-bold text-lg text-gray-800 capitalize mb-6">{{ countryName }}</h3>
        <div class="space-y-5">
          <SettingInput v-for="{id, key, value} in settings" :key="id" :id="id" :key-name="key" :value="value" />
        </div>
      </div>
    </div>
  </div>
</template>
  
  