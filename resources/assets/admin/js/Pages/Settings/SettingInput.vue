<script setup>
import route from 'ziggy';
import { useForm } from '@inertiajs/inertia-vue3';
import FormInput from '@/Components/FormInput.vue';
import LoadingIndicatior from '@/Components/LoadingIndicatior.vue';
import Checkbox from '@/Components/Checkbox.vue';
import { ref, watch, computed } from 'vue';

const { keyName, value, id } = defineProps({
    id: [String, Number],
    keyName: String,
    value: [String, Number, Boolean]
});

// Convert string "true"/"false" to actual boolean values
const normalizedValue = computed(() => {
    if (value === "true" || value === true) return true;
    if (value === "false" || value === false) return false;
    return value;
});

// Determine if this is a boolean setting that should use a checkbox
const isBoolean = computed(() => {
    // List of settings that should be rendered as checkboxes
    const booleanSettings = ['show_news_page', 'show_careers_page'];
    return booleanSettings.includes(keyName);
});

const form = useForm({
    [keyName]: normalizedValue.value
});

const loading = ref(false);

const isEdited = ref(false);

watch(() => form, () => {
    isEdited.value = true;
}, { deep: true });

function update() {
    if(!isEdited.value && !isBoolean.value) {
        return;
    }
    
    form.put(route('admin.settings.update', id), {
        onBefore: () => loading.value = true,
        onFinish: () => {
            loading.value = false;
            isEdited.value = false;
        },
    });
}

// Handle checkbox changes specifically
function handleCheckboxChange(value) {
    form[keyName] = value;
    // Always update on checkbox change, don't rely on isEdited flag
    form.put(route('admin.settings.update', id), {
        onBefore: () => loading.value = true,
        onFinish: () => {
            loading.value = false;
            isEdited.value = false;
        },
    });
}
</script>
<template>
    <!-- Render checkbox for boolean settings -->
    <div v-if="isBoolean" class="flex items-center">
        <label :for="keyName" class="mr-2 text-sm font-medium text-gray-700 capitalize">
            {{ keyName.replace(/_/g, ' ') }}
        </label>
        <Checkbox 
            :id="keyName"
            :checked="form[keyName]" 
            @update:checked="handleCheckboxChange"
        />
        <span v-if="loading" class="ml-2">
            <LoadingIndicatior class="text-primary-500 h-4 w-4" />
        </span>
    </div>
    
    <!-- Render text input for non-boolean settings -->
    <FormInput 
        v-else 
        :label="keyName.replace(/_/g, ' ')" 
        v-model="form[keyName]" 
        @blur="update"
    >
        <template v-if="loading" #loading>
            <div class="flex justify-center items-center w-8 absolute top-0 mt-2 right-1 bg-white">
                <LoadingIndicatior class="text-primary-500 mr-0" />
            </div>
        </template>
    </FormInput>
</template>