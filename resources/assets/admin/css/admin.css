@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {

.dp__theme_light {
      --dp-background-color: #fff;
      --dp-text-color: #212121;
      --dp-hover-color: #f3f3f3;
      --dp-hover-text-color: #212121;
      --dp-hover-icon-color: #959595;
      --dp-primary-color: theme(colors.primary.500);
      --dp-primary-text-color: #f8f5f5;
      --dp-secondary-color: #c0c4cc;
      --dp-border-color: theme(colors.gray.300);
      --dp-menu-border-color: theme(colors.gray.300);
      --dp-border-color-hover: theme(colors.primary.500);
      --dp-disabled-color: #f6f6f6;
      --dp-scroll-bar-background: #f3f3f3;
      --dp-scroll-bar-color: #959595;
      --dp-success-color: #76d275;
      --dp-success-color-disabled: #a3d9b1;
      --dp-icon-color: theme(colors.gray.400);
      --dp-danger-color: theme(colors.red.500);
      --dp-highlight-color: rgba(25, 118, 210, 0.1);
   }

   .dp__menu {
    @apply rounded-md overflow-hidden;
   }

   .input-error {
      --dp-border-color: theme(colors.red.300);
      --dp-hover-icon-color: theme(colors.red.300);
      --dp-icon-color: theme(colors.red.300);
   }
  
   .dp__pointer {
    @apply shadow-sm rounded-md focus:outline-none focus:border-primary-500 focus:ring-primary-500;
   }

   .ql-toolbar {
    @apply rounded-tl-md rounded-tr-md;
  }
  .ql-container {
    @apply rounded-bl-md rounded-br-md focus-within:outline-none focus-within:ring-2 focus-within:ring-primary-500;
  }

  .ql-snow {
    @apply border border-gray-300 shadow-sm !important;
  }

  .input-error .ql-snow {
    @apply border-red-300 !important;
  }

  .ql-editor {
    min-height: 300px;
  }

  .content-rtl .ql-editor,
  .content-rtl input ,
  .content-rtl textarea   {
    direction: rtl;
    text-align: right;
  }


  .filepond--credits {
      @apply hidden;
  }

  .filepond--panel-root {
      background-color: theme(colors.gray.200) !important;
  }

  .input-error .filepond--panel-root {
      border-width: 1px;
      border-style: solid;
      border-color: theme(colors.red.300) !important;
  }
}