<x-frontend::app-layout class="ceo-message-page">
   <div class="ceo-header relative overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-b from-[#ECF7FF] to-white">
         <div class="absolute inset-0 opacity-30">
            <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
               <defs>
                  <pattern id="squares" x="0" y="0" width="28" height="28" patternUnits="userSpaceOnUse">
                     <rect x="0.5" y="0.5" width="27" height="27" stroke="currentColor" stroke-width="0.5" fill="none" class="text-primary/20" />
                  </pattern>
               </defs>
               <rect width="100%" height="100%" fill="url(#squares)" />
            </svg>
         </div>

         <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-primary/50 to-transparent transform -skew-x-12 animate-wave"></div>
         </div>
      </div>

      <x-frontend::navbar />

      <div class="relative z-10">
         <x-container class="py-4 mb-3 lg:py-6">
            <nav class="flex" aria-label="Breadcrumb" data-animation="slideUp" data-duration="800">
               <ol class="inline-flex items-center space-x-1 md:space-x-3 text-sm">
                  <li class="inline-flex flex-nowrap items-center">
                     <a href="{{ app_route('home') }}" class="inline-flex flex-nowrap items-center text-gray-600 hover:text-primary transition-colors">
                        <svg class="w-4 h-4 me-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                           <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                        </svg>
                        Home
                     </a>
                  </li>
                  <li aria-current="page">
                     <div class="flex items-center">
                        <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                           <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="ml-1 text-gray-500 md:ml-2 font-medium">{{ __('ceo_message') }}</span>
                     </div>
                  </li>
               </ol>
            </nav>
         </x-container>
      </div>
   </div>

   <main>
      <x-section class="py-10 lg:py-16">
         <x-container>
            <div class="grid grid-cols-12 gap-6 lg:gap-8">
               <!-- <div class="col-span-12 md:col-span-5 lg:col-span-4">
                  <div class="w-full h-full rounded-xl bg-white border p-2 relative">
                     <img src="{{ asset('images/ceo_photo.jpeg') }}" class="object-cover w-full h-full rounded-xl" loading="lazy" alt="Heresh Doski - CEO" />
                     <div class="absolute bottom-2 left-0 w-full px-2">
                        <div class="bg-zinc-900/80 rounded-xl overflow-hidden p-4">
                           <h3 class="text-xl font-bold text-white mb-1">Heresh Doski</h3>
                           <p class="text-primary font-medium">Chief Executive Officer</p>
                        </div>
                     </div>
                  </div>
               </div> -->
               <div class="col-span-12  "> <!-- md:col-span-7 lg:col-span-8 -->
                  <div class="prose prose-lg mx-auto" data-animation="slideUp" data-duration="800">
                     <h1 class="text-2xl lg:text-3xl font-bold leading-tight mb-4" data-animation="slideUp" data-duration="1000" data-delay="200">
                        <span class="text-gray-900">{{ __('ceo_message') }}</span>
                     </h1>
                     <p class="mb-6">
                        {{ __('ceo_msg_p1') }}
                     </p>

                     <p class="mb-6">
                        {{ __('ceo_msg_p2') }}
                     </p>

                     <p class="mb-6">
                        {{ __('ceo_msg_p3') }}
                     </p>

                     <p class="mb-10">
                        {{ __('ceo_msg_p4') }}
                     </p>
                     <hr />
                     <h3 class="text-2xl font-bold mb-1">{{ __('heresh_doski') }}</h3>
                     <p class="text-primary font-medium">{{ __('chief_executive_officer') }}</p>
                  </div>
               </div>
            </div>
         </x-container>
      </x-section>
   </main>
</x-frontend::app-layout>