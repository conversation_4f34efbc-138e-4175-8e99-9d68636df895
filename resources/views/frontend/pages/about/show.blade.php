<x-frontend::app-layout class="about-page">
   <div class="about-header relative overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-r from-gray-50 via-gray-100 to-gray-50 animate-gradient-x">
         <div class="absolute inset-0 opacity-30">
            <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
               <defs>
                  <pattern id="squares" x="0" y="0" width="28" height="28" patternUnits="userSpaceOnUse">
                     <rect x="0.5" y="0.5" width="27" height="27" stroke="currentColor" stroke-width="0.5" fill="none" class="text-primary/20" />
                  </pattern>
               </defs>
               <rect width="100%" height="100%" fill="url(#squares)" />
            </svg>
         </div>

         <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-primary/50 to-transparent transform -skew-x-12 animate-wave"></div>
         </div>
      </div>

      <x-frontend::navbar />

      <div class="relative z-10">
         <x-container class="py-20 lg:py-32">
            <div class="grid grid-cols-1 lg:grid-cols-12 gap-8 lg:gap-12 items-center">

               <div class="lg:col-span-6 text-gray-900">
                  <nav class="flex mb-8" aria-label="Breadcrumb" data-animation="slideUp" data-duration="800">
                     <ol class="inline-flex items-center space-x-1 md:space-x-3 text-sm">
                        <li class="inline-flex items-center">
                           <a href="{{ route('home') }}" class="text-gray-600 hover:text-primary transition-colors">
                              Home
                           </a>
                        </li>
                        <li>
                           <div class="flex items-center">
                              <x-icons.chevron-right class="w-4 h-4 text-gray-400 mx-2" />
                              <span class="text-primary font-medium">About Us</span>
                           </div>
                        </li>
                     </ol>
                  </nav>

                  <div class="mb-8" data-animation="slideUp" data-duration="1000" data-delay="200">
                     <h1 class="text-4xl lg:text-6xl font-bold leading-tight mb-4">
                        <span class="text-gray-900">We Are</span>
                        <span class="text-primary">BIM</span>
                     </h1>
                  </div>

                  <p class="text-lg text-gray-600 leading-relaxed mb-8 max-w-2xl" data-animation="slideUp" data-duration="1200" data-delay="400">
                     Based in Erbil, Iraq, we specialize in construction, engineering, maintenance services, supply chain management and procurement, delivering sustainable, high-quality solutions with precision and innovation.
                  </p>
               </div>

               <div class="lg:col-span-6 order-first lg:order-last" data-animation="slideRight" data-duration="1200" data-delay="600">
                  <div class="relative h-full min-h-[400px] md:min-h-[500px] lg:min-h-[550px]">
                     <!-- Main image container with creative design -->
                     <div class="relative">                        
                        <!-- Main image frame -->
                        <div class="relative bg-white rounded-3xl p-3 lg:p-4 shadow-2xl border border-gray-100 h-full">
                           <div class="relative overflow-hidden rounded-2xl h-full">
                              <img src="{{ asset('images/about_header_pic2.jpeg') }}"
                                 alt="BIM Company"
                                 class="w-full h-full object-cover min-h-[400px] md:min-h-[500px] lg:min-h-[550px]">
                              
                              <!-- Image overlay gradient -->
                              <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                           </div>
                        </div>
                     </div>

                     <!-- Floating elements - Responsive positioning -->
                     <div class="absolute top-4 lg:top-6 -right-2 lg:-right-6 bg-white rounded-xl lg:rounded-2xl p-4 lg:p-6 shadow-xl border border-gray-100 max-w-xs w-64 lg:w-72" data-animation="fadeIn" data-duration="1000" data-delay="1000">
                        <div class="flex items-start space-x-3 lg:space-x-4">
                           <div class="w-10 h-10 lg:w-12 lg:h-12 bg-gradient-to-br from-primary to-primary-600 rounded-lg lg:rounded-xl flex items-center justify-center flex-shrink-0">
                              <svg class="w-5 h-5 lg:w-6 lg:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                 <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                              </svg>
                           </div>
                           <div>
                              <h3 class="font-bold text-gray-900 text-xs lg:text-sm mb-1">Quality Service</h3>
                              <p class="text-xs text-gray-600 leading-relaxed">Building sustainable infrastructure across Iraq</p>
                           </div>
                        </div>
                     </div>

                     <!-- Bottom floating card - Responsive positioning -->
                     <div class="absolute -bottom-6 lg:-bottom-10 -left-4 lg:-left-10 bg-white rounded-xl lg:rounded-2xl p-4 lg:p-6 shadow-xl border border-gray-100 w-60 lg:w-72" data-animation="fadeIn" data-duration="1000" data-delay="1200">
                        <div class="flex items-center space-x-3 lg:space-x-4">
                           <div class="w-12 h-12 lg:w-14 lg:h-14 bg-primary/10 rounded-full flex items-center justify-center">
                              <span class="text-xl lg:text-2xl font-bold text-primary">10+</span>
                           </div>
                           <div>
                              <p class="text-xs lg:text-sm text-gray-500 mb-1">Years of</p>
                              <p class="font-bold text-gray-900 text-sm lg:text-base">Excellence</p>
                              <div class="flex items-center mt-2">
                                 <div class="flex space-x-1">
                                    <div class="w-1.5 h-1.5 lg:w-2 lg:h-2 bg-primary rounded-full"></div>
                                    <div class="w-1.5 h-1.5 lg:w-2 lg:h-2 bg-primary/70 rounded-full"></div>
                                    <div class="w-1.5 h-1.5 lg:w-2 lg:h-2 bg-primary/40 rounded-full"></div>
                                 </div>
                                 <span class="text-xs text-gray-500 ml-2">Since 2014</span>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </x-container>
      </div>
   </div>



   <main>
      <x-section class="py-16">
         @include('frontend.pages.about.partials.mission')
      </x-section>
   </main>
</x-frontend::app-layout>