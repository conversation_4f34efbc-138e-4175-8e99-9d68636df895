<div class="header relative bg-gradient-to-b from-[#ECF7FF] to-white min-h-[50vh]">
    <div class="header-inner"></div>
    <x-frontend::navbar />
    <x-container class="mt-10">
        <div class="grid grid-cols-1 md:grid-cols-12 items-center gap-16">
            <div class="md:col-span-5">
                <img src="{{ asset('images/about_header_pic.png') }}" class="max-w-full md:max-w-[400px]" loading="lazy" alt="" data-animation="slideUp" data-duration="1000">
            </div>
            <div class="md:col-span-7">
                <h1 class="text-3xl font-bold header-title" data-animation="slideUp" data-duration="1000" data-delay="200">BIM</h1>
                <p class="mt-6 text-[#01365E]" data-animation="slideUp" data-duration="1000" data-delay="300">BIM, established in 2014 and based in Erbil, Iraq, specializes in construction, engineering, maintenance service, supply chain management and procurement. BIM delivers sustainable, high-quality solutions and maintenance services. In construction and engineering, BIM provides comprehensive solutions with a focus on innovation and precision.</p>
                <div class="grid grid-cols-2 gap-y-6 md:gap-y-4 md:gap-x-4 mt-6" data-animation-stagger="slideUp" data-stagger-target=".stat-item" data-stagger-duration="200" data-duration="1000" data-delay="400">
                    <div class="max-w-[250px] flex flex-col md:flex-row items-start gap-6 stat-item">
                        <span class="text-4xl text-green-600">125+</span>
                        <span class="text-zinc-700">Happy Private and Corporate Clients</span>
                    </div>
                    <div class="max-w-[250px] flex flex-col md:flex-row items-start gap-4 stat-item">
                        <span class="text-4xl text-green-600">87%</span>
                        <span class="text-zinc-700">Successfully Completed Projects</span>
                    </div>
                    <div class="max-w-[250px] flex flex-col md:flex-row items-start gap-4 stat-item">
                        <span class="text-4xl text-green-600">400+</span>
                        <span class="text-zinc-700">Partnership Agreements</span>
                    </div>
                    <div class="max-w-[250px] flex flex-col md:flex-row items-start gap-4 stat-item">
                        <span class="text-4xl text-green-600">50%</span>
                        <span class="text-zinc-700">Regular and permanent status</span>
                    </div>
                </div>
            </div>
        </div>
    </x-container>
</div>
@push('css')
<style>
    .header-inner {
        background-image: url("data:image/svg+xml;utf8,<svg width='33' height='33' viewBox='0 0 33 33' fill='none' xmlns='http://www.w3.org/2000/svg'><rect x='0.5' y='0.5' width='32' height='32' stroke='%23008CD3' stroke-opacity='0.08'/></svg>");
        background-repeat: repeat;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 0;
        opacity: 0.5;
        pointer-events: none;
        background-size: 28px 28px;
        background-position: center;
        background-attachment: fixed;
        width: 100%;
        height: 100%;
    }

    .header-title {
        font-family: 'Inter';
        font-style: normal;
        font-weight: 700;
        font-size: 60px;
        line-height: 64px;
        letter-spacing: -0.01em;

        background: linear-gradient(90deg, #008DD4 0%, #010D27 25.96%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;

        flex: none;
        order: 0;
        align-self: stretch;
        flex-grow: 0;

    }
</style>
@endpush