<x-container>
    <div class="relative pt-16 pb-0">
        <div class="relative z-10 max-w-6xl mx-auto">
            <div class="text-center mb-16" data-animation="slideUp" data-duration="800">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Our Team</h2>
                <div class="w-16 h-1 bg-primary mx-auto rounded-full"></div>
                <p class="text-lg text-gray-600 mt-6 max-w-2xl mx-auto">
                    Meet the dedicated professionals who drive our success and deliver exceptional results for our clients.
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                @foreach($teamMembers as $member)
                    <div class="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100" 
                         data-animation="slideUp" data-duration="800" data-delay="{{ $loop->index * 100 + 300 }}">
                        
                        <!-- Member Photo -->
                        <div class="relative h-64 bg-gradient-to-br from-primary/10 to-primary/5 flex items-center justify-center overflow-hidden">
                            @if($member->photoUrl())
                                <img src="{{ $member->photoUrl() }}" alt="{{ $member->name }}"
                                     class="w-full h-full object-cover">
                            @else
                                <div class="w-20 h-20 bg-primary/20 rounded-full flex items-center justify-center">
                                    <svg class="w-10 h-10 text-primary/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                </div>
                            @endif
                            <div class="absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>

                        <div class="p-6">
                            <div class="text-center mb-4">
                                <h4 class="text-xl font-semibold text-gray-900 mb-1">{{ $member->name }}</h4>
                                <p class="text-primary font-medium text-sm uppercase tracking-wide">{{ $member->role }}</p>
                                <p class="text-gray-500 text-xs uppercase tracking-wide mt-1">{{ $member->department }}</p>
                            </div>

                            @if($member->description)
                            <div class="mb-4">
                                <p class="text-gray-600 text-sm leading-relaxed line-clamp-3">{{ $member->description }}</p>
                            </div>
                            @endif

                            <div class="space-y-2">
                                @if($member->email)
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="w-4 h-4 mr-2 text-primary/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                                    </svg>
                                    <a href="mailto:{{ $member->email }}" class="hover:text-primary transition-colors">{{ $member->email }}</a>
                                </div>
                                @endif

                                @if($member->phone)
                                <div class="flex items-center text-sm text-gray-500">
                                    <svg class="w-4 h-4 mr-2 text-primary/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                    </svg>
                                    <a href="tel:{{ $member->phone }}" class="hover:text-primary transition-colors">{{ $member->phone }}</a>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
</x-container>

<style>
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
