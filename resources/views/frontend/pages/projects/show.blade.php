<x-frontend::app-layout>
    <div class="header relative bg-gradient-to-b from-[#ECF7FF] to-white min-h-[50vh]">
        <div class="header-inner"></div>
        <x-frontend::navbar />
        <x-container class="mt-10 lg:mt-16">
            <div class="flex items-center gap-6 col-span-12 md:col-span-6 lg:col-span-5">
                @if($project->icon())
                    <img src="{{ $project->icon() }}" alt="{{ $project->name }} icon" class="w-14 h-14 object-contain" data-animation="slideUp" data-duration="800" />
                @else
                    <x-section-icons.one data-animation="slideUp" data-duration="800" />
                @endif
                <div class="flex gap-5 justify-between items-center flex-1">
                    <h2 class="font-bold text-3xl text-brand-800" data-animation="slideUp" data-duration="1000" data-delay="200">{{ $project->name }}</h2>
                </div>
            </div>
            <div class="mt-10 lg:mt-12 pb-12 prose w-full max-w-7xl mx-auto">
                <img src="{{ $project->thumbnail() }}" alt="{{ $project->name }}" class="aspect-square object-cover rounded-lg" data-animation="slideUp" data-duration="1000" data-delay="400" />
                <h2 data-animation="slideUp" data-duration="1000" data-delay="500">About Project</h2>
                <div data-animation="slideUp" data-duration="1000" data-delay="600">
                    {!! $project->content !!}
                </div>
            </div>
            @if($project->getMedia('gallery')->count() > 0)
            <h2 class="mt-12 font-bold text-3xl text-brand-800" data-animation="slideUp" data-duration="1000" data-delay="700">Project Gallery</h2>
            <div class="mt-8 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4" data-animation-stagger="slideUp" data-stagger-target=".project-gallery-item" data-stagger-duration="200" data-duration="1000" data-delay="800">
                @foreach($project->getMedia('gallery') as $image)
                <div class="aspect-square w-full relative overflow-hidden rounded-lg border border-brand-200 project-gallery-item p-1">
                    <a href="{{ $image->getUrl() }}"
                        data-fancybox="project-gallery"
                        data-caption="{{ $project->name }}"
                        class="block w-full h-full group">

                        <img
                            src="{{ $image->getUrl() }}"
                            alt="{{ $project->name }}"
                            class="w-full h-full object-cover transition-all duration-500 ease-in-out group-hover:scale-110 rounded-lg" />

                        <div class="project-gallery-overlay absolute inset-0 bg-gradient-to-tr from-black/50 to-transparent opacity-0 transition-opacity duration-300 flex items-center justify-center">
                            <div class="translate-y-4 group-hover:translate-y-0 transition-transform duration-300 bg-white/90 rounded-full p-3">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="#02537D" stroke-width="1.5">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-5.197-5.197m0 0A7.5 7.5 0 105.196 5.196a7.5 7.5 0 0010.607 10.607z" />
                                </svg>
                            </div>

                            <div class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent text-white translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                                <p class="font-medium text-sm text-center">View Image</p>
                            </div>
                        </div>
                    </a>
                </div>
                @endforeach
            </div>
            @endif

            {{-- Project Maps Section --}}
            @if($projectMaps->count() > 0)
            <h2 class="mt-12 font-bold text-3xl text-brand-800" data-animation="slideUp" data-duration="1000" data-delay="900">{{$projectMaps?->first()?->name ?? 'Maps'}}</h2>
            <div class="mt-8 space-y-8" data-animation="slideUp" data-duration="1000" data-delay="1000">
                @foreach($projectMaps as $map)
                    <div class="project-map-section p-0 bg-gradient-to-b from-primary-400 rounded-xl to-transparent  overflow-hidden">
                        <div class="p-6">
                            @if($map->type === 'single')
                                {{-- Single Map with Multiple Markers --}}
                                <div class="project-map-container">
                                    <div class="map-loading" id="loading-{{ $map->id }}">
                                        <div class="map-loading-spinner"></div>
                                        Loading map...
                                    </div>
                                    <div id="project-map-{{ $map->id }}" class="project-map rounded-xl" style="display: none;"></div>
                                    <div id="error-map-{{ $map->id }}" class="hidden text-red-500 mt-2 p-2 bg-red-50 rounded-lg">
                                        <p class="font-medium">Map Error</p>
                                        <p class="text-sm">There was an issue loading the map. Please try refreshing the page.</p>
                                    </div>
                                    <div id="error-project-map-{{ $map->id }}" class="hidden text-red-500 mt-2 p-2 bg-red-50 rounded-lg"></div>
                                </div>
                            @elseif($map->type === 'multi_city' && $map->customizations->count() > 0)
                                {{-- Multi-City Map with Tabs --}}
                                <div class="multi-city-map-container">
                                    {{-- City Navigation Tabs --}}
                                    <div class="city-navigation mb-6">
                                        <div class="flex flex-wrap gap-2">
                                            @foreach($map->customizations as $index => $customization)
                                                @php
                                                    $city = $customization->projectCity;
                                                    $cityTitle = $customization->custom_title ?? $city->name;
                                                    $cityDescription = $customization->custom_description ?? $city->description;
                                                @endphp
                                                <button 
                                                    class="city-btn px-4 py-2 rounded-lg transition-colors duration-200 {{ $index === 0 ? 'bg-primary text-white' : 'bg-gray-200 text-gray-700 hover:bg-primary hover:text-white' }}" 
                                                    data-city-id="{{ $city->id }}"
                                                    data-map-id="{{ $map->id }}"
                                                    @if($cityDescription) title="{{ $cityDescription }}" @endif
                                                >
                                                    {{ $cityTitle }}
                                                </button>
                                            @endforeach
                                        </div>
                                    </div>

                                    {{-- City Info Panel --}}
                                    @foreach($map->customizations as $index => $customization)
                                        @php
                                            $city = $customization->projectCity;
                                            $cityTitle = $customization->custom_title ?? $city->name;
                                            $cityDescription = $customization->custom_description ?? $city->description;
                                        @endphp
                                        @if($cityDescription)
                                            <div 
                                                class="city-info-panel mb-4 p-4 bg-white border border-zinc-200 rounded-xl {{ $index === 0 ? '' : 'hidden' }}" 
                                                data-city-info="{{ $city->id }}"
                                                data-map-id="{{ $map->id }}"
                                            >
                                                <h4 class="font-semibold text-lg text-blue-900 mb-2">{{ $cityTitle }}</h4>
                                                <p class="text-blue-800 text-base">{{ $cityDescription }}</p>
                                            </div>
                                        @endif
                                    @endforeach

                                    {{-- Map Container --}}
                                    <div class="project-map-container">
                                        <div class="map-loading" id="loading-{{ $map->id }}">
                                            <div class="map-loading-spinner"></div>
                                            Loading map...
                                        </div>
                                        <div id="multi-city-map-{{ $map->id }}" class="project-map" style="display: none;"></div>
                                        <div id="error-map-{{ $map->id }}" class="hidden text-red-500 mt-2 p-2 bg-red-50 rounded-lg">
                                            <p class="font-medium">Map Error</p>
                                            <p class="text-sm">There was an issue loading the map. Please try refreshing the page.</p>
                                        </div>
                                        <div id="error-multi-city-map-{{ $map->id }}" class="hidden text-red-500 mt-2 p-2 bg-red-50 rounded-lg"></div>
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                @endforeach
            </div>
            @endif
        </x-container>
    </div>

    @push('scripts')
    @vite(['resources/assets/frontend/js/gallery.js'], 'assets/frontend')
    @if($projectMaps->count() > 0)
        @vite(['resources/assets/frontend/js/components/ProjectMap.js'], 'assets/frontend')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const projectMaps = @json($projectMaps->values());
                const currentLocale = @json(app_locale());
                
                // Make current locale available globally for ProjectMap
                window.currentLocale = currentLocale;
                
                const mapInstances = new Map();
                
                // Make sure Leaflet is available through the ProjectMap class
                const L = window.L || (window.ProjectMap && window.ProjectMap.L);
                
                projectMaps.forEach(function(mapData) {
                    const loadingElement = document.getElementById(`loading-${mapData.id}`);
                    
                    try {
                        if (mapData.type === 'single') {
                            const mapElement = document.getElementById(`project-map-${mapData.id}`);
                            if (!mapElement) return;
                            
                            // Initialize single map
                            const mapInstance = new ProjectMap(`project-map-${mapData.id}`, {
                                zoom: mapData.zoom_level || 7, // Default to 7
                                center: mapData.center_coordinates || [36.1915, 44.0092]
                            });
                            mapInstance.init();
                            
                            // Add markers
                            if (mapData.markers && mapData.markers.length > 0) {
                                
                                // Map the markers to a standard format and log for debugging
                                const standardizedMarkers = mapData.markers.map(marker => {
                                    // Process coordinates if needed
                                    let coords = marker.coordinates;
                                    if (typeof coords === 'string') {
                                        try {
                                            coords = JSON.parse(coords);
                                        } catch (e) {
                                            console.warn('Failed to parse coordinates string:', coords);
                                        }
                                    }
                                    
                                    return {
                                        coordinates: coords,
                                        title: marker.title,
                                        description: marker.description,
                                        address: marker.address,
                                        icon_type: marker.icon_type || 'default' // Include icon type
                                    };
                                });
                                
                                // Pass to the addMarkers method which now has robust validation
                                mapInstance.addMarkers(standardizedMarkers);
                            }
                            
                            // Hide loading and show map
                            loadingElement.style.display = 'none';
                            mapElement.style.display = 'block';
                            
                            // Resize map after showing
                            setTimeout(() => mapInstance.resize(), 100);
                            
                            mapInstances.set(mapData.id, mapInstance);
                            
                        } else if (mapData.type === 'multi_city') {
                            const mapElement = document.getElementById(`multi-city-map-${mapData.id}`);
                            if (!mapElement) return;
                            
                            // Initialize simple map for multi-city
                            const mapInstance = new ProjectMap(`multi-city-map-${mapData.id}`, {
                                zoom: mapData.zoom_level || 7,
                                center: mapData.center_coordinates || [36.1915, 44.0092]
                            });
                            
                            mapInstance.init();
                            mapInstances.set(mapData.id, mapInstance);
                            
                            // Set up city switching
                            const cityButtons = document.querySelectorAll(`.city-btn[data-map-id="${mapData.id}"]`);
                            
                            // Show first city by default
                            if (mapData.customizations && mapData.customizations.length > 0) {
                                // Show first city info panel
                                const firstCustomization = mapData.customizations[0];
                                const firstCity = firstCustomization.project_city;
                                
                                if (firstCity) {
                                    const firstCityPanel = document.querySelector(`[data-city-info="${firstCity.id}"][data-map-id="${mapData.id}"]`);
                                    if (firstCityPanel) {
                                        firstCityPanel.classList.remove('hidden');
                                    }
                                    
                                    showCity(mapInstance, firstCustomization, mapData.id);
                                }
                            }
                            
                            // Add click handlers for city buttons
                            cityButtons.forEach(button => {
                                button.addEventListener('click', function() {
                                    const cityId = parseInt(this.dataset.cityId);
                                    // Find the customization with this city ID
                                    const customization = mapData.customizations.find(c => 
                                        (c.project_city && c.project_city.id === cityId) || 
                                        c.project_city_id === cityId
                                    );
                                    
                                    if (customization) {
                                        // Update active button
                                        cityButtons.forEach(btn => {
                                            btn.classList.remove('bg-primary', 'text-white');
                                            btn.classList.add('bg-gray-200', 'text-gray-700');
                                        });
                                        this.classList.remove('bg-gray-200', 'text-gray-700');
                                        this.classList.add('bg-primary', 'text-white');
                                        
                                        // Show city info panel
                                        const infoPanels = document.querySelectorAll(`[data-city-info][data-map-id="${mapData.id}"]`);
                                        infoPanels.forEach(panel => panel.classList.add('hidden'));
                                        
                                        const activePanel = document.querySelector(`[data-city-info="${cityId}"][data-map-id="${mapData.id}"]`);
                                        if (activePanel) {
                                            activePanel.classList.remove('hidden');
                                        }
                                        
                                        // Show city on map
                                        showCity(mapInstance, customization, mapData.id);
                                    }
                                });
                            });
                            
                            // Hide loading and show map
                            loadingElement.style.display = 'none';
                            mapElement.style.display = 'block';
                            
                            // Resize map after showing
                            setTimeout(() => mapInstance.resize(), 100);
                        }
                        
                    } catch (error) {
                        console.error('Error initializing map:', error);
                        loadingElement.innerHTML = '<div class="text-red-500">Error loading map. Please try refreshing the page.</div>';
                    }
                });
                
                function showCity(mapInstance, customization, mapId) {
                    try {
                        // Clear existing markers
                        // Use proper method to clear markers - first check if the map object exists
                        if (mapInstance.map) {
                            // Remove all existing markers by clearing the map
                            mapInstance.map.eachLayer((layer) => {
                                if (layer instanceof L.Marker) {
                                    mapInstance.map.removeLayer(layer);
                                }
                            });
                            
                            // Clear existing bounds overlay if it exists
                            if (mapInstance.boundsOverlay) {
                                mapInstance.map.removeLayer(mapInstance.boundsOverlay);
                                mapInstance.boundsOverlay = null;
                            }
                        }
                        
                        // Get the actual city data from the customization
                        const projectCity = customization.project_city;
                        
                        if (!projectCity) {
                            console.error('City data not found in customization');
                            return;
                        }
                        
                        // Set city view
                        // Set map center and zoom level first
                        if (projectCity.center_coordinates) {
                            // Check if setCenter method exists
                            if (typeof mapInstance.setCenter === 'function') {
                                // Use project city zoom level or default to 6 (not 10)
                                const zoomLevel = projectCity.zoom_level || 7;
                                mapInstance.setCenter(projectCity.center_coordinates[0], projectCity.center_coordinates[1], zoomLevel);
                            } else {
                                // Fallback implementation
                                try {
                                    const lat = Array.isArray(projectCity.center_coordinates) ? 
                                        projectCity.center_coordinates[0] : 
                                        projectCity.center_coordinates.lat;
                                    
                                    const lng = Array.isArray(projectCity.center_coordinates) ? 
                                        projectCity.center_coordinates[1] : 
                                        projectCity.center_coordinates.lng;
                                    
                                    // Use project city zoom level or default to 7 (not 10)
                                    const zoom = projectCity.zoom_level || 7;
                                    
                                    // Set center and zoom
                                    mapInstance.map.setView([lat, lng], zoom);
                                } catch (error) {
                                    console.error('Error setting center:', error);
                                }
                            }
                        }
                        
                        // Add city bounds if available (preserve the zoom level set above)
                        if (projectCity.bounds) {
                            // Check if setBounds method exists
                            if (typeof mapInstance.setBounds === 'function') {
                                mapInstance.setBounds(projectCity.bounds, { fitBounds: false });
                            } else {
                                // Fallback implementation for setBounds
                                try {
                                    let bounds;
                                    
                                    // Handle different bounds formats
                                    if (typeof projectCity.bounds === 'object' && projectCity.bounds !== null) {
                                        if (Array.isArray(projectCity.bounds)) {
                                            // If it's an array of coordinates for a polygon
                                            if (projectCity.bounds.length > 0 && typeof projectCity.bounds[0] === 'object') {
                                                const coordinates = projectCity.bounds.map(point => [
                                                    point.lat || point[0],
                                                    point.lng || point[1]
                                                ]);
                                                const polygon = L.polygon(coordinates, {
                                                    color: '#6366F1',
                                                    weight: 2,
                                                    fillOpacity: 0.1,
                                                    dashArray: '5, 5'
                                                }).addTo(mapInstance.map);
                                                
                                                // Store the overlay for later removal
                                                mapInstance.boundsOverlay = polygon;
                                            } 
                                            // If it's a simple [south, west, north, east] array
                                            else if (projectCity.bounds.length >= 4 && typeof projectCity.bounds[0] === 'number') {
                                                const rectangle = L.rectangle([
                                                    [projectCity.bounds[0], projectCity.bounds[1]],
                                                    [projectCity.bounds[2], projectCity.bounds[3]]
                                                ], {
                                                    color: '#6366F1',
                                                    weight: 2,
                                                    fillOpacity: 0.1,
                                                    dashArray: '5, 5'
                                                }).addTo(mapInstance.map);
                                                
                                                // Store the overlay for later removal
                                                mapInstance.boundsOverlay = rectangle;
                                            }
                                        } 
                                        // If it's an object with south, north, east, west properties
                                        else if (projectCity.bounds.south !== undefined && 
                                                projectCity.bounds.north !== undefined && 
                                                projectCity.bounds.west !== undefined && 
                                                projectCity.bounds.east !== undefined) {
                                            const rectangle = L.rectangle([
                                                [projectCity.bounds.south, projectCity.bounds.west],
                                                [projectCity.bounds.north, projectCity.bounds.east]
                                            ], {
                                                color: '#6366F1',
                                                weight: 2,
                                                fillOpacity: 0.1,
                                                dashArray: '5, 5'
                                            }).addTo(mapInstance.map);
                                            
                                            // Store the overlay for later removal
                                            mapInstance.boundsOverlay = rectangle;
                                        }
                                    }
                                } catch (error) {
                                    console.error('Error setting bounds:', error);
                                }
                            }
                        }
                        
                        // Add city markers from the customization
                        if (customization.markers && customization.markers.length > 0) {

                            // Check which marker adding method is available
                            if (typeof mapInstance.addMarkers === 'function') {
                                // Use the addMarkers method if available - let the ProjectMap.js handle validation
                                mapInstance.addMarkers(customization.markers);
                            } else if (typeof mapInstance.addMarker === 'function') {
                                // Use individual addMarker calls if that's what's available
                                customization.markers.forEach((marker, index) => {
                                    // Skip processing if coordinates are missing
                                    if (!marker.coordinates) {
                                        console.warn(`Marker ${index} is missing coordinates`);
                                        return;
                                    }
                                    
                                    let lat, lng;
                                    
                                    if (Array.isArray(marker.coordinates)) {
                                        lat = marker.coordinates[0];
                                        lng = marker.coordinates[1];
                                    } else if (typeof marker.coordinates === 'object') {
                                        lat = marker.coordinates.lat || marker.coordinates.latitude;
                                        lng = marker.coordinates.lng || marker.coordinates.longitude;
                                    } else if (typeof marker.coordinates === 'string') {
                                        try {
                                            const coords = JSON.parse(marker.coordinates);
                                            if (Array.isArray(coords)) {
                                                [lat, lng] = coords;
                                            } else {
                                                lat = coords.lat || coords.latitude;
                                                lng = coords.lng || coords.longitude;
                                            }
                                        } catch (e) {
                                            console.warn(`Failed to parse coordinates string for marker ${index}:`, marker.coordinates);
                                            return;
                                        }
                                    }
                                    
                                    if (lat !== undefined && lng !== undefined) {
                                        mapInstance.addMarker(
                                            lat,
                                            lng,
                                            {
                                                iconType: marker.icon_type || 'default', // Include icon type
                                                popup: {
                                                    title: marker.title,
                                                    description: marker.description,
                                                    address: marker.address
                                                },
                                                // Add properties to prevent marker blinking
                                                riseOnHover: false,
                                                riseOffset: 0,
                                                bubblingMouseEvents: false
                                            }
                                        );
                                    }
                                });
                            } else {
                                // Direct Leaflet fallback if no ProjectMap methods are available
                                customization.markers.forEach((marker, index) => {
                                    // Skip processing if coordinates are missing
                                    if (!marker.coordinates) {
                                        console.warn(`Marker ${index} is missing coordinates`);
                                        return;
                                    }
                                    
                                    let lat, lng;
                                    
                                    if (Array.isArray(marker.coordinates)) {
                                        lat = parseFloat(marker.coordinates[0]);
                                        lng = parseFloat(marker.coordinates[1]);
                                    } else if (typeof marker.coordinates === 'object') {
                                        lat = parseFloat(marker.coordinates.lat || marker.coordinates.latitude);
                                        lng = parseFloat(marker.coordinates.lng || marker.coordinates.longitude);
                                    } else if (typeof marker.coordinates === 'string') {
                                        try {
                                            const coords = JSON.parse(marker.coordinates);
                                            if (Array.isArray(coords)) {
                                                lat = parseFloat(coords[0]);
                                                lng = parseFloat(coords[1]);
                                            } else {
                                                lat = parseFloat(coords.lat || coords.latitude);
                                                lng = parseFloat(coords.lng || coords.longitude);
                                            }
                                        } catch (e) {
                                            console.warn(`Failed to parse coordinates string for marker ${index}:`, marker.coordinates);
                                            return;
                                        }
                                    }
                                    
                                    if (!isNaN(lat) && !isNaN(lng) && lat >= -90 && lat <= 90 && lng >= -180 && lng <= 180) {
                                        try {
                                            const leafletMarker = L.marker([lat, lng]).addTo(mapInstance.map);
                                            
                                            // Add popup if there's content
                                            if (marker.title || marker.description) {
                                                const popupContent = `
                                                    <div class="marker-popup">
                                                        ${marker.title ? `<h4>${marker.title}</h4>` : ''}
                                                        ${marker.description ? `<p>${marker.description}</p>` : ''}
                                                        ${marker.address ? `<p class="address">${marker.address}</p>` : ''}
                                                    </div>
                                                `;
                                                leafletMarker.bindPopup(popupContent);
                                            }
                                        } catch (error) {
                                            console.error(`Error creating marker ${index}:`, error);
                                        }
                                    } else {
                                        console.warn(`Invalid coordinates for marker ${index}:`, lat, lng);
                                    }
                                });
                            }
                        }
                    } catch (error) {
                        console.error('Error showing city:', error);
                        
                        // Display an error message to the user
                        const mapId = customization.project_city ? customization.project_city.id : 'unknown';
                        const errorElementId = `error-${mapId}`;
                        let errorElement = document.getElementById(errorElementId);
                        
                        // Create error element if it doesn't exist
                        if (!errorElement) {
                            errorElement = document.createElement('div');
                            errorElement.id = errorElementId;
                            errorElement.className = 'text-red-500 mt-2 p-2 bg-red-50 rounded-lg hidden';
                            
                            // Find the map container and insert the error element after it
                            const mapContainer = document.querySelector(`[data-map-id="${mapId}"]`).closest('.project-map-container');
                            if (mapContainer) {
                                mapContainer.after(errorElement);
                            }
                        }
                        
                        errorElement.textContent = 'There was an error displaying this map. Please try refreshing the page.';
                        errorElement.classList.remove('hidden');
                        
                        // Try to recover map functionality
                        try {
                            if (mapInstance && mapInstance.map) {

                                mapInstance.map.setView([36.1915, 44.0092], 7);
                            }
                        } catch (recoveryError) {
                            console.error('Failed to recover map after error:', recoveryError);
                        }
                        
                        // Log detailed debug info
                        console.debug('Map state:', {
                            mapInstance: mapInstance,
                            customization: customization,
                            projectCity: customization.project_city
                        });
                    }
                }
            });
        </script>
    @endif
    @endpush

    @push('css')
    @if($projectMaps->count() > 0)
        @vite(['resources/assets/frontend/css/components/project-map.css'], 'assets/frontend')
    @endif
    <style>
        /* Fix for blinking markers during zoom */
        .leaflet-marker-icon {
            will-change: transform;
            transform: translate3d(0, 0, 0);
            backface-visibility: hidden;
            perspective: 1000px;
            transition: none !important;
            animation: none !important;
        }
        
        /* Fix for marker shadow blinking */
        .leaflet-marker-shadow {
            will-change: transform;
            transform: translate3d(0, 0, 0);
            backface-visibility: hidden;
            transition: none !important;
            animation: none !important;
        }
        
        .header-inner {
            background-image: url("data:image/svg+xml;utf8,<svg width='33' height='33' viewBox='0 0 33 33' fill='none' xmlns='http://www.w3.org/2000/svg'><rect x='0.5' y='0.5' width='32' height='32' stroke='%23008CD3' stroke-opacity='0.08'/></svg>");
            background-repeat: repeat;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 0;
            opacity: 0.5;
            pointer-events: none;
            background-size: 28px 28px;
            background-position: center;
            background-attachment: fixed;
            width: 100%;
            height: 880px;
        }

        /* Project Gallery Styles */
        .project-gallery-item {
            transition: all 0.3s ease;
        }

        .project-gallery-item:hover .project-gallery-overlay {
            opacity: 0.7;
        }

        .header-title {
            font-family: 'Inter';
            font-style: normal;
            font-weight: 700;
            font-size: 60px;
            line-height: 64px;
            letter-spacing: -0.01em;

            background: linear-gradient(90deg, #008DD4 0%, #010D27 25.96%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;

            flex: none;
            order: 0;
            align-self: stretch;
            flex-grow: 0;

        }
    </style>
    @endpush
</x-frontend::app-layout>