<x-frontend::app-layout>
    <div class="header relative bg-gradient-to-b from-[#ECF7FF] to-white min-h-[50vh]">
        <div class="header-inner"></div>
        <x-frontend::navbar />
        <x-container class="mt-10 lg:mt-16">
            <div class="flex items-center gap-6 col-span-12 md:col-span-6 lg:col-span-5">
                <x-section-icons.two data-animation="slideUp" data-duration="800" />
                <div class="flex gap-5 justify-between items-center flex-1">
                    <h2 class="font-bold text-3xl text-brand-800" data-animation="slideUp" data-duration="1000" data-delay="200">{{ __('our_projects') }}</h2>
                </div>
            </div>
            <div class="mt-10 lg:mt-12 flex flex-col gap-10 lg:gap-12 pb-12">
                @foreach ($projects as $project)
                <div class="relative" data-animation="slideUp" data-duration="1000" data-delay="{{ $loop->iteration * 200 }}">
                    <div class="bg-brand-100 w-full h-full rounded-lg absolute right-0 md:-left-1 -bottom-1 z-0"></div>
                    <div class="flex flex-col md:flex-row items-center bg-white rounded-lg shadow-sm overflow-hidden relative border border-brand-100">
                        <div class="order-2 md:order-1 flex-1 p-6 lg:p-10 relative z-20">
                            <h3 class="text-2xl lg:text-3xl font-bold text-brand-900">
                                {{ $project->name }}
                            </h3>
                            <p class="text-sm text-zinc-950 mt-5 relative z-20">
                                {{ $project->limitedExcerpt(200) }}
                            </p>
                            <a href="{{ $project->path() }}" class="mt-8 lg:mt-10 text-sm font-medium px-6 py-1.5 bg-primary text-white border border-primary rounded-md focus:outline-none hover:bg-white hover:text-primary inline-flex items-center transition-colors duration-300">
                                <span>{{ __('more_details') }}</span>
                                <x-icons.arrow-right class=" w-4 h-4 ml-2" />
                            </a>
                        </div>
                        <div class="order-1 md:order-2 w-full md:max-w-[400px] h-[300px] [&>img]:w-full [&>img]:h-full [&>img]:object-cover relative z-20">
                            {!! $project->responsiveThumbnail() !!}
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
        </x-container>
    </div>
    @push('css')
    <style>
        .header-inner {
            background-image: url("data:image/svg+xml;utf8,<svg width='33' height='33' viewBox='0 0 33 33' fill='none' xmlns='http://www.w3.org/2000/svg'><rect x='0.5' y='0.5' width='32' height='32' stroke='%23008CD3' stroke-opacity='0.08'/></svg>");
            background-repeat: repeat;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 0;
            opacity: 0.5;
            pointer-events: none;
            background-size: 28px 28px;
            background-position: center;
            background-attachment: fixed;
            width: 100%;
            height: 880px;
        }

        .header-title {
            font-family: 'Inter';
            font-style: normal;
            font-weight: 700;
            font-size: 60px;
            line-height: 64px;
            letter-spacing: -0.01em;

            background: linear-gradient(90deg, #008DD4 0%, #010D27 25.96%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;

            flex: none;
            order: 0;
            align-self: stretch;
            flex-grow: 0;

        }
    </style>
    @endpush
</x-frontend::app-layout>