<x-frontend::app-layout>
    <div class="header relative bg-gradient-to-b from-[#ECF7FF] to-white">
        <div class="header-inner"></div>
        <x-frontend::navbar />
        <x-container class="mt-10 lg:mt-16">
            <div class="flex items-center py-4 overflow-x-auto whitespace-nowrap">
                <a href="/" class="text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                    </svg>
                </a>

                <span class="mx-5 text-gray-500 rtl:-scale-x-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                </span>

                <a href="{{ route('news') }}" class="text-gray-500 hover:underline">
                    News
                </a>
                <span class="mx-5 text-gray-500 rtl:-scale-x-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                </span>

                <a href="#" class="text-gray-500 hover:underline">
                    {{ $publication->title }}
                </a>
            </div>
        </x-container>
    </div>
    <x-container class="mt-10 pb-16 lg:pb-24">
        <article class="relative break-words prose-a:text-blue-500 prose prose-lg prose-h1:text-lg prose-h1:lg:text-4xl prose-stone mx-auto max-w-[787px]">
            <h1 class="mt-0" data-animation="slideUp" data-duration="1000">{{ $publication->title }}</h1>
            <p class="text-sm" data-animation="slideUp" data-duration="1000" data-delay="100">Published at: {{ $publication->created_at->format('M, d Y') }}</p>
            <img src="{{$publication->thumbnail()}}" class="w-full" alt="" data-animation="slideUp" data-duration="1000" data-delay="200">
            <div class="flex items-start" data-animation="slideUp" data-duration="1000" data-delay="300">
                <div class="w-full lg:-mt-[24px]">
                    {!! remove_style_attribute($publication->content) !!}
                </div>
            </div>
        </article>
    </x-container>
    @push('css')
    <style>
        .header-inner {
            background-image: url("data:image/svg+xml;utf8,<svg width='33' height='33' viewBox='0 0 33 33' fill='none' xmlns='http://www.w3.org/2000/svg'><rect x='0.5' y='0.5' width='32' height='32' stroke='%23008CD3' stroke-opacity='0.08'/></svg>");
            background-repeat: repeat;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 0;
            opacity: 0.5;
            pointer-events: none;
            background-size: 28px 28px;
            background-position: center;
            background-attachment: fixed;
            width: 100%;
            height: 230px;
        }

        .header-title {
            font-family: 'Inter';
            font-style: normal;
            font-weight: 700;
            font-size: 60px;
            line-height: 64px;
            letter-spacing: -0.01em;

            background: linear-gradient(90deg, #008DD4 0%, #010D27 25.96%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;

            flex: none;
            order: 0;
            align-self: stretch;
            flex-grow: 0;

        }
    </style>
    @endpush
</x-frontend::app-layout>