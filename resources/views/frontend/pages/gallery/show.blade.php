<x-frontend::page-layout fullScreen>
    @include('frontend.pages.partials._page-header')
    <x-container class="py-10 lg:py-16">
        <div class="relative gallery mt-12" data-animation-stagger="slideUp" data-stagger-target="figure" data-stagger-duration="100" data-duration="500" id="gallery">
            @foreach($galleries as $gallery)
            <figure data-src="{{ asset($gallery->photo()) }}" data-caption="{{ $gallery->caption }}" data-fancybox="gallery" class="relative border-2 p-2 border-primary cursor-pointer group overflow-hidden block">
                <img src="{{ asset($gallery->thumb()) }}" loading="lazy" alt="" class="h-auto max-w-full object-cover transition-transform duration-500 group-hover:scale-110">
                <div class="bg-primary absolute inset-0 w-0 h-full opacity-0 group-hover:opacity-30 group-hover:w-full transition-all duration-500"></div>
            </figure>
            @endforeach
        </div>
    </x-container>
    @push('scripts')
    @vite(['resources/assets/frontend/js/gallery.js'], 'assets/frontend')
    @endpush
</x-frontend::page-layout>