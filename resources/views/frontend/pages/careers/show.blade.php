<x-frontend::app-layout>
    <div class="header relative bg-gradient-to-b from-[#ECF7FF] to-white min-h-[50vh]">
        <div class="header-inner"></div>
        <x-frontend::navbar />
        <x-container class="mt-10 lg:mt-16">
            <div class="flex items-center py-4 overflow-x-auto whitespace-nowrap">
                <a href="/" class="text-zinc-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                    </svg>
                </a>

                <span class="mx-5 text-zinc-700 rtl:-scale-x-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                </span>

                <a href="{{ app_route('careers.index') }}" class="text-zinc-700 hover:underline">
                    {{ __('careers') }}
                </a>
                <span class="mx-5 text-zinc-700 rtl:-scale-x-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                </span>

                <a href="#" class="text-zinc-700 hover:underline">
                    {{ $job->title }}
                </a>
            </div>
            <div class="mt-4 lg:mt-6 flex items-center gap-6 col-span-12 md:col-span-6 lg:col-span-5">
                <svg class="text-brand-700" width="32" height="32" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 14.15v4.25c0 1.094-.787 2.036-1.872 2.18-2.087.277-4.216.42-6.378.42s-4.291-.143-6.378-.42c-1.085-.144-1.872-1.086-1.872-2.18v-4.25m16.5 0a2.18 2.18 0 0 0 .75-1.661V8.706c0-1.081-.768-2.015-1.837-2.175a48.114 48.114 0 0 0-3.413-.387m4.5 8.006c-.194.165-.42.295-.673.38A23.978 23.978 0 0 1 12 15.75c-2.648 0-5.195-.429-7.577-1.22a2.016 2.016 0 0 1-.673-.38m0 0A2.18 2.18 0 0 1 3 12.489V8.706c0-1.081.768-2.015 1.837-2.175a48.111 48.111 0 0 1 3.413-.387m7.5 0V5.25A2.25 2.25 0 0 0 13.5 3h-3a2.25 2.25 0 0 0-2.25 2.25v.894m7.5 0a48.667 48.667 0 0 0-7.5 0M12 12.75h.008v.008H12v-.008Z" />
                </svg>

                <div class="flex gap-5 justify-between items-center flex-1">
                    <h1 class="font-bold text-3xl text-brand-800">{{ $job->title }}</h1>
                </div>
            </div>
        </x-container>
        <x-container class="bg-white relative rounded-lg mt-12 lg:mt-16 shadow-lg mb-12">
            <div class="p-6 lg:p-10">
                
                <div class="lg:flex lg:items-start mt-6 lg:mt-8">
                    <div class="prose max-w-4xl lg:w-8/12 lg:pr-12">
                        <h2>{{ __('job_description') }}</h2>
                        {!! $job->description !!}
                    </div>
                    <aside class="p-4 lg:w-4/12">
                        <div class="mt-2 text-sm text-zinc-500 p-6 bg-zinc-50 -mr-4">
                            <span class="block text-xs uppercase">{{ trans('location') }}</span>
                            <span class="font-semibold mt-1 block text-zinc-900">{{ $job->location }}</span>
                            <br />
                            <span class="block text-xs uppercase">{{ trans('status') }}</span>
                            <span class="font-semibold mt-1 block text-zinc-900">{{ trans($job->closed ? 'closed' : 'open') }}</span>
                        </div>
                    </aside>
                </div>
                @if (!$job->closed)
                <div class="mt-8 border-t pt-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ __('apply_now') }}</h3>

                    @if (session('success'))
                        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
                            {{ session('success') }}
                        </div>
                    @endif

                    @if ($errors->any())
                        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
                            <ul class="list-disc list-inside">
                                @foreach ($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ app_route('careers.apply', $job->slug) }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                        @csrf
                        <x-honeypot />
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label for="full_name" class="block text-sm font-medium text-gray-700 mb-2">{{ __('full_name') }} <span class="text-red-500">*</span></label>
                                <input type="text" id="full_name" name="full_name" value="{{ old('full_name') }}" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-brand-500 focus:border-brand-500">
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">{{ __('email') }} <span class="text-red-500">*</span></label>
                                <input type="email" id="email" name="email" value="{{ old('email') }}" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-brand-500 focus:border-brand-500">
                            </div>
                        </div>

                        <div>
                            <label for="summary" class="block text-sm font-medium text-gray-700 mb-2">{{ __('job_summary') }}</label>
                            <textarea id="summary" name="summary" rows="4" placeholder="Tell us briefly about yourself and why you're interested in this position..."
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-brand-500 focus:border-brand-500">{{ old('summary') }}</textarea>
                            <p class="mt-1 text-sm text-gray-500">{{ trans('max_chars') }}</p>
                        </div>

                        <div>
                            <label for="cv" class="block text-sm font-medium text-gray-700 mb-2">{{ __('upload_cv') }} <span class="text-red-500">*</span></label>
                            <input type="file" id="cv" name="cv" accept=".pdf,.doc,.docx" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-brand-500 focus:border-brand-500">
                            <p class="mt-1 text-sm text-gray-500">{{ trans('upload_cv_hint') }}</p>
                        </div>

                        <div class="flex items-center justify-between">
                            <button type="submit"
                                    class="inline-flex rounded-lg items-center gap-4 px-6 py-2.5 bg-brand-600 border border-transparent hover:-translate-y-1 text-base text-white tracking-widest hover:bg-brand-700 active:bg-brand-900 focus:outline-none focus:border-brand-900 focus:ring ring-brand-300 disabled:opacity-25 transition ease-in-out duration-300">
                                <span>{{ __('submit_application') }}</span>
                                <x-icons.arrow-right />
                            </button>
                        </div>
                    </form>
                </div>
                @else
                <div class="mt-4">
                    <x-button disabled>{{ trans('application_closed') }}</x-button>
                </div>
                @endif
            </div>
        </x-container>

        @push('css')
        <style>
            .header-inner {
                background-image: url("data:image/svg+xml;utf8,<svg width='33' height='33' viewBox='0 0 33 33' fill='none' xmlns='http://www.w3.org/2000/svg'><rect x='0.5' y='0.5' width='32' height='32' stroke='%23008CD3' stroke-opacity='0.08'/></svg>");
                background-repeat: repeat;
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                z-index: 0;
                opacity: 0.5;
                pointer-events: none;
                background-size: 28px 28px;
                background-position: center;
                background-attachment: fixed;
                width: 100%;
                height: 880px;
            }

            .header-title {
                font-family: 'Inter';
                font-style: normal;
                font-weight: 700;
                font-size: 60px;
                line-height: 64px;
                letter-spacing: -0.01em;

                background: linear-gradient(90deg, #008DD4 0%, #010D27 25.96%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;

                flex: none;
                order: 0;
                align-self: stretch;
                flex-grow: 0;

            }
        </style>
        @endpush
</x-frontend::app-layout>