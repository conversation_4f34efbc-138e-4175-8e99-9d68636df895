<x-frontend::app-layout>
    <div class="header relative bg-gradient-to-b from-[#ECF7FF] to-white min-h-[50vh]">
        <div class="header-inner"></div>
        <x-frontend::navbar />
        <x-container class="mt-10 lg:mt-16">
            <div class="flex items-center gap-6 col-span-12 md:col-span-6 lg:col-span-5">
                <x-section-icons.two data-animation="slideUp" data-duration="800" />
                <div class="flex gap-5 justify-between items-center flex-1">
                    <h2 class="font-bold text-3xl text-brand-800" data-animation="slideUp" data-duration="1000" data-delay="200">{{ __('join_our_team') }}</h2>
                </div>
            </div>
            <p data-animation="slideUp" data-duration="1000" data-delay="200" class="text-base text-zinc-600 mt-5">
                At BIM, we’re building the future of Iraq’s infrastructure and services. As part of our team, you’ll have the opportunity to grow your skills across diverse fields such as engineering, construction, maintenance, and supply chain — all while working alongside experienced professionals.
            </p>
        </x-container>
        <x-container class="mt-14 lg:mt-16">
            @if(count($availableJobs) > 0)
            <h2 class="text-2xl font-bold text-brand mb-6" data-animation="slideUp" data-duration="1000" data-delay="400">{{ __('available_jobs') }}</h2>
            @endif
            <div class="mt-3 space-y-1" data-animation-stagger="slideUp" data-stagger-target=".job-vacancy" data-stagger-duration="200" data-duration="1000" data-delay="500">

                @forelse ($availableJobs as $job)
                <x-job-vacancy :job="$job" />
                @empty
                <p class="mt-3">{{ __('no_jobs_available') }}</p>
                @endforelse
            </div>
        </x-container>
    </div>
    @push('css')
    <style>
        .header-inner {
            background-image: url("data:image/svg+xml;utf8,<svg width='33' height='33' viewBox='0 0 33 33' fill='none' xmlns='http://www.w3.org/2000/svg'><rect x='0.5' y='0.5' width='32' height='32' stroke='%23008CD3' stroke-opacity='0.08'/></svg>");
            background-repeat: repeat;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 0;
            opacity: 0.5;
            pointer-events: none;
            background-size: 28px 28px;
            background-position: center;
            background-attachment: fixed;
            width: 100%;
            height: 880px;
        }

        .header-title {
            font-family: 'Inter';
            font-style: normal;
            font-weight: 700;
            font-size: 60px;
            line-height: 64px;
            letter-spacing: -0.01em;

            background: linear-gradient(90deg, #008DD4 0%, #010D27 25.96%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;

            flex: none;
            order: 0;
            align-self: stretch;
            flex-grow: 0;

        }
    </style>
    @endpush
</x-frontend::app-layout>