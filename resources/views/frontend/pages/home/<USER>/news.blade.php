<x-container>
    <div class="flex items-start gap-6 col-span-12 md:col-span-6 lg:col-span-5">
        <x-section-icons.two />
        <div class="flex gap-5 justify-between items-center flex-1">
            <h2 class="font-bold text-3xl text-brand-800">{{ trans('latest_updates') }}</h2>

            <a href="{{ app_route('news') }}" class="inline-flex items-center justify-center px-6 py-1.5 border border-brand-600 text-white bg-brand font-medium rounded-md transition duration-300">
                {{ trans('view_all_articles') }}
                <x-icons.arrow-right class="w-4 h-4 ms-2" />
            </a>
        </div>
    </div>

    <div class="mt-12">
        <!-- Grid -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach ($news as $n)
            <x-news-card data-animation="slideUp" data-duration="1000" data-delay="{{ $loop->iteration * 100 }}" :news="$n" />
            @endforeach
        </div>
    </div>
</x-container>