<x-container>
    <div class="flex items-start gap-6 col-span-12 md:col-span-6 lg:col-span-5" data-animation="slideUp" data-duration="1000">
        <x-section-icons.two data-animation="fade" data-duration="1000" data-delay="100" />
        <div class="flex gap-5 items-center">
            <h2 class="font-bold text-3xl text-brand-800" data-animation="slideUp" data-duration="1000" data-delay="200">Our Projects</h2>
            <p class="text-zinc-700" data-animation="slideUp" data-duration="1000" data-delay="300">Delivering smart, reliable, and sustainable services across every sector.</p>
        </div>

    </div>
    <div class="col-span-12 grid-cols-12 relative mt-14">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 relative" data-animation-stagger="slideUp" data-stagger-target=".project" data-stagger-duration="200" data-duration="800" data-delay="400">
            @foreach ($projects as $project)
            <a href="{{ $project->path() }}" class="relative group project overflow-hidden flex flex-col justify-start p-8 rounded-lg bg-brand-900 min-h-[300px]">
                <!-- Background image -->
                <div class="absolute inset-0 rounded-lg overflow-hidden">
                    <div class="w-full h-full opacity-30">
                        {!! str_replace('<img', '<img class="w-full h-full object-cover"', $project->responsiveThumbnail()) !!}
                    </div>
                </div>
                
                <!-- Dark overlay for better text readability -->
                <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-black/20 rounded-lg"></div>
                
                <!-- Content -->
                <div class="relative z-10">
                    <h3 class="text-white font-medium text-lg mb-3 min-h-[3.5rem]">{{ $project->name }}</h3>
                    <p class="text-zinc-100 text-sm">{{ $project->limitedExcerpt(100) }}</p>
                </div>
                
                <!-- Hover effect -->
                <div class="pointer-events-none rounded-lg absolute inset-0 bg-gradient-to-tr from-cyan-500/40 to-blue-700/20 -translate-y-full translate-x-full group-hover:translate-x-0 group-hover:translate-y-0 transition-transform duration-500 ease-in-out"></div>
            </a>
            @endforeach
            <a href="{{ route('projects.index') }}" class="project overflow-hidden relative group flex flex-col gap-5 p-8 rounded-lg bg-brand-900 items-center justify-center">
                <svg class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" width="212" height="211" viewBox="0 0 212 211" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <linearGradient id="bimGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" stop-color="#0b4a6f" />
                            <stop offset="100%" stop-color="#02537D" />
                        </linearGradient>
                    </defs>
                    <path d="M123.464 211C123.464 177.234 123.465 144.353 123.463 111.472C123.463 105.652 123.169 99.8144 123.537 94.0184C123.856 89.0038 121.246 88.5656 117.281 88.5807C91.759 88.6779 66.2369 88.6223 40.715 88.6188C27.5332 88.6171 14.3509 88.6181 1.16863 88.6181C0.779249 88.0353 0.38938 87.4521 0 86.8691C9.23063 78.069 18.5841 69.3934 27.667 60.4434C46.8311 41.5593 65.806 22.484 85.0099 3.64068C86.8247 1.8602 89.7052 0.136113 92.0998 0.124639C131.284 -0.0704251 170.471 0.0140458 209.656 0.0445627C210.048 0.0455392 210.439 0.170781 211.511 0.354615C211.632 2.10995 211.878 4.02861 211.88 5.94776C211.911 43.2734 211.856 80.5995 212 117.924C212.016 122.092 210.589 124.932 207.679 127.822C180.477 154.842 153.42 182.007 126.303 209.113C125.762 209.655 125.01 209.985 123.464 211ZM153.831 58.3109C153.831 85.0791 153.831 110.8 153.831 136.521C154.416 136.857 155.001 137.193 155.586 137.529C163.102 129.69 170.786 122.001 178.021 113.911C179.902 111.808 181.316 108.461 181.348 105.668C181.607 82.5926 181.491 59.5125 181.489 36.4332C181.489 34.5392 181.489 32.6449 181.489 30.4956C159.983 30.4956 139.458 31.2387 119.022 30.218C106.66 29.6013 97.1249 32.4469 89.4618 42.3266C85.3641 47.6092 80.2077 52.073 75.5293 56.9061C75.8195 57.3746 76.1096 57.8426 76.4001 58.3109C101.828 58.3109 127.256 58.3109 153.831 58.3109Z" fill="url(#bimGradient)" />
                </svg>

                <h3 class="text-white font-medium text-2xl relative z-10">More<br />
                    Projects<br />
                    BIM Company</h3>

                <div class="pointer-events-none rounded-xl absolute inset-0 bg-gradient-to-tr from-cyan-500/40 to-blue-700/20 -translate-y-full translate-x-full group-hover:translate-x-0 group-hover:translate-y-0 transition-transform duration-500 ease-in-out"></div>
            </a>
        </div>
    </div>
</x-container>