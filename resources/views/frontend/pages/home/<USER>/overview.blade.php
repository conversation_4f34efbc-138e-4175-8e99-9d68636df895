<x-container class="relative">
    <div class="text-center mb-16">
        <span class="text-sm font-semibold text-brand-600 tracking-wider uppercase" data-animation="slideUp" data-duration="800" data-delay="100">{{ trans('bim_advantage') }}</span>
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mt-2" data-animation="slideUp" data-duration="1000" data-delay="200">{{ trans('pioneering_digital') }}</h2>
        <div class="mx-auto mt-4 max-w-3xl">
            <p class="text-lg text-gray-600" data-animation="slideUp" data-duration="1000" data-delay="300">
                {{ trans('bim_advantage_desc') }}
            </p>
        </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-12 gap-10 lg:gap-16">
        <div class="md:col-span-6 flex flex-col justify-center">
            <h3 class="text-2xl font-bold text-gray-900 mb-6" data-animation="slideUp" data-duration="1000" data-delay="400">
                {{ trans('why_bim_company') }}
            </h3>
            <div class="space-y-6">
                <div class="flex gap-4" data-animation="slideUp" data-duration="1000" data-delay="500">
                    <div class="flex-shrink-0">
                        <div class="flex items-center justify-center w-12 h-12 rounded-md bg-brand-600 text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6A2.25 2.25 0 0 1 6 3.75h2.25A2.25 2.25 0 0 1 10.5 6v2.25a2.25 2.25 0 0 1-2.25 2.25H6a2.25 2.25 0 0 1-2.25-2.25V6ZM3.75 15.75A2.25 2.25 0 0 1 6 13.5h2.25a2.25 2.25 0 0 1 2.25 2.25V18a2.25 2.25 0 0 1-2.25 2.25H6A2.25 2.25 0 0 1 3.75 18v-2.25ZM13.5 6a2.25 2.25 0 0 1 2.25-2.25H18A2.25 2.25 0 0 1 20.25 6v2.25A2.25 2.25 0 0 1 18 10.5h-2.25a2.25 2.25 0 0 1-2.25-2.25V6ZM13.5 15.75a2.25 2.25 0 0 1 2.25-2.25H18a2.25 2.25 0 0 1 2.25 2.25V18A2.25 2.25 0 0 1 18 20.25h-2.25A2.25 2.25 0 0 1 13.5 18v-2.25Z" />
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900">{{ trans('bim_solutions') }}</h4>
                        <p class="mt-2 text-gray-600">{{ trans('bim_solutions_desc') }}</p>
                    </div>
                </div>
                
                <div class="flex gap-4" data-animation="slideUp" data-duration="1000" data-delay="600">
                    <div class="flex-shrink-0">
                        <div class="flex items-center justify-center w-12 h-12 rounded-md bg-brand-600 text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z" />
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900">{{ trans('expert_team') }}</h4>
                        <p class="mt-2 text-gray-600">{{ trans('expert_team_desc') }}</p>
                    </div>
                </div>
                
                <div class="flex gap-4" data-animation="slideUp" data-duration="1000" data-delay="700">
                    <div class="flex-shrink-0">
                        <div class="flex items-center justify-center w-12 h-12 rounded-md bg-brand-600 text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 13.5l10.5-11.25L12 10.5h8.25L9.75 21.75 12 13.5H3.75z" />
                            </svg>
                        </div>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900">{{ trans('innovation_driven') }}</h4>
                        <p class="mt-2 text-gray-600">{{ trans('innovation_driven_desc') }}</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="md:col-span-6 relative">
            <div class="absolute -z-10 w-full h-full max-w-[600px] max-h-[600px] rounded-full bg-brand-100/50 blur-3xl"></div>
            <div class="grid grid-cols-2 gap-4 relative">
                <img class="rounded-lg shadow-xl object-cover w-full aspect-[4/5] transform translate-y-8" data-animation="slideUp" data-duration="1000" data-delay="400" src="{{ asset('images/overview/1.jpg') }}" loading="lazy" alt="BIM Model">
                <img class="rounded-lg shadow-xl object-cover w-full aspect-[4/5]" data-animation="slideUp" data-duration="1200" data-delay="500" src="{{ asset('images/overview/2.jpg') }}" loading="lazy" alt="Construction Project">
                <img class="rounded-lg shadow-xl object-cover w-full col-span-2 aspect-[16/9]" data-animation="slideUp" data-duration="1400" data-delay="600" src="{{ asset('images/slides/20.jpeg') }}" loading="lazy" alt="Digital Construction">
            </div>
        </div>
    </div>
</x-container>