<div class="header relative bg-gradient-to-b from-[#ECF7FF] to-white min-h-[50vh]">
    <div class="header-inner"></div>
    <x-frontend::navbar />
    <x-container class="mt-10 lg:mt-24">
        <div class="grid grid-cols-1 md:grid-cols-12 items-center gap-16 lg:gap-24">
            <div class="md:col-span-5" data-animation="slideLeft" data-duration="1000" data-delay="100">
                <img src="{{ asset('images/home_hero_picture1.png') }}" class="max-w-full" loading="lazy" alt="">
            </div>
            <div class="md:col-span-7">
                <h1 class="text-3xl font-bold header-title" data-animation="slideUp" data-duration="1000">BIM</h1>
                <p class="mt-6 text-[#01365E]" data-animation="slideUp" data-duration="1000" data-delay="200">BIM, established in 2014 and based in Erbil, Iraq, specializes in construction, engineering, maintenance service, supply chain management and procurement. BIM delivers sustainable, high-quality solutions and maintenance services. In construction and engineering, BIM provides comprehensive solutions with a focus on innovation and precision.</p>
                <a href="{{ route('about') }}" class="cursor-pointer mt-6 text-sm font-medium px-6 py-1.5 bg-primary text-white border border-primary rounded-md focus:outline-none transition-colors duration-300 hover:bg-white hover:text-primary inline-flex items-center" data-animation="slideUp" data-duration="800" data-delay="500">
                    <span>About us</span>
                    <x-icons.arrow-right class=" w-4 h-4 ml-2" />
                </a>
            </div>
        </div>
    </x-container>
</div>
@push('css')
<style>
    .header-inner {
        background-image: url("data:image/svg+xml;utf8,<svg width='33' height='33' viewBox='0 0 33 33' fill='none' xmlns='http://www.w3.org/2000/svg'><rect x='0.5' y='0.5' width='32' height='32' stroke='%23008CD3' stroke-opacity='0.08'/></svg>");
        background-repeat: repeat;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 0;
        opacity: 0.5;
        pointer-events: none;
        background-size: 28px 28px;
        background-position: center;
        background-attachment: fixed;
        width: 100%;
        height: 100%;
    }

    .header-title {
        font-family: 'Inter';
        font-style: normal;
        font-weight: 700;
        font-size: 60px;
        line-height: 64px;
        letter-spacing: -0.01em;

        background: linear-gradient(90deg, #008DD4 0%, #010D27 25.96%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;

        flex: none;
        order: 0;
        align-self: stretch;
        flex-grow: 0;

    }
</style>
@endpush