<x-frontend::app-layout class="contact-page">
    <div class="header relative bg-gradient-to-b from-[#ECF7FF] to-white min-h-[50vh]">
        <div class="header-inner"></div>
        <x-frontend::navbar />
        <x-container class="mt-10 lg:mt-16">
            <div class="flex items-center gap-6 col-span-12 md:col-span-6 lg:col-span-5">
                <x-section-icons.quote />
                <div class="flex gap-5 justify-between items-center flex-1">
                    <h2 class="font-bold text-3xl text-brand-800">{{ trans('lets_talk') }}</h2>
                </div>
            </div>
        </x-container>
        <x-container class="mt-10 lg:mt-12">
            <div class="pb-12 mx-auto flex sm:flex-nowrap flex-wrap">
                <iframe width="100%" height="200" class="relative w-full rounded-lg md:hidden mb-6 md:mb-0" frameborder="0" title="map" marginheight="0" marginwidth="0" scrolling="no" src="https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d1740.7054565771318!2d44.01934550622935!3d36.247250321955846!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e0!3m2!1sen!2siq!4v1747479018931!5m2!1sen!2siq" style="filter: grayscale(1) contrast(1.2);"></iframe>
                <div class="lg:w-2/3 md:w-1/2 bg-gray-300 rounded-lg overflow-hidden md:mr-8 md:p-10 md:flex items-end justify-start relative">
                    <iframe width="100%" height="100%" class="hidden md:block absolute inset-0 w-full md:h-full" frameborder="0" title="map" marginheight="0" marginwidth="0" scrolling="no" src="https://www.google.com/maps/embed?pb=!1m14!1m12!1m3!1d1740.7054565771318!2d44.01934550622935!3d36.247250321955846!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!5e0!3m2!1sen!2siq!4v1747479018931!5m2!1sen!2siq" style="filter: grayscale(1) contrast(1.2) opacity(0.4);"></iframe>
                    <div class="bg-white relative flex flex-wrap py-6 rounded-lg shadow-md w-full ">
                        <div class="lg:w-2/3 px-6">
                            <h2 class="title-font font-semibold text-gray-900 tracking-widest text-xs">{{ __('address') }}</h2>
                            <p class="mt-1">Erbil, KRG - New Azadi S21/12</p>
                            <div class="w-full mt-4">
                                <h3 class="title-font font-semibold text-gray-900 tracking-widest text-xs uppercase">{{ __('follow_us') }}</h3>
                                <x-social-icons class="mt-2" color="text-brand hover:text-brand-700" />
                            </div>
                        </div>
                        <div class="lg:w-1/3 px-6 mt-4 lg:mt-0">
                            <h2 class="title-font font-semibold text-gray-900 tracking-widest text-xs">{{ __('email') }}</h2>
                            <a href="mailto:<EMAIL>" class="text-indigo-500 leading-relaxed"><EMAIL></a>
                            <h2 class="title-font font-semibold text-gray-900 tracking-widest text-xs mt-4">{{ __('phone') }}</h2>
                            <a href="tel:+9647502244849" dir="ltr" class="leading-relaxed">+964 ************</a>
                        </div>
                    </div>
                </div>
                <div class="lg:w-1/3 md:w-1/2 bg-brand-900 flex flex-col rounded-lg md:ms-auto w-full py-8 px-6 mt-8 md:mt-0">
                    <div class="relative mb-4">
                        <label for="name" class="leading-7 text-sm text-white">{{ __('name') }}</label>
                        <input type="text" id="name" name="name" class="w-full bg-white rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 text-base outline-none text-gray-700 py-1 px-3 leading-8 transition-colors duration-200 ease-in-out">
                    </div>
                    <div class="relative mb-4">
                        <label for="email" class="leading-7 text-sm text-white">{{ __('email') }}</label>
                        <input type="email" id="email" name="email" class="w-full bg-white rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 text-base outline-none text-gray-700 py-1 px-3 leading-8 transition-colors duration-200 ease-in-out">
                    </div>
                    <div class="relative mb-4">
                        <label for="message" class="leading-7 text-sm text-white">{{ __('message') }}</label>
                        <textarea id="message" name="message" class="w-full bg-white rounded-lg border border-gray-300 focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 h-32 text-base outline-none text-gray-700 py-1 px-3 resize-none leading-6 transition-colors duration-200 ease-in-out"></textarea>
                    </div>
                    <button class="w-full px-6 py-3 rounded-lg font-medium tracking-wide text-white capitalize transition-colors duration-300 transform bg-brand hover:bg-brand focus:outline-none focus:ring focus:ring-brand-300 focus:ring-opacity-50">
                        {{ __('get_in_touch') }}
                    </button>
                </div>
            </div>
            <div class="bg-brand-600 w-full p-6 flex items-center justify-between rounded-lg text-white text-lg">
                <p>{{ trans('lets_talk_help') }}</p>
                <a href="mailto:<EMAIL>" class="inline-flex items-center gap-2 text-white font-medium text-lg">
                    <span><EMAIL></span>
                    <x-icons.arrow-right class="text-white w-6 h-6"/>
                </a>
            </div>
        </x-container>
    </div>

    @push('css')
    <style>
        .header-inner {
            background-image: url("data:image/svg+xml;utf8,<svg width='33' height='33' viewBox='0 0 33 33' fill='none' xmlns='http://www.w3.org/2000/svg'><rect x='0.5' y='0.5' width='32' height='32' stroke='%23008CD3' stroke-opacity='0.08'/></svg>");
            background-repeat: repeat;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 0;
            opacity: 0.5;
            pointer-events: none;
            background-size: 28px 28px;
            background-position: center;
            background-attachment: fixed;
            width: 100%;
            height: 880px;
        }

        .header-title {
            font-family: 'Inter';
            font-style: normal;
            font-weight: 700;
            font-size: 60px;
            line-height: 64px;
            letter-spacing: -0.01em;

            background: linear-gradient(90deg, #008DD4 0%, #010D27 25.96%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;

            flex: none;
            order: 0;
            align-self: stretch;
            flex-grow: 0;

        }
    </style>
    @endpush
</x-frontend::app-layout>