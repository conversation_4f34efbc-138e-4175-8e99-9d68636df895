<div {{ $attributes->merge(['class' => 'relative z-50 bg-none main-menu py-4 w-full'])}} style="z-index: 999;">
    <x-container class="px-4 sm:px-8 lg:px-12" x-data="Components.popover({ open: false, focus: false })" x-init="init()" x-on:keydown.escape="onEscape" @close-popover-group.window="onClosePopoverGroup">
        <div class="flex justify-between items-center" x-data="{searchVisible: false}">
            <img src="{{ asset('bim_logo.png') }}" class="app-logo max-w-[124px]" />
            <div class="flex items-center">
                <nav class="hidden md:flex items-center justify-center gap-6 xl:gap-10" x-data="Components.popoverGroup()" x-init="init()">
                    @foreach ($menus as $menu)
                    @if ($menu->hasChildren())
                    <x-nav-dropdown :active="$menu->isActive()">
                        {{ $menu->name }}
                        <x-slot name="dropdownMenu">
                            @foreach ($menu->children as $child)
                            <a href="{{ $child->getLink() }}" class="-m-3 block text-sm text-white p-3 hover:text-primary">
                                {{ $child->name }}
                            </a>
                            @endforeach
                        </x-slot>
                    </x-nav-dropdown>
                    @else
                    <x-nav-link href="{{ $menu->getLink() }}" :active="$menu->isActive()">{{ $menu->name }}</x-nav-link>
                    @endif
                    @endforeach
                </nav>
            </div>

            <div class="flex-shrink-0 flex items-center space-x-2 lg:space-x-0">
                <div class="hidden md:block">
                    <x-frontend::locale-selector />
                </div>
                <div class="md:hidden">
                    <button type="button" aria-label="Open menu" class="inline-flex items-center main-menu__burger justify-center p-1.5 text-secondary focus:outline-none hover:text-primary border border-transparent hover:border-brand group" x-on:click="toggleMenu()" x-on:mousedown="if (open) $event.preventDefault()" aria-expanded="false" :aria-expanded="open.toString()">
                        <span class="sr-only">Open menu</span>
                        <svg class="h-6 w-6" x-description="Heroicon name: outline/bars-3" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div x-show="open" x-transition:enter="duration-200 ease-out" x-transition:enter-start="opacity-0 scale-75" x-transition:enter-end="opacity-100 scale-100" x-transition:leave="duration-100 ease-in" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-75" class="fixed inset-x-0 top-0 origin-top-right transform p-2 transition md:hidden z-50" x-ref="panel" x-on:click.away="open = false" style="display: none;">
            <div class="divide-y-2 divide-gray-200 bg-white shadow-lg ring-2 ring-brand ring-opacity-5">
                <div class="p-3">
                    <div class="flex items-center justify-between">
                        <div>
                            <a href="{{ config('app.url') }}">
                                <x-logo class="h-14" />
                            </a>
                        </div>
                        <div>
                            <button type="button" aria-label="Close menu" class="inline-flex items-center justify-center p-2 text-gray-400 dark:text-slate-50 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-brand" @click="toggle">
                                <span class="sr-only">Close menu</span>
                                <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    <div class="flex flex-col -mx-3 gap-2 mt-8">
                        @foreach ($menus as $menu)
                        @if ($menu->hasChildren())
                        <x-mobile-nav-dropdown :last-item="$loop->last" :active="$menu->isActive()">
                            {{ $menu->name }}
                            <x-slot name="dropdownMenu">
                                @foreach ($menu->children as $child)
                                <a href="{{ $child->getLink() }}" class="-m-3 py-2 px-1 text-sm uppercase font-meiumd block hover:bg-brand">
                                    {{ $child->name }}
                                </a>
                                @endforeach
                            </x-slot>
                        </x-mobile-nav-dropdown>
                        @else
                        <x-mobile-nav-link :last-item="$loop->last" link="{{ $menu->getLink() }}" :active="$menu->isActive()">{{ $menu->name }}</x-nav-link>
                            @endif
                            @endforeach

                    </div>
                    <div class="mt-6 border-t py-6 space-y-4">
                        <div class="flex justify-center">
                            <x-frontend::locale-selector :is-mobile="true" />
                        </div>
                        <x-social-icons color="text-brand hover:text-brand-700" class="flex items-center justify-evenly" />
                    </div>
                </div>
            </div>
        </div>
    </x-container>
</div>