@php
$title = trans(config('app.name', 'ASCO')) ;
$title = (isset($seo['title']) ? $seo['title'] . ' | ' : '') . $title;
@endphp
<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" x-data="menu" dir="{{ locale_dir() }}" class="h-full no-js scroll-smooth focus:scroll-auto">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ $title }}</title>

    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="512x512" href="/android-chrome-512x512.png">
    <link rel="icon" type="image/png" sizes="192x192" href="/android-chrome-192x192.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    <meta name="msapplication-TileColor" content="#ffffff">
    <meta name="msapplication-TileImage" content="/ms-icon-144x144.png">
    <meta name="theme-color" content="#ffffff">

    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request()->fullUrl() }}">
    <meta property="og:title" content="{{ $title }}">
    @isset($seo['og_image'])
    <meta property="og:image" content="{{ $seo['og_image'] }}">
    <meta property="twitter:image" content="{{ $seo['og_image'] }}">
    @endisset
    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image">
    <meta property="twitter:url" content="{{ request()->fullUrl() }}">
    <meta property="twitter:title" content="{{ $title }}">

    @if (app()->environment('local'))
    <meta name="robots" content="noindex, nofollow" />
    @endif

    <script>
        document.documentElement.classList.remove('no-js');
        document.documentElement.classList.add('js');
    </script>

    @if(isset($seo['description']) && !blank($seo['description']))
    <meta name="description" content="{{ $seo['description'] }}">
    <meta property="og:description" content="{{ $seo['description'] }}">
    <meta property="twitter:description" content="{{ $seo['description'] }}">
    @else
    <meta name="description" content="{{ trans('bim_short_desc') }}">
    <meta property="og:description" content="{{ trans('bim_short_desc') }}">
    <meta property="twitter:description" content="{{ trans('bim_short_desc') }}">
    @endif
    <style>
        [x-cloak] {
            display: none !important;
        }
    </style>

    @isset($seo['hreflang'])
    @foreach ($seo['hreflang'] as $locale => $url)
    <link rel="alternate" hreflang="{{ $locale }}" href="{{ $url }}">
    @endforeach
    @endisset
    <!-- Styles -->
    @vite(['resources/assets/frontend/website.css'], 'assets/frontend')
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .no-js #loader-container {
            display: none;
        }

        #loader-container {
            opacity: 1;
            pointer-events: none;
            transition: opacity 0.2s ease-in-out;
        }

        .loaded #loader-container {
            opacity: 0;
        }
    </style>
    @stack('extra-styles')
    @stack('css')
    @livewireStyles

    @if (app()->environment('production'))
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-E79FFLSSSW"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-E79FFLSSSW');
    </script>
    @endif
</head>

<body {{ $attributes->merge(['class' => 'relative min-h-full antialiased bg-white']) }}>
    <!-- <x-preloader /> -->

    {{ $slot }}

    <span class="absolute top-0 left-[124px] w-[1px] bg-[#008CD3] h-full pointer-events-none -z-10" style="opacity: 0.08"></span>
    <span class="absolute top-0 left-[96px] w-[1px] bg-[#008CD3] h-full pointer-events-none -z-10" style="opacity: 0.08"></span>
    <span class="absolute top-0 right-[124px] w-[1px] bg-[#008CD3] h-full pointer-events-none -z-10" style="opacity: 0.08"></span>
    <span class="absolute top-0 right-[96px] w-[1px] bg-[#008CD3] h-full pointer-events-none -z-10" style="opacity: 0.08"></span>

    @include('frontend.layouts.footer')

    <x-scroll-top />

    @stack('teleport')
    @vite(['resources/assets/frontend/js/app.js'], 'assets/frontend')
    @livewireScripts
    @stack('scripts')

    <script>
        // Make current locale available globally for JavaScript components
        window.currentLocale = @json(app_locale());
        
        document.addEventListener('DOMContentLoaded', menu);

        function menu() {
            // main-menu
            const menu = document.querySelector('.main-menu');
            const menuItems = document.querySelectorAll('.main-menu__item');
            const logo = document.querySelector('.app-logo');
            const searchIcon = document.querySelector('.main-menu__search-icon');
            const burger = document.querySelector('.main-menu__burger');
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 200) {
                    menu.classList.remove('relative');
                    menu.classList.remove('py-4');
                    menu.classList.add('py-2');
                    menu.classList.add('bg-white');
                    menu.classList.add('bg-opacity-80');
                    menu.classList.add('backdrop-blur-2xl');
                    menu.classList.add('main-slide-in');
                    menu.style.position = 'fixed';
                    menu.style.top = '0';
                    menu.style.left = '0';
                    menu.style.right = '0';
                    menu.classList.add('shadow');
                } else {
                    menu.classList.add('relative');
                    menu.classList.remove('main-slide-in');
                    menu.classList.add('py-4');
                    menu.classList.remove('py-2');
                    menu.classList.remove('bg-white');
                    menu.classList.remove('bg-opacity-80');
                    menu.classList.remove('backdrop-blur-2xl');
                    menu.style.position = 'relative';
                    menu.style.top = '0';
                    menu.style.left = '0';
                    menu.style.right = '0';
                    menu.classList.remove('shadow');
                }
            }, {
                passive: true
            });
        }
    </script>
</body>

</html>