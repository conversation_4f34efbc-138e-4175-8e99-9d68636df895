<a {{ $attributes->merge(['class' => 'p-[1.5px] rounded-xl relative group']) }} href="{{ $news->path() }}">
    <div class="bg-gradient-to-b from-brand-50 bg-brand-100 w-full h-full rounded-xl absolute -right-2 -bottom-2 z-0 transition-transform duration-500 group-hover:-translate-x-2 group-hover:-translate-y-2"></div>
    <div class="p-4 md:p-5 bg-white rounded-xl relative">
        <div class="group flex flex-col h-full group overflow-hidden bg-white">
            <div class="aspect-w-16 aspect-h-11">
                <img class="w-full object-cover rounded-xl" src="{{ $news->thumbnail() }}" alt="{{ $news->title }}">
            </div>
            <div class="py-6">
                <h3 class="text-xl font-semibold text-gray-800 group-hover:text-primary transition-colors duration-300">
                    {{ $news->title }}
                </h3>
                <p class="mt-5 text-gray-600 dark:text-gray-400">
                    {{ $news->limitedExcerpt() }}
                </p>
            </div>
        </div>
    </div>
</a>