<div id="loader-container" class="fixed loaded bg-brand-900 bg-gradient-to-bl from-[#ECF7FF] to-white left-0 top-0 right-0 bottom-0 w-full h-full flex flex-col items-center justify-center" style="z-index: 9999999;">
  <div class="loader"></div>
</div>

<style>
.loader {
    width: 40px;
    height: 40px;
    --c:no-repeat linear-gradient(orange 0 0);
    background: var(--c),var(--c),var(--c),var(--c);
    background-size: 21px 21px;
    animation: l5 1.5s infinite cubic-bezier(0.3,1,0,1);
}
@keyframes l5 {
   0%   {background-position: 0    0,100% 0   ,100% 100%,0 100%}
   33%  {background-position: 0    0,100% 0   ,100% 100%,0 100%;width:60px;height: 60px}
   66%  {background-position: 100% 0,100% 100%,0    100%,0 0   ;width:60px;height: 60px}
   100% {background-position: 100% 0,100% 100%,0    100%,0 0   }
}
</style>

@push('scripts')
<script>
  // Stop the animation when the main content is loaded
  window.addEventListener('load', () => {
    setTimeout(() => {
      const loader = document.getElementById('loader-container')
      loader.addEventListener('transitionend', () => {
        loader.parentNode.removeChild(loader)
      })
      document.documentElement.classList.add('loaded');
    }, 500);
  });
</script>
@endpush
