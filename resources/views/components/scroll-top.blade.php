<button role="button" aria-label="scroll to top" class="scroll-to-top fixed right-10 bottom-10 w-12 h-12 bg-brand-900 rounded-full text-white flex items-center justify-center" style="opacity: 0;">
    <svg class="w-7 h-7" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"></path>
    </svg>
</button>
@push('scripts')
<script>
    const scrollTopButton = document.querySelector('.scroll-to-top');
    document.addEventListener('scroll', function(e) {
        if (window.pageYOffset < 200) {
            scrollTopButton.style.opacity = 0;
            return;
        }

        scrollTopButton.style.opacity = 1;

    }, {
        passive: true
    });

    if(scrollTopButton) {
        scrollTopButton.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            })
        })
    }
</script>
@endpush