@props(['isMobile' => true])
<div {{ $attributes->merge(['class' => 'relative inline-block text-start']) }} x-data="Components.menu({ open: false })" x-init="init()" @keydown.escape.stop="open = false; focusButton()" @click.away="onClickAway($event)">
    <!-- Language selector button - show generic language icon -->
    <button type="button" class="inline-flex items-center justify-center gap-1 px-2 py-1 rounded-lg shadow bg-white text-zinc-700 border border-primary/20 hover:border-primary/90 focus:outline-none transition-colors duration-200 group" x-ref="button" @click="onButtonClick()" @keyup.space.prevent="onButtonEnter()" @keydown.enter.prevent="onButtonEnter()" aria-expanded="true" aria-haspopup="true" x-bind:aria-expanded="open.toString()" @keydown.arrow-up.prevent="onArrowUp()" @keydown.arrow-down.prevent="onArrowDown()" title="Change language ({{ $activeLocale->native }})">
        <!-- Globe icon -->
         <svg class="h-6 w-6 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
        </svg>
        
        <!-- Arrow down icon with rotation animation -->
        <svg class="h-4 w-4 transition-transform duration-300 ease-in-out" 
             :class="{ 'rotate-180': open }" 
             fill="none" 
             stroke="currentColor" 
             viewBox="0 0 24 24" 
             xmlns="http://www.w3.org/2000/svg">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
        
        <span class="sr-only">Change language ({{ $activeLocale->native }})</span>
    </button>

    <!-- Dropdown menu -->
    <div x-show="open" 
         x-transition:enter="transition ease-out duration-200" 
         x-transition:enter-start="transform opacity-0 scale-95" 
         x-transition:enter-end="transform opacity-100 scale-100" 
         x-transition:leave="transition ease-in duration-150" 
         x-transition:leave-start="transform opacity-100 scale-100" 
         x-cloak 
         x-transition:leave-end="transform opacity-0 scale-95" 
         class="absolute {{ $isMobile ? 'right-0' : 'left-0' }} z-50 mt-2 w-48 origin-top-right bg-white dark:bg-slate-800 rounded-lg shadow-xl ring-1 ring-black/5 dark:ring-white/10 focus:outline-none border border-gray-200 dark:border-slate-700" 
         x-ref="menu-items" 
         role="menu" 
         aria-orientation="vertical" 
         aria-labelledby="menu-button" 
         tabindex="-1" 
         @keydown.arrow-up.prevent="onArrowUp()" 
         @keydown.arrow-down.prevent="onArrowDown()" 
         @keydown.tab="open = false" 
         @keydown.enter.prevent="open = false; focusButton()" 
         @keyup.space.prevent="open = false; focusButton()">
        
        <div class="py-2" role="none">
            @foreach ($localesExceptActive as $locale)
                <!-- Only show non-active languages - clickable -->
                <a href="{{ app_route(request()->route()->getName(), ['locale' => $locale->shortName()]) }}" 
                   class="flex items-center px-4 py-3 text-sm transition-colors duration-150 text-gray-700 dark:text-slate-200 hover:bg-gray-50 dark:hover:bg-slate-700" 
                   :class="{ 'bg-gray-100 text-gray-900 dark:bg-slate-700 dark:text-slate-50': activeIndex === {{ $loop->iteration }}, 'text-gray-700 dark:text-slate-200': !(activeIndex === {{ $loop->iteration }}) }" 
                   x-state:on="Active" 
                   x-state:off="Not Active" 
                   role="menuitem" 
                   tabindex="-1" 
                   id="menu-item-{{ $loop->iteration }}" 
                   @mouseenter="activeIndex = {{ $loop->iteration }}" 
                   @mouseleave="activeIndex = -1" 
                   @click="open = false; focusButton()">
                    
                    <!-- Flag icon -->
                    <div class="flex-shrink-0 mr-3">
                        <img src="{{ $locale->flag() }}" 
                             loading="lazy" 
                             class="h-5 w-5 rounded-sm object-cover" 
                             alt="{{ $locale->name }} flag">
                    </div>
                    
                    <!-- Language info -->
                    <div class="flex flex-col min-w-0 flex-1">
                        <span class="font-medium text-sm">{{ $locale->native }}</span>
                        <span class="text-xs text-gray-500 dark:text-slate-400">{{ $locale->name }}</span>
                    </div>
                </a>
            @endforeach
        </div>
    </div>
</div>