@props(['active'])

<div class="relative inline-block" x-data="Components.popover({ open: false, focus: false })" x-init="init()" x-on:keydown.escape="onEscape" x-on:close-popover-group.window="onClosePopoverGroup">
    <x-nav-link x-on:click.prevent="toggle" x-on:mousedown="if (open) $event.preventDefault()"  class="inline-flex justify-between items-start" :active="$active">
        <span>{{ $slot }}</span>
        <svg x-state:on="Item active" x-state:off="Item inactive" class="inline-block align-middle -mr-1 h-5 w-5 text-current transition-transform duration-200" :class="{ 'rotate-180': open }" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd"></path>
        </svg>
    </x-nav-link>
    <div x-show="open" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 translate-y-1" x-transition:enter-end="opacity-100 translate-y-0" x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 translate-y-0" x-transition:leave-end="opacity-0 translate-y-1" class="absolute start-0 z-10 mt-4 w-screen max-w-max transform px-2 sm:px-0" x-ref="panel" x-on:click.away="open = false" style="display: none;">
        <div class="overflow-hidden shadow-xl text-sm">
            <div class="relative grid gap-6 bg-zinc-500 px-4 py-4 sm:p-6">
                    {{ $dropdownMenu }}
            </div>
        </div>
    </div>
</div>