@props(['color' => 'text-white hover:text-blue-500'])

<div {{ $attributes->merge(['class' => 'flex -mx-1.5']) }}>
    @if (settings('linkedin'))
    <a href="{{ settings('linkedin') }}" class="mx-1.5 {{ $color }} transition-colors duration-300 transform">
        <x-icons.linkedin class="w-6 h-6" />
    </a>
    @endif

    @if (settings('facebook'))
    <a href="{{ settings('facebook') }}" class="mx-1.5 {{ $color }} transition-colors duration-300 transform ">
        <x-icons.fb />
    </a>
    @endif

    

    @if (settings('instagram'))
    <a href="{{ settings('instagram') }}" class="mx-1.5 {{ $color }} transition-colors duration-300 transform">
        <x-icons.instagram />
    </a>
    @endif
    

    @if (settings('twitter_x'))
    <a href="{{ settings('twitter_x') }}" class="mx-1.5 {{ $color }} transition-colors duration-300 transform">
        <x-icons.x />
    </a>
    @endif
    
    @if (settings('threads'))
    <a href="{{ settings('threads') }}" class="mx-1.5 {{ $color }} transition-colors duration-300 transform">
        <x-icons.threads />
    </a>
    @endif

    @if (settings('youtube'))
    <a href="{{ settings('youtube') }}" class="mx-1.5 {{ $color }} transition-colors duration-300 transform">
        <x-icons.youtube />
    </a>
    @endif
</div>