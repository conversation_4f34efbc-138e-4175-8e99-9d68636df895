@props(['active', 'link'])
@php
$active ??= false;
$classes = $active
? 'text-white bg-brand'
: 'text-zinc-700 hover:bg-brand hover:text-white';
@endphp

<div class="relative" x-data="Components.popover({ open: false, focus: false })" x-init="init()" x-on:keydown.escape="onEscape" x-on:close-popover-group.window="onClosePopoverGroup">
    <button type="button" x-state:on="Item active" x-state:off="Item inactive" class="{{ $classes }} group px-5 py-3 inline-flex w-full justify-between items-center text-sm uppercase font-bold hover:text-brand-600 focus:outline-none text-gray-600" :class="{ 'text-gray-900': open, 'text-gray-600': !(open) }" x-on:click="toggle" x-on:mousedown="if (open) $event.preventDefault()" aria-expanded="false" :aria-expanded="open.toString()">
        <span>{{ $slot }}</span>
        <svg x-state:on="Item active" x-state:off="Item inactive" class="h-5 w-5 fill-current transition-transform duration-200 ease" :class="{ 'rotate-180': open }" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
            <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd"></path>
        </svg>
    </button>
    <div x-show="open" x-transition:enter="transition ease-out duration-200" x-transition:enter-start="opacity-0 translate-y-1" x-transition:enter-end="opacity-100 translate-y-0" x-transition:leave="transition ease-in duration-150" x-transition:leave-start="opacity-100 translate-y-0" x-transition:leave-end="opacity-0 translate-y-1" class="w-full transform px-2 sm:px-0" x-ref="panel" x-on:click.away="open = false" style="display: none;">
        <div class="overflow-hidden">
            <div class="relative flex flex-col gap-6 bg-white dark:bg-slate-900 px-5 py-4">
                    {{ $dropdownMenu }}
            </div>
        </div>
    </div>
</div>