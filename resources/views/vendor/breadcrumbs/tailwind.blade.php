@unless ($breadcrumbs->isEmpty())
    <nav aria-label="Breadcrumb" class="container mx-auto breadcrumb">
        <ol class="py-4 px-2 lg:px-8 flex flex-wrap bg-zinc-50 dark:bg-slate-800 text-sm text-gray-800 dark:text-slate-100">
            @foreach ($breadcrumbs as $breadcrumb)

                @if ($breadcrumb->url && !$loop->last)
                    <li>
                        <a href="{{ $breadcrumb->url }}" class="text-blue-600 dark:text-blue-300 hover:text-blue-900 dark:hover:text-blue-500 hover:underline focus:text-blue-900 dark:focus:text-blue-500 focus:underline">
                            {{ $breadcrumb->title }}
                        </a>
                    </li>
                @else
                    <li class="lg:max-w-[38rem] lg:truncate" aria-current="page">
                        {{ $breadcrumb->title }}
                    </li>
                @endif

                @unless($loop->last)
                    <li class="text-gray-500 px-2">
                        /
                    </li>
                @endif

            @endforeach
        </ol>
    </nav>
@endunless
