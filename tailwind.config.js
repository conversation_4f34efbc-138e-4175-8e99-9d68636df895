const defaultTheme = require("tailwindcss/defaultTheme");

/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        "./vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php",
        "./storage/framework/views/*.php",
        "./resources/views/**/*.blade.php",
        "./resources/assets/admin/js/**/*.{vue,js}",
    ],

    theme: {
        extend: {
            fontFamily: {
                sans: [...defaultTheme.fontFamily.sans],
            },
            colors: {
                primary: {
                    DEFAULT: "#008cd3",
                    50: "#f0f9ff",
                    100: "#e0f2fe",
                    200: "#b9e6fe",
                    300: "#7cd3fd",
                    400: "#36bffa",
                    500: "#0ca7eb",
                    600: "#008cd3",
                    700: "#016aa3",
                    800: "#065986",
                    900: "#0b4a6f",
                    950: "#072f4a",
                },
            },
        },
    },

    plugins: [require("@tailwindcss/forms")],
};
