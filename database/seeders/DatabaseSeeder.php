<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;

use App\Admin\Support\Roles;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     *
     * @return void
     */
    public function run()
    {
        activity()->disableLogging();

        $this->call(PermissionSeeder::class);
        $this->call(SettingSeeder::class);
        $this->call(BooleanSettingsSeeder::class);
        $this->setupRoles();

        $admin = \App\Models\User::factory()->create([
            'name' => 'Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('pa$$word'),
        ]);

        $admin->assignRole(Roles::Admin->value);
        $admin = Role::findByName(Roles::Admin->value);
        foreach (Permission::get() as $permission) {
            $admin->givePermissionTo($permission);
        }

        $superAdmin = \App\Models\User::factory()->create([
            'name' => 'Super Admin',
            'email' => '<EMAIL>',
            'password' => bcrypt('pa$$word'),
        ]);

        $superAdmin->forceFill([
            'is_super_admin' => true,
        ])->saveQuietly();
    }

    protected function setupRoles()
    {
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        Role::create([
            'name' => Roles::Admin->value,
            'description' => 'System administrator with full access to all features'
        ]);
        
        Role::create([
            'name' => Roles::User->value,
            'description' => 'Standard user with limited access to basic features'
        ]);
    }
}
