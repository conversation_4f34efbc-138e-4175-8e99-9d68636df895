<?php

namespace Database\Seeders;

use App\Admin\Support\Permissions;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\PermissionRegistrar;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        foreach (Permissions::cases() as $permission) {
            Permission::firstOrCreate([
                'name' => $permission->value,
            ]);
        }
        app(PermissionRegistrar::class)->forgetCachedPermissions();
    }
}
