<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Setting::truncate();

        $settings = [
            [
                'key' => 'linkedin',
                'value' => '#',
            ],
            [
                'key' => 'instagram',
                'value' => '#',
            ],
            [
                'key' => 'twitter_x',
                'value' => '#',
            ],
            [
                'key' => 'facebook',
                'value' => '#',
            ],
            [
                'key' => 'threads',
                'value' => '#',
            ],
            [
                'key' => 'youtube',
                'value' => '#',
            ],
        ];

        foreach ($settings as $setting) {
            Setting::create([
                'key' => $setting['key'],
                'value' => $setting['value'],
            ]);
        }
    }
}
