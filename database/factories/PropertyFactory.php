<?php

namespace Database\Factories;

use App\Models\Property;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class PropertyFactory extends Factory
{
    protected $model = Property::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->sentence(5),
            'description' => $this->faker->paragraph(4),
            'type' => $this->faker->randomElement(['office', 'residential']),
            'price' => $this->faker->randomFloat(2, 100000, 10000000),
            'area' => $this->faker->numberBetween(500, 5000),
            'floor_number' => $this->faker->numberBetween(1, 100),
            'bedrooms' => $this->faker->numberBetween(1, 5),
            'bathrooms' => $this->faker->numberBetween(1, 3),
            'kitchen' => $this->faker->numberBetween(1, 1), // 1 for yes, 0 for no
            'is_featured' => $this->faker->boolean(30), // 30% chance of getting true
        ];
    }
}
