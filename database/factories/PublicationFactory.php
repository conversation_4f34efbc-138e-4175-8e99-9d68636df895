<?php

namespace Database\Factories;

use App\Models\Category;
use App\Models\Governorate;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Publication>
 */
class PublicationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            'title' => [
                'en' => $title = $this->faker->text(100),
            ],
            'content' => [
                'en' => $this->faker->text(400),
            ],
            'publication_date' => now(),
            'category_id' => Category::factory(),
        ];
    }
}
