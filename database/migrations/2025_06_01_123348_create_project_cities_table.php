<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('project_cities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_map_id')->constrained()->onDelete('cascade');
            $table->text('name'); // City name (translatable)
            $table->string('slug')->unique();
            $table->json('center_coordinates'); // City center [lat, lng]
            $table->integer('zoom_level')->default(12);
            $table->json('bounds'); // City bounds for map view
            $table->text('description')->nullable(); // City description (translatable)
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('project_cities');
    }
};
