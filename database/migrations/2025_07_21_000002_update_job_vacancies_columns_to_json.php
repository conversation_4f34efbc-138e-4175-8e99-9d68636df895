<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('job_vacancies', function (Blueprint $table) {
            // Convert text columns to json type
            $table->json('title')->change();
            $table->json('description')->change();
            $table->json('location')->nullable()->change();

            // Convert existing data to JSON format
            DB::statement("UPDATE job_vacancies SET 
                title = JSON_OBJECT('en', title, 'ku', title, 'ar', title),
                description = JSON_OBJECT('en', description, 'ku', description, 'ar', description),
                location = CASE 
                    WHEN location IS NULL THEN NULL 
                    ELSE JSON_OBJECT('en', location, 'ku', location, 'ar', location)
                END
            ");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('job_vacancies', function (Blueprint $table) {
            // Convert JSON columns back to text
            $table->text('title')->change();
            $table->text('description')->change();
            $table->text('location')->nullable()->change();
        });
    }
};
