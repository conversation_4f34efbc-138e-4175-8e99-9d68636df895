<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('project_city_customizations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_map_id')->constrained()->onDelete('cascade');
            $table->foreignId('project_city_id')->constrained()->onDelete('cascade');
            $table->text('custom_title')->nullable(); // Custom title (translatable)
            $table->text('custom_description')->nullable(); // Custom description (translatable)
            $table->timestamps();
            
            // Ensure one customization per city per project map
            $table->unique(['project_map_id', 'project_city_id'], 'project_city_customization_unique');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('project_city_customizations');
    }
};
