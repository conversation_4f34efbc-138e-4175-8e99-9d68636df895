<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('project_maps', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained()->onDelete('cascade');
            $table->string('name'); // Map name (e.g., "Main Map", "City Distribution", etc.)
            $table->enum('type', ['single', 'multi_city']); // single map with multiple markers or multi-city maps
            $table->json('center_coordinates')->nullable(); // Default center point [lat, lng]
            $table->integer('default_zoom')->default(10);
            $table->json('bounds')->nullable(); // Map bounds for fitting view
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('project_maps');
    }
};
