<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('project_cities', function (Blueprint $table) {
            // Make project_map_id nullable to support standalone cities
            $table->foreignId('project_map_id')->nullable()->change();
            
            // Update the slug to be unique only when not null (for project-specific cities)
            // We'll handle uniqueness validation in the model for standalone cities
            $table->dropUnique(['slug']);
            $table->index('slug');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('project_cities', function (Blueprint $table) {
            // Revert project_map_id to non-nullable
            $table->foreignId('project_map_id')->nullable(false)->change();
            
            // Restore unique constraint on slug
            $table->dropIndex(['slug']);
            $table->string('slug')->unique()->change();
        });
    }
};
