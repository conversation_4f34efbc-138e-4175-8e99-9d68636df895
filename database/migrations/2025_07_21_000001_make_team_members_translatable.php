<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('team_members', function (Blueprint $table) {
            // Convert existing columns to JSON
            $table->json('name')->nullable()->change();
            $table->json('department')->nullable()->change();
            $table->json('role')->nullable()->change();

            // Convert existing data to JSON format
            DB::statement("UPDATE team_members SET 
                name = JSON_OBJECT('en', name, 'ku', name, 'ar', name),
                department = JSON_OBJECT('en', department, 'ku', department, 'ar', department),
                role = JSON_OBJECT('en', role, 'ku', role, 'ar', role)
            ");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('team_members', function (Blueprint $table) {
            // Convert JSON columns back to strings
            $table->string('name')->nullable()->change();
            $table->string('department')->nullable()->change();
            $table->string('role')->nullable()->change();
        });
    }
};
