<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('project_markers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_map_id')->constrained()->onDelete('cascade');
            $table->foreignId('project_city_id')->nullable()->constrained()->onDelete('cascade'); // For city-specific markers
            $table->text('title'); // Marker title (translatable)
            $table->text('description')->nullable(); // Marker description (translatable)
            $table->json('coordinates'); // Marker position [lat, lng]
            $table->string('icon_type')->default('default'); // Marker icon type
            $table->string('icon_color')->default('#008CD3'); // Marker color
            $table->json('popup_content')->nullable(); // Custom popup content
            $table->string('link_url')->nullable(); // Optional link
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('project_markers');
    }
};
