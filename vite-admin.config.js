import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue'
import DefineOptions from 'unplugin-vue-define-options/vite'
import fs from 'fs'
import {homedir} from 'os'
import {resolve} from 'path'

let host = 'bim.test'
const path = 'resources/assets/admin';

export default defineConfig({
    plugins: [
        laravel({
            input: [
                path + '/css/admin.css',
                path + '/js/admin.js',
            ],
            refresh: true,
            buildDirectory: 'assets/admin'
        }),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false,
                },
            },
        }),
        DefineOptions(),
    ],
    css: {
        postcss: {
            plugins: [
                require("autoprefixer"),
                require('tailwindcss'),
            ]
        },
    },
    resolve: {
        alias: {
            '@': '/resources/assets/admin/js',
            'ziggy': '/vendor/tightenco/ziggy/src/js'
        },
    },
    server: detectServerConfig(host),
    build: {
        polyfillModulePreload: true,
    }
});

function detectServerConfig(host) {
    let keyPath = resolve(homedir(), `Library/Application Support/Herd/config/valet/Certificates/${host}.key`)
    let certificatePath = resolve(homedir(), `Library/Application Support/Herd/config/valet/Certificates/${host}.crt`)

    if (!fs.existsSync(keyPath)) {
        return {}
    }

    if (!fs.existsSync(certificatePath)) {
        return {}
    }

    return {
        hmr: {host},
        host,
        https: {
            key: fs.readFileSync(keyPath),
            cert: fs.readFileSync(certificatePath),
        },
    }
}