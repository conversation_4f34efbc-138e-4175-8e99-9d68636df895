<?php

use App\Admin\Http\Controllers\CityController;
use App\Admin\Http\Controllers\DashboardController;
use App\Admin\Http\Controllers\GalleryController;
use App\Admin\Http\Controllers\JobVacancyController;
use App\Admin\Http\Controllers\ProjectController;
use App\Admin\Http\Controllers\ProjectMapController;
use App\Admin\Http\Controllers\ProjectCityController;
use App\Admin\Http\Controllers\ProjectMarkerController;
use App\Admin\Http\Controllers\PublicationController;
use App\Admin\Http\Controllers\RoleController;
use App\Admin\Http\Controllers\RolePermissionController;
use App\Admin\Http\Controllers\SettingController;
use App\Admin\Http\Controllers\SlideController;
use App\Admin\Http\Controllers\TeamMemberController;
use App\Admin\Http\Controllers\UploadImageController;
use App\Admin\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

Route::get('/', DashboardController::class)->name('home');

Route::resource('job-vacancies', JobVacancyController::class);
Route::resource('publications', PublicationController::class);
Route::resource('projects', ProjectController::class);
Route::resource('team-members', TeamMemberController::class);
Route::delete('projects/medias/{media}', [ProjectController::class, 'destroyMedia'])->name('projects.media.destroy');

// Cities Management Routes
Route::resource('cities', CityController::class);
Route::post('cities/{city}/toggle-status', [CityController::class, 'toggleStatus'])->name('cities.toggle-status');

// Project Maps Routes
Route::resource('project-maps', ProjectMapController::class);

// Project Cities Routes
Route::resource('project-cities', ProjectCityController::class);
Route::post('project-cities/sort-order', [ProjectCityController::class, 'updateSortOrder'])->name('project-cities.sort-order');
Route::post('project-cities/{projectCity}/toggle-status', [ProjectCityController::class, 'toggleStatus'])->name('project-cities.toggle-status');

// Project Markers Routes (API endpoints for dynamic management)
Route::prefix('api')->group(function () {
    Route::get('project-markers', [ProjectMarkerController::class, 'index'])->name('api.project-markers.index');
    Route::post('project-markers', [ProjectMarkerController::class, 'store'])->name('api.project-markers.store');
    Route::get('project-markers/{projectMarker}', [ProjectMarkerController::class, 'show'])->name('api.project-markers.show');
    Route::put('project-markers/{projectMarker}', [ProjectMarkerController::class, 'update'])->name('api.project-markers.update');
    Route::delete('project-markers/{projectMarker}', [ProjectMarkerController::class, 'destroy'])->name('api.project-markers.destroy');
    Route::post('project-markers/sort-order', [ProjectMarkerController::class, 'updateSortOrder'])->name('api.project-markers.sort-order');
    Route::post('project-markers/{projectMarker}/toggle-status', [ProjectMarkerController::class, 'toggleStatus'])->name('api.project-markers.toggle-status');
});

Route::resource('galleries', GalleryController::class);

Route::resource('slides', SlideController::class);

Route::post('/upload-image', [UploadImageController::class, 'store'])->name('media.upload');

Route::get('settings', [SettingController::class, 'edit'])->name('settings.edit');
Route::put('settings/{setting}', [SettingController::class, 'update'])->name('settings.update');

Route::resource('users', UserController::class);

// Role Management Routes
Route::resource('roles', RoleController::class)
    ->middleware('auth.superadmin')
    ->except(['show']);

Route::get('roles/{role}/permissions', [RolePermissionController::class, 'index'])
    ->middleware('auth.superadmin')
    ->name('roles.permissions.index');

Route::put('roles/{role}/permissions', [RolePermissionController::class, 'update'])
    ->middleware('auth.superadmin')
    ->name('roles.permissions.update');
