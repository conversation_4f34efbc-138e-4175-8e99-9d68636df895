<?php

use Illuminate\Support\Facades\Route;
use App\Frontend\Http\Controllers\NewsController;
use App\Frontend\Http\Controllers\HomePageController;
use App\Frontend\Http\Controllers\AboutPageController;
use App\Frontend\Http\Controllers\CareerController;
use App\Frontend\Http\Controllers\JobApplicationController;
use App\Frontend\Http\Controllers\ProjectController;
use App\Frontend\Http\Controllers\PublicationController;

Route::get('/', HomePageController::class)->name('home');

Route::get('/news/', [NewsController::class, 'index'])->name('news');
Route::get('news/{publication:slug}', [PublicationController::class, 'show'])->name('publications.show');

Route::get('projects', [ProjectController::class, 'index'])->name('projects.index');
Route::get('projects/{project:slug}', [ProjectController::class, 'show'])->name('projects.show');

Route::get('careers', [CareerController::class, 'index'])->name('careers.index');
Route::get('careers/{job}', [CareerController::class, 'show'])->name('careers.show');
Route::post('careers/{job}/apply', [JobApplicationController::class, 'store'])->name('careers.apply');

Route::get('about', [AboutPageController::class, 'about'])->name('about');
Route::view('contact-us', 'frontend.pages.contact.show')->name('contact');
