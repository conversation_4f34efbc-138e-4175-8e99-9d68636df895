<?php

use App\Models\Setting;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Route;

if (! function_exists('app_locale')) {
    /**
     * Get or set currenct locale (langauge)
     * for example: en, ku, ar
     *
     * @param  string|null  $locale
     * @return string
     */
    function app_locale(?string $locale = null): string
    {
        if (! is_null($locale)) {
            app()->setLocale($locale);
            Carbon::setLocale($locale);
        }

        return app()->getLocale();
    }
}

if (! function_exists('app_route')) {
    /**
     * Get localized route
     *
     * @param  string  $name
     * @param  string|null  $locale
     * @return string
     */
    function app_route(string $name, array $parameters = []): string
    {
        // Get route parameteres
        $routeParameterNames = Route::getRoutes()->getByName($name)?->parameterNames() ?? [];

        // Current route or url paramteres
        $currentRouteParameters = request()->route()?->parameters() ?? [];

        // Remove unnecessary parameters
        $routeParameters = Arr::where($currentRouteParameters, function ($value, $name) use ($routeParameterNames) {
            return in_array($name, $routeParameterNames);
        });

        $parameters = array_merge($routeParameters, $parameters);

        return route($name, $parameters);
    }
}

if (! function_exists('locale_dir')) {
    /**
     *  get direction of current locale
     *
     * @param  string  $path
     * @return string
     */
    function locale_dir(): string
    {
        $locale = app_locale();

        return config('lang.supported_locales')[$locale]['direction'];
    }
}

if (! function_exists('isRtl')) {
    /**
     *  Determine if current locale direction is right-to-left
     *
     * @param  string  $path
     * @return string
     */
    function isRtl(): bool
    {
        return locale_dir() == 'rtl';
    }
}

if (! function_exists('current_user')) {
    /**
     * Get Current authenticated user
     *
     * @return \App\Models\User
     */
    function current_user(): App\Models\User
    {
        return Auth::user() ?? new \App\Models\User();
    }
}

if (! function_exists('admin_path')) {
    /**
     * return path of admin in App directory
     *
     * @param  string  $path
     * @return string
     */
    function admin_path(string $path = ''): string
    {
        return app_path('admin'.($path ? DIRECTORY_SEPARATOR.$path : $path));
    }
}

if (! function_exists('website_path')) {
    /**
     *  return path of website in App directory
     *
     * @param  string  $path
     * @return string
     */
    function website_path(string $path = ''): string
    {
        return app_path('frontend'.($path ? DIRECTORY_SEPARATOR.$path : $path));
    }
}

if (! function_exists('settings')) {
    /**
     *  Get settings
     *
     * @param  string  $path
     * @return string
     */
    function settings(?string $settingKey = null, ?string $default = null): ?string
    {
        if (empty($settingKey)) {
            return null;
        }

        /** @var \Illuminate\Support\Collection */
        $allSettings = Cache::rememberForever(
            Setting::cacheKey(),
            fn () => Setting::get()
        );

        return $allSettings->firstWhere(fn ($setting) => $setting->key == $settingKey)?->value ?? $default;
    }
}

if (! function_exists('remove_style_attribute')) {
    /**
     *  Remove style attributes from string or html
     *
     * @param  string  $html
     * @return string
     */
    function remove_style_attribute(string $html = ''): ?string
    {
        if (empty($html)) {
            return null;
        }

        return preg_replace('/(<[^>]+) style=".*?"/i', '$1', $html);
    }
}
