<?php

namespace App\Frontend\ViewModels;

use App\Models\Publication;
use App\Shared\Enums\PublicationMediaCollection;
use Illuminate\Database\Eloquent\Collection;
use Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection;
use Spatie\ViewModels\ViewModel;

class PublicationViewModel extends ViewModel
{
    public function __construct(
        public readonly Publication $publication
    ) {
    }

    /**
     * Get all PDF attachments
     *
     * @return MediaCollection
     */
    public function attachments(): MediaCollection
    {
        return $this->publication->getMedia(PublicationMediaCollection::Attachments->value);
    }


    /**
     * Recent 3 publica
     *
     * @return Collection<Publication>
     */
    public function recent_publications(): Collection
    {
        return  Publication::select(['id', 'title', 'slug', 'excerpt'])
            ->with(['media'])
            ->whereNot('id', $this->publication->id)
            ->latest('id')
            ->take(5)
            ->get();
    }
}
