<?php

namespace App\Frontend\ViewModels;

use App\Models\Project;
use App\Models\Publication;
use App\Models\Slide;
use Illuminate\Database\Eloquent\Collection;
use Spatie\ViewModels\ViewModel;

class HomePageViewModel extends ViewModel
{

    public function __construct() {}

    /**
     * Get latest 6 slides
     *
     * @return Collection<\App\Models\Project>
     */
    public function projects(): Collection
    {
        return Project::query()
            ->with(['media'])
            ->oldest('sort')
            ->limit(3)
            ->get();
    }

    public function news(): Collection
    {
        return Publication::query()
            ->select(['id', 'title', 'slug', 'excerpt', 'created_at'])
            ->with(['media'])
            ->latest('id')
            ->take(12)
            ->get();
    }
}
