<?php

namespace App\Frontend\ViewModels;

use App\Models\Publication;
use App\Models\Story;
use Illuminate\Database\Eloquent\Collection;
use Spatie\ViewModels\ViewModel;

class PublicationsViewModel extends ViewModel
{
    /**
     * Latest 5 featured Stories
     *
     * @return Collection<Story>
     */
    public function featured_publications(): Collection
    {
        return Publication::select([
            'id',
            'slug',
            'title',
            'content',
        ])
            ->with(['media'])
            ->latest('id')
            ->take(6)
            ->get();
    }
}
