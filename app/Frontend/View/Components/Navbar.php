<?php

namespace App\Frontend\View\Components;

use App\Frontend\Support\Menu;
use App\Frontend\Support\MenuBuilder;
use App\Models\Plan;
use App\Models\Property;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\View\Component;

class Navbar extends Component
{
    protected Request $request;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Get the view / contents that represents the component.
     *
     * @return \Illuminate\View\View
     */
    public function render()
    {
        return view('frontend.layouts.main-navbar', [
            'menus' => $this->buildMenu(),
        ]);
    }

    public function buildMenu()
    {
        $menu = MenuBuilder::build()
            ->add(
                Menu::new()
                    ->setName(trans('Home'))
                    ->setRoute('home')
            )
            ->add(
                Menu::new()
                    ->setName(trans('CEO Message'))
                    ->setRoute('ceo-message')
            )
            ->add(
                Menu::new()
                    ->setName(trans('Our Projects'))
                    ->setRoute('projects.index')
            );
        if (settings(Setting::SHOW_CAREERS_PAGE) == 'true') {
            $menu->add(
                Menu::new()
                    ->setName(trans('Careers'))
                    ->setRoute('careers.index')

            );
        }
        if (settings(Setting::SHOW_NEWS_PAGE) == 'true') {
            $menu->add(
                Menu::new()
                    ->setName(trans('News'))
                    ->setRoute('news')
            );
        }
        $menu->add(
            Menu::new()
                ->setName('About')
                ->setRoute('about')
        )
        ->add(
            Menu::new()
                ->setName('Contact us')
                ->setRoute('contact')
        );

        return $menu->getMenus();
    }
}
