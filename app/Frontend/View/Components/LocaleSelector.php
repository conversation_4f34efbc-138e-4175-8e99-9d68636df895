<?php

namespace App\Frontend\View\Components;

use App\Shared\Support\Locale;
use App\Shared\Support\LocaleManager;
use Illuminate\Support\Collection;
use Illuminate\View\Component;

class LocaleSelector extends Component
{
    public Locale $activeLocale;

    public Collection $locales;

    public Collection $localesExceptActive;

    public function __construct()
    {
        $this->activeLocale = LocaleManager::activeLocale();
        $this->locales = LocaleManager::supportedLocales();
        $this->localesExceptActive = LocaleManager::localesExceptActive(); // Hide active language from dropdown
    }

    /**
     * Get the view / contents that represents the component.
     *
     * @return \Illuminate\View\View
     */
    public function render()
    {
        return view('components.locale-selector');
    }
}
