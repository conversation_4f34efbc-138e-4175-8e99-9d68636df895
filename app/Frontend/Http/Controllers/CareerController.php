<?php

namespace App\Frontend\Http\Controllers;

use App\Models\JobVacancy;
use Illuminate\Support\Str;

class CareerController extends BaseController
{
    public function index()
    {
        $this->seo()
            ->setTitle('Careers')
            ->setDescription('Join our team');

        return view('frontend.pages.careers.index', [
            'availableJobs' => JobVacancy::latest()->open()->limit(25)->get()
        ]);
    }

    public function show(string $job)
    {
        $job = JobVacancy::query()->where('slug', $job)->firstOrFail();

        $this->seo()
            ->setTitle($job->title)
            ->setDescription(Str::of($job->description)->limit(160)->stripTags());

        return view('frontend.pages.careers.show', compact('job'));
    }
}
