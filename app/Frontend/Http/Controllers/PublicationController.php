<?php

namespace App\Frontend\Http\Controllers;

use App\Frontend\ViewModels\PublicationViewModel;
use App\Models\Publication;

class PublicationController extends BaseController
{
    public function show(Publication $publication)
    {
        abort_if(! $publication, 404);

        $this->seo()
            ->setTitle($publication->title)
            ->setOgImage($publication->thumbnail())
            ->setDescription($publication->excerpt);

        return view('frontend.pages.publications.show', new PublicationViewModel($publication));
    }
}
