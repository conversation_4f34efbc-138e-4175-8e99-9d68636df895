<?php

namespace App\Frontend\Http\Controllers;

use App\Models\Publication;
use Illuminate\Http\Request;

class NewsController extends BaseController
{
    public function index(Request $request)
    {
        $this->seo()
            ->setTitle(trans('News'))
            ->setDescription(trans('app.about'));

        return view('frontend.pages.news.index');
    }

    public function show(Request $request, Publication $news)
    {
        $this->seo()
            ->setTitle(trans('News'))
            ->setDescription(trans('app.about'));

        return view('frontend.pages.news.index', compact('news'));
    }
}
