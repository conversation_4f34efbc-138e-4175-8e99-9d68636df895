<?php

namespace App\Frontend\Http\Controllers;

use App\Models\Project;

class ProjectController extends BaseController
{
    public function index()
    {
        $this->seo()->setTitle(__('projects_seo_title'));

        return view('frontend.pages.projects.index', [
            'projects' => Project::oldest('sort')->with(['media'])->get(),
        ]);
    }

    public function show(Project $project)
    {
        $this->seo()
            ->setTitle($project->name)
            ->setDescription($project->excerpt);

        $project->load(['maps.customizations.projectCity', 'maps.markers']);

        return view('frontend.pages.projects.show', [
            'project' => $project,
            'projectMaps' => $project->maps->where('is_active', true),
        ]);
    }
}
