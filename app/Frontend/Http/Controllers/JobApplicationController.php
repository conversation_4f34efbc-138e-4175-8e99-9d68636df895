<?php

namespace App\Frontend\Http\Controllers;

use App\Models\JobSubmission;
use App\Models\JobVacancy;
use Illuminate\Http\Request;

class JobApplicationController extends BaseController
{
    public function store(Request $request, string $job)
    {
        // Find the job by slug
        $jobVacancy = JobVacancy::where('slug', $job)->firstOrFail();

        // Check if job is still open
        if ($jobVacancy->closed) {
            return back()->withErrors(['job' => 'This job position is no longer accepting applications.']);
        }

        $validated = $request->validate([
            'full_name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'summary' => 'nullable|string|max:1000',
            'cv' => 'required|file|mimes:pdf,doc,docx|max:2048', // 2MB limit
        ]);

        // Create the job submission
        $submission = JobSubmission::create([
            'job_vacancy_id' => $jobVacancy->id,
            'full_name' => $validated['full_name'],
            'email' => $validated['email'],
            'summary' => $validated['summary'],
        ]);

        // Handle CV upload
        if ($request->hasFile('cv')) {
            $submission->addMediaFromRequest('cv')
                ->toMediaCollection(JobSubmission::MEDIA_COLLECTION);
        }

        return back()->with('success', 'Your application has been submitted successfully! We will review your application and get back to you soon.');
    }
}
