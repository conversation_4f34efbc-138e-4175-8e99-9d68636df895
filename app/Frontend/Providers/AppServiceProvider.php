<?php

namespace App\Frontend\Providers;

use Illuminate\Routing\UrlGenerator;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot(UrlGenerator $urlGenerator)
    {
        // to force the app using https when proxy or loadbalancer is used
        $urlGenerator->forceScheme('https');
        $this->app['request']->server->set('HTTPS','on');
    }
}
