<?php

namespace App\Frontend\Providers;

use Illuminate\Support\Facades\Blade;
use Illuminate\Support\ServiceProvider;


class ViewServiceProvider extends ServiceProvider
{
    protected static $namespace = 'App\\Frontend\\View\\Components';

    public function boot()
    {
        Blade::if('isRtl', function () {
            return isRtl();
        });

        Blade::componentNamespace(self::$namespace, 'frontend');
        Blade::componentNamespace(self::$namespace, 'web');
    }
}
