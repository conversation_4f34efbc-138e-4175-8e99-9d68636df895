<?php

namespace App\Frontend\Support;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;

class Menu
{
    public string $name = '';

    public string $route = '';

    public array $routeParameters = [];

    public string $url = '';

    public Request $request;

    public array $children = [];

    public ?Closure $isActiveIfUrlCallback = null;

    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    public static function new(): self
    {
        return new self(app(Request::class));
    }

    public static function create(): self
    {
        return self::new();
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function setRoute(string $route, array $parameters = []): self
    {
        $this->route = $route;
        $this->routeParameters = $parameters;

        return $this;
    }

    public function isActive(): bool
    {
        if (! is_null($this->isActiveIfUrlCallback)) {
            $cb = $this->isActiveIfUrlCallback;

            return $cb();
        }

        return $this->request->routeIs($this->route) || $this->request->fullUrlIs($this->url);
    }

    public function isActiveIf(Closure $fn)
    {
        $this->isActiveIfUrlCallback = $fn;

        return $this;
    }

    public function setUrl(string $url)
    {
        $this->url = $url;

        return $this;
    }

    public function getLink()
    {
        return app_route($this->route, $this->routeParameters);
    }

    public function addChildren(Collection | array $items)
    {
        array_push($this->children, ...$items);

        return $this;
    }

    public function hasChildren()
    {
        return count($this->children);
    }
}
