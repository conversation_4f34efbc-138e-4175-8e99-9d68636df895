<?php

namespace App\Frontend\Support;

use Illuminate\Support\Facades\View;

class SeoAttributeManager
{
    protected array $seo = [];

    public function setTitle(string $title): self
    {
        $this->setSeo('title', $title);

        return $this;
    }

    public function setDescription(string $description): self
    {
        $this->setSeo('description', $description);

        return $this;
    }

    public function setOgImage(string $imageFullUrl): self
    {
        $this->setSeo('og_image', $imageFullUrl);

        return $this;
    }

    public function setRelCanonical(string $relCanonical): self
    {
        $this->setSeo('rel_canonical', $relCanonical);

        return $this;
    }

    public function setRelHrefLang($locale, string $url): self
    {
        $hreflang = [$locale => $url];

        if (isset($this->seo['hreflang']) && is_array($this->seo['hreflang'])) {
            $hreflang = array_merge($this->seo['hreflang'], $hreflang);
        }
        $this->setSeo('hreflang', $hreflang);

        return $this;
    }

    private function setSeo(string $key, string|array $value)
    {
        $this->seo[$key] = $value;
        $this->share();
    }

    private function share()
    {
        View::share('seo', $this->seo);
    }
}
