<?php

namespace App\Models;

use Spa<PERSON>\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Gallery extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    protected $fillable = [
        'caption',
    ];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('gallery')
            ->useDisk('gallery')
            ->singleFile();
    }

    // 150px by 150px thumbnail
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(400)
            ->height(400);
    }

    public function thumb(): string
    {
        return $this->getFirstMediaUrl('gallery', 'thumb') ?? '';
    }

    public function photo()
    {
        return $this->getFirstMediaUrl('gallery') ?? '';
    }
}
