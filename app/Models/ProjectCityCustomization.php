<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Translatable\HasTranslations;

class ProjectCityCustomization extends Model
{
    use HasFactory, LogsActivity, HasTranslations;

    protected $fillable = [
        'project_map_id',
        'project_city_id',
        'custom_title',
        'custom_description',
    ];

    protected $translatable = [
        'custom_title',
        'custom_description',
    ];

    protected $casts = [
        'custom_title' => 'array',
        'custom_description' => 'array',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable();
    }

    // Relationships
    public function projectMap(): BelongsTo
    {
        return $this->belongsTo(ProjectMap::class);
    }

    public function projectCity(): BelongsTo
    {
        return $this->belongsTo(ProjectCity::class);
    }

    // Helper methods
    public function hasCustomTitle(): bool
    {
        return !empty($this->getTranslation('custom_title', app()->getLocale()));
    }

    public function hasCustomDescription(): bool
    {
        return !empty($this->getTranslation('custom_description', app()->getLocale()));
    }

    public function getDisplayTitle(string $locale = null): ?string
    {
        $locale = $locale ?? app()->getLocale();
        return $this->getTranslation('custom_title', $locale) ?: 
               $this->projectCity?->getTranslation('name', $locale);
    }

    public function getDisplayDescription(string $locale = null): ?string
    {
        $locale = $locale ?? app()->getLocale();
        return $this->getTranslation('custom_description', $locale) ?: 
               $this->projectCity?->getTranslation('description', $locale);
    }
}
