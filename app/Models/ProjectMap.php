<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class ProjectMap extends Model
{
    use HasFactory, LogsActivity;

    protected $fillable = [
        'project_id',
        'name',
        'type',
        'center_coordinates',
        'default_zoom',
        'bounds',
        'is_active',
    ];

    protected $casts = [
        'center_coordinates' => 'array',
        'bounds' => 'array',
        'is_active' => 'boolean',
        'default_zoom' => 'integer',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable();
    }

    public function customizations() {
        return $this->hasMany(ProjectCityCustomization::class, 'project_map_id');
    }

    // Relationships
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    public function cities(): HasMany
    {
        return $this->hasMany(ProjectCity::class);
    }

    public function markers(): HasMany
    {
        return $this->hasMany(ProjectMarker::class);
    }

    public function cityCustomizations(): HasMany
    {
        return $this->hasMany(ProjectCityCustomization::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeSingleType($query)
    {
        return $query->where('type', 'single');
    }

    public function scopeMultiCityType($query)
    {
        return $query->where('type', 'multi_city');
    }

    // Helper methods
    public function isSingleType(): bool
    {
        return $this->type === 'single';
    }

    public function isMultiCityType(): bool
    {
        return $this->type === 'multi_city';
    }

    public function getActiveCities()
    {
        return $this->cities()->active()->orderBy('sort_order')->get();
    }

    public function getActiveMarkers()
    {
        return $this->markers()->active()->orderBy('sort_order')->get();
    }
}
