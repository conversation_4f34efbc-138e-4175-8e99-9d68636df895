<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Translatable\HasTranslations;

class ProjectMarker extends Model
{
    use HasFactory, LogsActivity, HasTranslations;

    protected $fillable = [
        'project_map_id',
        'project_city_id',
        'title',
        'description',
        'coordinates',
        'icon_type',
        'icon_color',
        'popup_content',
        'link_url',
        'is_active',
        'sort_order',
    ];

    protected $translatable = [
        'title',
        'description',
    ];

    protected $casts = [
        'coordinates' => 'array',
        'popup_content' => 'array',
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable();
    }

    // Relationships
    public function projectMap(): BelongsTo
    {
        return $this->belongsTo(ProjectMap::class);
    }

    public function projectCity(): BelongsTo
    {
        return $this->belongsTo(ProjectCity::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeForCity($query, $cityId)
    {
        return $query->where('project_city_id', $cityId);
    }

    public function scopeGlobal($query)
    {
        return $query->whereNull('project_city_id');
    }

    // Helper methods
    public function getLatitude(): ?float
    {
        return $this->coordinates[0] ?? null;
    }

    public function getLongitude(): ?float
    {
        return $this->coordinates[1] ?? null;
    }

    public function hasLink(): bool
    {
        return !empty($this->link_url);
    }

    public function isGlobalMarker(): bool
    {
        return is_null($this->project_city_id);
    }

    public function isCitySpecific(): bool
    {
        return !is_null($this->project_city_id);
    }
}
