<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class JobSubmission extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    protected $fillable = [
        'job_vacancy_id',
        'full_name',
        'email',
        'summary',
        'reviewed',
    ];

    protected $casts = [
        'reviewed' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Disk name of media for CV uploads
     */
    const MEDIA_DISK = 'job_submissions_media';

    /**
     * Media collection name for CV
     */
    const MEDIA_COLLECTION = 'cv';

    /**
     * Get the job vacancy that this submission belongs to
     */
    public function jobVacancy(): BelongsTo
    {
        return $this->belongsTo(JobVacancy::class);
    }

    /**
     * Get the url of the CV file
     */
    public function cvUrl(): ?string
    {
        return $this->getFirstMediaUrl(static::MEDIA_COLLECTION);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection(static::MEDIA_COLLECTION)
            ->acceptsMimeTypes(['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'])
            ->onlyKeepLatest(1)
            ->useDisk(self::MEDIA_DISK);
    }
}
