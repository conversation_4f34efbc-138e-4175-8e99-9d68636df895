<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

class Slide extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;
    use HasTranslations;

    protected $fillable = [
        'title',
        'description',
        'link',
        'link_text',
    ];

     /**
     * Translatable
     *
     * @var array
     */
    protected $translatable = [
        'title',
        'description',
        'link_text',
    ];


    public function imageUrl(): ?string
    {
        return $this->getFirstMediaUrl('image');
    }

    public function registerMediaCollections(): void
    {
        // Image - Thumbnail
        $this->addMediaCollection('image')
            ->acceptsMimeTypes(['image/jpeg', 'image/png'])
            ->onlyKeepLatest(1)
            ->useDisk('public');
    }
}
