<?php

namespace App\Models;

use App\Shared\Enums\ProjectMediaCollection;
use App\Shared\Enums\PublicationMediaCollection;
use App\Shared\Traits\HasSlug;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Str;
use Laravel\Scout\Searchable;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

class Project extends Model implements HasMedia
{
    use HasFactory;
    use HasTranslations;
    use HasSlug;
    use InteractsWithMedia;
    use LogsActivity;
    use Searchable;

    protected $fillable = [
        'name',
        'slug',
        'excerpt',
        'content',
        'sort',
    ];

    /**
     * Translatable
     *
     * @var array
     */
    protected $translatable = [
        'name',
        'excerpt',
        'content',
    ];

    // Relationships
    public function maps(): HasMany
    {
        return $this->hasMany(ProjectMap::class);
    }

    public function activeMaps(): HasMany
    {
        return $this->hasMany(ProjectMap::class)->active();
    }

    /**
     * Disk name of media for images and attachemnts
     */
    const MEDIA_DISK = 'projects_media';

    /**
     * Get limited name
     *
     * @return string
     */
    public function limitedName(): string
    {
        return Str::of($this->name)
            ->stripTags()
            ->trim('')
            ->limit(64, '...')
            ->toString();
    }

    /**
     * Get a small part of the content like exceprt
     *
     * @return string
     */
    public function limitedExcerpt(int $limit = 100): string
    {
        return Str::of($this->excerpt)
            ->stripTags()
            ->trim('')
            ->limit($limit, '...')
            ->toString();
    }

    /**
     * Url path of the single publication
     *
     * @return string
     */
    public function path(): string
    {
        return app_route('projects.show', ['project' => $this->slug]);
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        return [
            'slug' => $this->slug,
            'name->en' => $this->name,
            'excerpt->en' => $this->excerpt,
            'content->en' => $this->content,
        ];
    }

    public function thumbnail(): ?string
    {
        return $this->getFirstMediaUrl(PublicationMediaCollection::Thumbnail->value);
    }

    public function icon(): ?string
    {
        return $this->getFirstMediaUrl(ProjectMediaCollection::Icon->value);
    }

    public function responsiveThumbnail()
    {
        return Blade::render('{{ $media }}', ['media' => $this->getFirstMedia(ProjectMediaCollection::Thumbnail->value)]);
    }

    protected static function slugifyProperty(): string
    {
        return 'name';
    }

    protected static function slugProperty(): string
    {
        return 'slug';
    }

    public function registerMediaCollections(): void
    {
        // Image - Thumbnail
        $this->addMediaCollection(ProjectMediaCollection::Thumbnail->value)
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp'])
            ->withResponsiveImages()
            ->onlyKeepLatest(1)
            ->useFallbackUrl('https://placehold.jp/30/cccccc/666666/215x280.png?text=Thumbnail')
            ->useDisk(self::MEDIA_DISK);

        // Project Icon (Optional)
        $this->addMediaCollection(ProjectMediaCollection::Icon->value)
            ->acceptsMimeTypes(['image/png', 'image/webp', 'image/svg+xml'])
            ->onlyKeepLatest(1)
            ->useDisk(self::MEDIA_DISK);

        $this->addMediaCollection(ProjectMediaCollection::Gallery->value)
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp'])
            ->onlyKeepLatest(10)
            ->useDisk(self::MEDIA_DISK);
    }

    public function scopeSearch(Builder $builder, ?string $term = '')
    {
        $term = trim(strip_tags($term));

        if (blank($term)) {
            return;
        }

        return $builder->where('name', 'LIKE', "%$term%")
            ->orWhere('excerpt', 'LIKE', "%$term%")
            ->orWhere('content', 'LIKE', "%$term%");
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable();
    }
}
