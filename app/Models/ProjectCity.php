<?php

namespace App\Models;

use App\Shared\Traits\HasSlug;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Translatable\HasTranslations;

class ProjectCity extends Model
{
    use HasFactory, LogsActivity, HasTranslations, HasSlug;

    protected $fillable = [
        'project_map_id',
        'name',
        'slug',
        'center_coordinates',
        'zoom_level',
        'bounds',
        'description',
        'is_active',
        'sort_order',
    ];

    protected $translatable = [
        'name',
        'description',
    ];

    protected $casts = [
        'center_coordinates' => 'array',
        'bounds' => 'array',
        'is_active' => 'boolean',
        'zoom_level' => 'integer',
        'sort_order' => 'integer',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable();
    }

    // Relationships
    public function projectMap(): BelongsTo
    {
        return $this->belongsTo(ProjectMap::class);
    }

    public function markers(): HasMany
    {
        return $this->hasMany(ProjectMarker::class);
    }

    public function customizations(): HasMany
    {
        return $this->hasMany(ProjectCityCustomization::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeStandalone($query)
    {
        return $query->whereNull('project_map_id');
    }

    public function scopeProjectSpecific($query)
    {
        return $query->whereNotNull('project_map_id');
    }

    // Slug trait methods
    protected static function slugifyProperty(): string
    {
        return 'name';
    }

    protected static function slugProperty(): string
    {
        return 'slug';
    }

    // Helper methods
    public function getActiveMarkers()
    {
        return $this->markers()->active()->orderBy('sort_order')->get();
    }

    public function hasMarkers(): bool
    {
        return $this->markers()->exists();
    }

    public function isStandalone(): bool
    {
        return is_null($this->project_map_id);
    }

    public function isProjectSpecific(): bool
    {
        return !is_null($this->project_map_id);
    }
}
