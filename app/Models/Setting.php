<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class Setting extends Model
{
    use HasFactory;
    use LogsActivity;

    const ADDRESS = 'address';

    const EMAIL = 'email';

    const INSTAGRAM = 'instagram';

    const TWITTER = 'twitter_x';

    const FACEBOOK = 'facebook';

    const YOUTUBE = 'youtube';
    
    const THREADS = 'threads';

    const SHOW_NEWS_PAGE = 'show_news_page';

    const SHOW_CAREERS_PAGE = 'show_careers_page';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'key',
        'value',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable();
    }

    public static function cacheKey(): string
    {
        return 'settigns';
    }
}
