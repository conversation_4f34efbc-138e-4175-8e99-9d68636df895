<?php

namespace App\Models;

use App\Shared\Enums\PublicationMediaCollection;
use App\Shared\Traits\HasSlug;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Str;
use Laravel\Scout\Searchable;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

class Publication extends Model implements HasMedia
{
    use HasFactory;
    use HasTranslations;
    use HasSlug;
    use InteractsWithMedia;
    use LogsActivity;
    use Searchable;

    protected $fillable = [
        'title',
        'slug',
        'excerpt',
        'content',
        'program_id',
        'expert_id',
    ];

    /**
     * Translatable
     *
     * @var array
     */
    protected $translatable = [
        'title',
        'excerpt',
        'content',
    ];

    /**
     * Disk name of media for images and attachemnts
     */
    const MEDIA_DISK = 'publications_media';

    /**
     * Get limited title
     *
     * @return string
     */
    public function limitedTitle(): string
    {
        return Str::of($this->title)
            ->stripTags()
            ->trim('')
            ->limit(64, '...')
            ->toString();
    }

    /**
     * Get a small part of the content like exceprt
     *
     * @return string
     */
    public function limitedExcerpt(): string
    {
        return Str::of($this->excerpt)
            ->stripTags()
            ->trim('')
            ->limit(100, '...')
            ->toString();
    }

    /**
     * Url path of the single publication
     *
     * @return string
     */
    public function path(): string
    {
        return app_route('publications.show', ['publication' => $this->slug]);
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        return [
            'slug' => $this->slug,
            'title->en' => $this->title,
            'title->ar' => $this->title,
            'title->ku' => $this->title,
            'excerpt->en' => $this->excerpt,
            'excerpt->ar' => $this->excerpt,
            'excerpt->ku' => $this->excerpt,
            'content->ar' => $this->content,
            'content->en' => $this->content,
            'content->ku' => $this->content,
        ];
    }

    public function thumbnail(): ?string
    {
        return $this->getFirstMediaUrl(PublicationMediaCollection::Thumbnail->value);
    }

    public function responsiveThumbnail()
    {
        return Blade::render('{{ $media }}', ['media' => $this->getFirstMedia(PublicationMediaCollection::Thumbnail->value)]);
    }

    protected static function slugifyProperty(): string
    {
        return 'title';
    }

    protected static function slugProperty(): string
    {
        return 'slug';
    }

    public function registerMediaCollections(): void
    {
        // Image - Thumbnail
        $this->addMediaCollection(PublicationMediaCollection::Thumbnail->value)
            ->acceptsMimeTypes(['image/jpeg', 'image/png'])
            ->withResponsiveImages()
            ->onlyKeepLatest(1)
            ->useFallbackUrl('https://placehold.jp/30/cccccc/666666/215x280.png?text=Thumbnail')
            ->useDisk(self::MEDIA_DISK);

        // PDF attachments
        // Limited to three PDF attachments (1 required others are optional) (English, Kurdish, Arabic)
        $this->addMediaCollection(PublicationMediaCollection::Attachments->value)
            ->acceptsMimeTypes(['application/pdf'])
            ->onlyKeepLatest(3)
            ->useDisk(self::MEDIA_DISK);
    }

    public function scopeSearch(Builder $builder, ?string $term = '')
    {
        $term = trim(strip_tags($term));

        if (blank($term)) {
            return;
        }

        return $builder->where('title', 'LIKE', "%$term%")
            ->orWhere('excerpt', 'LIKE', "%$term%")
            ->orWhere('content', 'LIKE', "%$term%");
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable();
    }
}
