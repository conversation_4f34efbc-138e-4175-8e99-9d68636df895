<?php

namespace App\Models;

use <PERSON><PERSON>\Scout\Searchable;
use App\Shared\Traits\HasSlug;
use Spatie\Activitylog\LogOptions;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Translatable\HasTranslations;

class JobVacancy extends Model
{
    use HasFactory;
    use HasSlug;
    use LogsActivity;
    use Searchable;
    use HasTranslations;


    protected $fillable = [
        'title',
        'slug',
        'description',
        'location',
        'closed',
    ];

    protected $translatable = [
        'title',
        'description',
        'location',
    ];

    protected $casts = [
        'closed' => 'boolean',
    ];

    /**
     * Url path of the single publication
     *
     * @return string
     */
    public function path(): string
    {
        return app_route('careers.show', ['job' => $this->slug]);
    }

    protected static function slugifyProperty(): string
    {
        return 'title';
    }

    protected static function slugProperty(): string
    {
        return 'slug';
    }

    /**
     * @param Builder $builder 
     * @return Builder 
     */
    public function scopeOpen(Builder $builder)
    {
        return $builder->where('closed', false);
    }

    /**
     * Get the submissions for this job vacancy
     */
    public function submissions(): HasMany
    {
        return $this->hasMany(JobSubmission::class);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable();
    }
}
