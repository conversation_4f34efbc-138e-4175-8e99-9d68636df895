<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class TeamMember extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'department',
        'name',
        'role',
        'description',
        'email',
        'phone',
        'bio',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Disk name of media for images and attachemnts
     */
    const MEDIA_DISK = 'team_media';

    /**
     * Media collection name for profile photo
     */
    const MEDIA_COLLECTION = 'photo';

    /**
     * Get the url of the profile photo
     *
     * @return string
     */
    public function photoUrl(): ?string
    {
        return $this->getFirstMediaUrl(static::MEDIA_COLLECTION);
    }

    public function registerMediaCollections(): void
    {
        // Image - Thumbnail
        $this->addMediaCollection(static::MEDIA_COLLECTION)
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp'])
            ->onlyKeepLatest(1)
            ->useDisk(self::MEDIA_DISK);
    }
}
