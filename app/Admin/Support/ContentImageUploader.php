<?php

namespace App\Admin\Support;

use Illuminate\Http\UploadedFile;

class ContentImageUploader
{
    const MODELS = [
        'publications',
        'events',
        'experts',
        'blog',
    ];

    public function upload(string $path, UploadedFile $image): string|false
    {
        return $image->storePubliclyAs(
            $path,
            $image->getClientOriginalName(),
            [
                'disk' => 'public',
            ]
        );
    }
}
