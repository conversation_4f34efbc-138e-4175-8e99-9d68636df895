<?php

namespace App\Admin\Support;

use Illuminate\Support\Collection;

enum Permissions: string
{
        // Projects
    case CreateProjects = 'create projects';
    case EditProjects = 'edit projects';
    case DeleteProjects = 'delete projects';
    case MenuProjects = 'menu projects';


        // News
    case CreateNews = 'create news';
    case EditNews = 'edit news';
    case DeleteNews = 'delete news';
    case MenuNews = 'menu news';


        // Job Vanacies
    case CreateVacancy = 'create vacancy';
    case EditVacancy = 'edit vacancy';
    case DeleteVacancy = 'delete vacancy';
    case MenuVacancy = 'menu vacancy';

        //Settings
    case MenuSetting = 'menu setting';
    case EditSetting = 'edit setting';


        // Gallery
    case CreateGallery = 'create gallery';
    case EditGallery = 'edit gallery';
    case DeleteGallery = 'delete gallery';
    case MenuGallery = 'menu gallery';


    public static function all(): Collection
    {
        return collect(self::cases())->map(fn(self $case) => $case->value);
    }
}
