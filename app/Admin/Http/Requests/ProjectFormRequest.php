<?php

namespace App\Admin\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProjectFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'name.en' => ['required', 'max:500'],
            'content.en' => ['required'],
            'excerpt.en' => ['required', 'max:300'],
            'excerpt.*' => ['nullable', 'max:300'],
            'name.*' => ['nullable', 'max:500'], // for other languages
            'content.*' => ['nullable'], // for other languages
            'sort' => ['nullable', 'integer', 'min:0'],
            'thumbnail' => ['required', 'image', 'max:12000'],
            'icon' => ['nullable', 'file', 'mimetypes:image/png,image/webp,image/svg+xml', 'max:5120'],
            'gallery' => ['array'],
            'gallery.*.file' => ['required', 'file', 'mimetypes:image/jpeg,image/png,image/webp', 'max:548'],
            'gallery.*.description_en' => ['nullable', 'string', 'max:500'],
            'gallery.*.description_ku' => ['nullable', 'string', 'max:500'],
            'gallery.*.description_ar' => ['nullable', 'string', 'max:500'],
        ];
    }
}
