<?php

namespace App\Admin\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class JobVacancyFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'title.en' => ['required', 'string', 'max:255'],
            'title.*' => ['nullable', 'string', 'max:255'],
            'description.en' => ['required', 'string'],
            'description.*' => ['nullable', 'string'],
            'location.en' => ['nullable', 'string', 'max:255'],
            'location.*' => ['nullable', 'string', 'max:255'],
            'closed' => ['nullable', 'boolean'],
        ];
    }
}
