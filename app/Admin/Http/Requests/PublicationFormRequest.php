<?php

namespace App\Admin\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PublicationFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'title.en' => ['required', 'max:500'],
            'content.en' => ['required'],
            'excerpt.en' => ['required', 'max:300'],
            'excerpt.*' => ['nullable', 'max:300'],
            'title.*' => ['nullable', 'max:500'], // for other languages
            'content.*' => ['nullable'], // for other languages
            'thumbnail' => ['required', 'image', 'max:12000'],
            'attachments.*' => ['nullable', 'file', 'mimetypes:application/pdf', 'max:12000'],
        ];
    }
}
