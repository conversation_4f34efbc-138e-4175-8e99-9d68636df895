<?php

namespace App\Admin\Http\Requests;

use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;

class EventFormRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'title.en' => ['required', 'max:500'],
            'content.en' => ['required'],
            'excerpt.en' => ['required', 'max:300'],
            'excerpt.*' => ['nullable', 'max:300'],
            'location.*' => ['nullable', 'max:255'],
            'title.*' => ['nullable', 'max:500'], // for other languages
            'content.*' => ['nullable'], // for other languages
            'thumbnail' => ['required', 'image', 'max:12000'],
            'event_date' => ['required', 'date'],
            'attachments.*' => ['nullable', 'file', 'mimetypes:application/pdf', 'max:12000'],
        ];
    }

    public function prepareForValidation()
    {
        $this->merge([
            'event_date' => $this->event_date ? Carbon::parse($this->event_date)->format('Y-m-d') : null,
        ]);
    }
}
