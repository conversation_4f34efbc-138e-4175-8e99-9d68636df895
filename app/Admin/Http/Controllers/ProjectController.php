<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Http\Requests\ProjectFormRequest;
use App\Admin\Http\Requests\ProjectUpdateFormRequest;
use App\Admin\Http\Resources\ProjectResource;
use App\Admin\Support\Permissions;
use App\Http\Controllers\Controller;
use App\Models\Project;
use App\Shared\Enums\ProjectMediaCollection;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class ProjectController extends Controller
{
    public function index(Request $request)
    {
        return Inertia::render('Projects/Index', [
            'projects' => fn () => ProjectResource::collection(
                Project::with(['media'])
                    ->oldest('sort')
                    ->paginate($request->perPage ?? 15)
            ),
            'filters' => $request->all(['search']),
        ]);
    }

    public function create()
    {
        $this->authorize(Permissions::CreateProjects->value);

        return Inertia::render('Projects/Create', [
            'availableCities' => \App\Admin\Http\Resources\CityResource::collection(
                \App\Models\ProjectCity::standalone()->active()->get()
            )->toArray(request()),
        ]);
    }

    public function store(ProjectFormRequest $request)
    {
        $this->authorize(Permissions::CreateProjects->value);

        /** @var \App\Models\Project */
        $project = Project::create($request->validated());

        if ($project) {
            // Upload Thumbnail
            $project->addMediaFromRequest('thumbnail')
                ->toMediaCollection(ProjectMediaCollection::Thumbnail->value);

            // Upload icon if provided
            if ($request->hasFile('icon')) {
                $project->addMediaFromRequest('icon')
                    ->toMediaCollection(ProjectMediaCollection::Icon->value);
            }

            // Upload attachments
            collect($request->file('gallery'))
                ->each(
                    fn ($attachment, $lang) => $project->addMedia($attachment)
                        ->toMediaCollection(ProjectMediaCollection::Gallery->value)
                );
        }

        session()->flash('success', 'Project has been created');

        return redirect()->route('admin.projects.edit', $project);
    }

    public function show(Project $project)
    {
        $this->authorize(Permissions::MenuProjects->value);

        $project->load(['maps.customizations.projectCity', 'maps.markers']);

        return Inertia::render('Projects/Show', [
            'project' => $project,
            'thumbnail' => $project->getFirstMedia(ProjectMediaCollection::Thumbnail->value),
            'icon' => $project->getFirstMedia(ProjectMediaCollection::Icon->value),
            'gallery' => $project->getMedia(ProjectMediaCollection::Gallery->value),
            'maps' => $project->maps->map(function ($map) {
                return [
                    'id' => $map->id,
                    'name' => $map->name,
                    'type' => $map->type,
                    'description' => $map->description,
                    'is_active' => $map->is_active,
                    'markers_count' => $map->markers->count(),
                    'cities_count' => $map->cities->count(),
                    'created_at' => $map->created_at->format('M d, Y'),
                ];
            }),
        ]);
    }

    public function edit(Project $project)
    {
        $this->authorize(Permissions::EditProjects->value);

        $project->load(['maps.customizations.projectCity', 'maps.markers']);

        return Inertia::render('Projects/Edit', [
            'project' => $project,
            'thumbnail' => $project->getFirstMedia(ProjectMediaCollection::Thumbnail->value),
            'icon' => $project->getFirstMedia(ProjectMediaCollection::Icon->value),
            'gallery' => $project->getMedia(ProjectMediaCollection::Gallery->value),
            'projectMaps' => $project->maps->map(function ($map) {
                return [
                    'id' => $map->id,
                    'name' => $map->name,
                    'type' => $map->type,
                    'description' => $map->description,
                    'is_active' => $map->is_active,
                    'markers_count' => $map->markers->count(),
                    'cities_count' => $map->cities->count(),
                    'created_at' => $map->created_at->format('M d, Y'),
                ];
            }),
            'availableCities' => \App\Admin\Http\Resources\CityResource::collection(
                \App\Models\ProjectCity::standalone()->active()->get()
            )->toArray(request()),
        ]);
    }

    public function update(ProjectUpdateFormRequest $request, Project $project)
    {
        $this->authorize(Permissions::EditProjects->value);

        $project->update($request->validated());

        if ($request->hasFile('thumbnail') && $request->shouldUploadThumbnail()) {
            $project->addMediaFromRequest('thumbnail')
                ->toMediaCollection(ProjectMediaCollection::Thumbnail->value);
        }

        if ($request->hasFile('icon') && $request->shouldUploadIcon()) {
            $project->addMediaFromRequest('icon')
                ->toMediaCollection(ProjectMediaCollection::Icon->value);
        }

        if ($request->hasFile('gallery')) {
            collect($request->file('gallery'))
                ->each(
                    fn ($attachment, $lang) => $project->addMedia($attachment)
                        ->toMediaCollection(ProjectMediaCollection::Gallery->value)
                );
        }

        session()->flash('success', 'Project has been updated');

        return redirect()->route('admin.projects.index');
    }

    public function destroyMedia(Media $media)
    {
        $media->delete();

        session()->flash('success', 'Attachment has been deleted');

        return back();
    }

    public function destroy(Project $project)
    {
        $this->authorize(Permissions::DeleteProjects->value);
        $project->delete();

        session()->flash('success', 'Project has been deleted');

        return back();
    }
}
