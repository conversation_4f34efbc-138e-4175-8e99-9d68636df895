<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Http\Resources\JobSubmissionResource;
use App\Admin\Support\Permissions;
use App\Http\Controllers\Controller;
use App\Models\JobSubmission;
use App\Models\JobVacancy;
use Illuminate\Http\Request;
use Inertia\Inertia;

class JobSubmissionController extends Controller
{
    public function index(Request $request, JobVacancy $jobVacancy)
    {
        $this->authorize(Permissions::MenuVacancy->value);

        return Inertia::render('JobSubmissions/Index', [
            'jobVacancy' => $jobVacancy,
            'submissions' => fn () => JobSubmissionResource::collection(
                $jobVacancy->submissions()
                    ->when($request->search, fn ($query) => 
                        $query->where('full_name', 'like', '%' . $request->search . '%')
                            ->orWhere('email', 'like', '%' . $request->search . '%')
                    )
                    ->latest('id')
                    ->paginate($request->perPage ?? 15)
            ),
            'filters' => $request->all(['search']),
        ]);
    }

    public function show(JobVacancy $jobVacancy, JobSubmission $submission)
    {
        $this->authorize(Permissions::MenuVacancy->value);

        // Ensure the submission belongs to this job vacancy
        if ($submission->job_vacancy_id !== $jobVacancy->id) {
            abort(404);
        }

        return Inertia::render('JobSubmissions/Show', [
            'jobVacancy' => $jobVacancy,
            'submission' => new JobSubmissionResource($submission),
            'cvFile' => $submission->getFirstMedia(JobSubmission::MEDIA_COLLECTION),
        ]);
    }

    public function markAsReviewed(Request $request, JobVacancy $jobVacancy, JobSubmission $submission)
    {
        $this->authorize(Permissions::EditVacancy->value);

        // Ensure the submission belongs to this job vacancy
        if ($submission->job_vacancy_id !== $jobVacancy->id) {
            abort(404);
        }

        $submission->update(['reviewed' => !$submission->reviewed]);

        $status = $submission->reviewed ? 'reviewed' : 'unreviewed';
        session()->flash('success', "Submission has been marked as {$status}.");

        return back();
    }

    public function destroy(JobVacancy $jobVacancy, JobSubmission $submission)
    {
        $this->authorize(Permissions::DeleteVacancy->value);

        // Ensure the submission belongs to this job vacancy
        if ($submission->job_vacancy_id !== $jobVacancy->id) {
            abort(404);
        }

        $submission->delete();

        session()->flash('success', 'Submission has been successfully deleted.');

        return redirect()->route('admin.job-vacancies.submissions.index', $jobVacancy);
    }
}
