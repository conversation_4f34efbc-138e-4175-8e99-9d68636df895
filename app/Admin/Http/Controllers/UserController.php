<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Http\Requests\UserStoreFormRequest;
use App\Admin\Http\Requests\UserUpdateFormRequest;
use App\Admin\Http\Resources\UserResource;
use App\Admin\Support\Roles;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Inertia\Inertia;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth.superadmin');
    }

    public function index()
    {
        return Inertia::render('Users/Index', [
            'users' => UserResource::collection(
                User::with(['roles'])
                    ->latest('id')
                    ->get()
            ),
        ]);
    }

    public function create()
    {
        return Inertia::render('Users/Create', [
            'roles' => Role::all(['id', 'name', 'description']),
        ]);
    }

    public function store(UserStoreFormRequest $request)
    {
        /** @var User */
        $user = User::create(array_merge(
            $request->validated(),
            ['password' => Hash::make($request->password)],
        ));

        // Assign role - use provided role or default to Admin
        $roleId = $request->role_id ?? Role::where('name', Roles::Admin->value)->first()?->id;
        if ($roleId) {
            $user->assignRole($roleId);
        }

        session()->flash('success', 'User has been successfully created.');

        return redirect()->route('admin.users.index');
    }

    public function edit(User $user)
    {
        if (current_user()->isSuperAdmin() && current_user()->id != $user->id) {
            session()->flash('error', 'You cannot edit another super admin\'s account.');
            return redirect()->back();
        }

        return Inertia::render('Users/Edit', [
            'user' => new UserResource($user->load('roles')),
            'roles' => Role::all(['id', 'name', 'description']),
        ]);
    }

    public function update(UserUpdateFormRequest $request, User $user)
    {
        if (current_user()->isSuperAdmin() && current_user()->id != $user->id) {
            session()->flash('error', 'You cannot edit another super admin\'s account.');
            return redirect()->back();
        }
        
        $input = $request->validated();
        unset($input['password']);

        if ($request->filled('password')) {
            $input = array_merge(
                $input,
                ['password' => Hash::make($request->password)]
            );
        }

        $user->update($input);

        // Update role if provided
        if ($request->has('role_id') && $request->role_id) {
            $user->syncRoles([$request->role_id]);
        }

        session()->flash('success', 'User has been successfully updated.');

        return redirect()->back();
    }

    public function destroy(User $user)
    {
        $user->delete();

        session()->flash('success', 'User has been successfully deleted.');

        return redirect()->back();
    }
}
