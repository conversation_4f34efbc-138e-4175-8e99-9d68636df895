<?php

namespace App\Admin\Http\Controllers;

use Inertia\Inertia;
use App\Models\Gallery;
use Illuminate\Http\Request;
use App\Admin\Support\Permissions;
use App\Http\Controllers\Controller;
use App\Admin\Http\Resources\GalleryResource;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class GalleryController extends Controller
{
    public function index(Request $request)
    {
        return Inertia::render('Gallery/Index', [
            'galleries' => fn () => GalleryResource::collection(
                Gallery::with(['media'])
                    ->latest('id')
                    ->paginate($request->perPage ?? 15)
            ),
            'filters' => $request->all(['search']),
        ]);
    }

    public function create()
    {
        return Inertia::modal('Gallery/Create')
            ->baseRoute('admin.galleries.index');
    }

    public function store(Request $request)
    {
        $this->authorize(Permissions::CreateGallery->value);
        $data = $request->validate([
            'photo' => ['required', 'image', 'max:2048'], //2MB
            'caption' => ['nullable', 'string', 'max:1000'],
        ]);

        /** @var \App\Models\Gallery */
        $gallery = Gallery::create($data);

        if ($gallery) {
            $gallery->addMediaFromRequest('photo')->toMediaCollection('gallery');
        }

        session()->flash('success', 'Gallery has been created');

        return redirect()->route('admin.galleries.index');
    }

    public function edit(Gallery $gallery)
    {
        $this->authorize(Permissions::EditGallery->value);

        return Inertia::modal('Gallery/Edit',  [
            'gallery' => $gallery,
            'photo' => $gallery->photo(),
        ])->baseRoute('admin.galleries.index');
    }

    public function update(Request $request, Gallery $gallery)
    {
        $this->authorize(Permissions::EditGallery->value);
        
        $data = $request->validate([
            'photo' => ['nullable', 'image', 'max:2048'], //2MB
            'caption' => ['nullable', 'string', 'max:1000'],
        ]);

        $gallery->update($data);

        if ($request->hasFile('photo')) {
            $gallery->addMediaFromRequest('photo')->toMediaCollection('gallery');
        }

        session()->flash('success', 'gallery has been updated');

        return redirect()->route('admin.galleries.index');
    }

    public function destroy(Gallery $gallery)
    {
        $this->authorize(Permissions::DeleteGallery->value);
        $gallery->delete();

        session()->flash('success', 'Gallery has been deleted');

        return back();
    }
}
