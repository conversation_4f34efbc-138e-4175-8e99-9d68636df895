<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Support\Roles;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Inertia\Inertia;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RolePermissionController extends Controller
{
    public function index(Role $role)
    {
        return Inertia::render('Role/Permissions', [
            'permissions' => Permission::get(),
            'role' => $role->load('permissions'),
        ]);
    }

    public function update(Request $request, Role $role)
    {
        if ($role->hasPermissionTo($permissionId = (int) $request->permission_id)) {
            $role->revokePermissionTo($permissionId);
        } else {
            $role->givePermissionTo($permissionId);
        }
        Cache::forget(Roles::Permission_Cache_key);

        return redirect()->back();
    }
}
