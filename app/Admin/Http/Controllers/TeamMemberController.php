<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Http\Resources\TeamMemberResource;
use App\Admin\Http\Requests\TeamMemberFormRequest;
use App\Admin\Support\Permissions;
use App\Http\Controllers\Controller;
use App\Models\TeamMember;
use Illuminate\Http\Request;
use Inertia\Inertia;

class TeamMemberController extends Controller
{
    public function index(Request $request)
    {
        $this->authorize(Permissions::MenuTeamMember->value);

        return Inertia::render('TeamMembers/Index', [
            'teamMembers' => fn () => TeamMemberResource::collection(
                TeamMember::when($request->search, fn ($query) => 
                    $query->where(function($q) use ($request) {
                        $search = '%' . $request->search . '%';
                        $q->whereRaw("JSON_UNQUOTE(JSON_EXTRACT(name, '$.en')) LIKE ?", [$search])
                          ->orWhereRaw("JSON_UNQUOTE(JSON_EXTRACT(name, '$.ku')) LIKE ?", [$search])
                          ->orWhereRaw("JSON_UNQUOTE(JSON_EXTRACT(name, '$.ar')) LIKE ?", [$search])
                          ->orWhereRaw("JSON_UNQUOTE(JSON_EXTRACT(department, '$.en')) LIKE ?", [$search])
                          ->orWhereRaw("JSON_UNQUOTE(JSON_EXTRACT(department, '$.ku')) LIKE ?", [$search])
                          ->orWhereRaw("JSON_UNQUOTE(JSON_EXTRACT(department, '$.ar')) LIKE ?", [$search])
                          ->orWhereRaw("JSON_UNQUOTE(JSON_EXTRACT(role, '$.en')) LIKE ?", [$search])
                          ->orWhereRaw("JSON_UNQUOTE(JSON_EXTRACT(role, '$.ku')) LIKE ?", [$search])
                          ->orWhereRaw("JSON_UNQUOTE(JSON_EXTRACT(role, '$.ar')) LIKE ?", [$search]);
                    })
                )
                ->latest('id')
                ->paginate($request->perPage ?? 15)
            ),
            'filters' => $request->all(['search']),
        ]);
    }

    public function create()
    {
        $this->authorize(Permissions::CreateTeamMember->value);

        return Inertia::render('TeamMembers/Create');
    }

    public function store(TeamMemberFormRequest $request)
    {
        $this->authorize(Permissions::CreateTeamMember->value);

        $validated = $request->validated();

        $teamMember = TeamMember::create($validated);

        if ($request->hasFile('photo')) {
            $teamMember->addMediaFromRequest('photo')
                ->toMediaCollection(TeamMember::MEDIA_COLLECTION);
        }

        session()->flash('success', 'Team member has been successfully created.');

        return redirect()->route('admin.team-members.index');
    }

    public function edit(TeamMember $teamMember)
    {
        $this->authorize(Permissions::EditTeamMember->value);

        return Inertia::render('TeamMembers/Edit', [
            'teamMember' => $teamMember,
            'photo' => $teamMember->getFirstMedia(TeamMember::MEDIA_COLLECTION),
        ]);
    }

    public function update(TeamMemberFormRequest $request, TeamMember $teamMember)
    {
        $this->authorize(Permissions::EditTeamMember->value);

        $validated = $request->validated();

        $teamMember->update($validated);

        if ($request->hasFile('photo') && $request->boolean('photoHasChanged')) {
            $teamMember->addMediaFromRequest('photo')
                ->toMediaCollection(TeamMember::MEDIA_COLLECTION);
        }

        session()->flash('success', 'Team member has been successfully updated.');

        return redirect()->route('admin.team-members.index');
    }

    public function destroy(TeamMember $teamMember)
    {
        $this->authorize(Permissions::DeleteTeamMember->value);

        $teamMember->delete();

        session()->flash('success', 'Team member has been successfully deleted.');

        return redirect()->route('admin.team-members.index');
    }
}
