<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Http\Resources\TeamMemberResource;
use App\Admin\Support\Permissions;
use App\Http\Controllers\Controller;
use App\Models\TeamMember;
use Illuminate\Http\Request;
use Inertia\Inertia;

class TeamMemberController extends Controller
{
    public function index(Request $request)
    {
        $this->authorize(Permissions::MenuTeamMember->value);

        return Inertia::render('TeamMembers/Index', [
            'teamMembers' => fn () => TeamMemberResource::collection(
                TeamMember::when($request->search, fn ($query) => 
                    $query->where('name', 'like', '%' . $request->search . '%')
                        ->orWhere('department', 'like', '%' . $request->search . '%')
                        ->orWhere('role', 'like', '%' . $request->search . '%')
                )
                ->latest('id')
                ->paginate($request->perPage ?? 15)
            ),
            'filters' => $request->all(['search']),
        ]);
    }

    public function create()
    {
        $this->authorize(Permissions::CreateTeamMember->value);

        return Inertia::render('TeamMembers/Create');
    }

    public function store(Request $request)
    {
        $this->authorize(Permissions::CreateTeamMember->value);


        $validated = $request->validate([
            'department' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'role' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:255',
            'photo' => 'nullable|image|mimes:jpeg,png,webp|max:2048',
        ]);

        $teamMember = TeamMember::create($validated);

        if ($request->hasFile('photo')) {
            $teamMember->addMediaFromRequest('photo')
                ->toMediaCollection(TeamMember::MEDIA_COLLECTION);
        }

        session()->flash('success', 'Team member has been successfully created.');

        return redirect()->route('admin.team-members.index');
    }

    public function edit(TeamMember $teamMember)
    {
        $this->authorize(Permissions::EditTeamMember->value);

        return Inertia::render('TeamMembers/Edit', [
            'teamMember' => $teamMember,
            'photo' => $teamMember->getFirstMedia(TeamMember::MEDIA_COLLECTION),
        ]);
    }

    public function update(Request $request, TeamMember $teamMember)
    {
        $this->authorize(Permissions::EditTeamMember->value);

        $validated = $request->validate([
            'department' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'role' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:255',
            'photo' => 'nullable|image|mimes:jpeg,png,webp|max:2048',
            'photoHasChanged' => 'boolean',
        ]);

        $teamMember->update($validated);

        if ($request->hasFile('photo') && $request->boolean('photoHasChanged')) {
            $teamMember->addMediaFromRequest('photo')
                ->toMediaCollection(TeamMember::MEDIA_COLLECTION);
        }

        session()->flash('success', 'Team member has been successfully updated.');

        return redirect()->route('admin.team-members.index');
    }

    public function destroy(TeamMember $teamMember)
    {
        $this->authorize(Permissions::DeleteTeamMember->value);

        $teamMember->delete();

        session()->flash('success', 'Team member has been successfully deleted.');

        return redirect()->route('admin.team-members.index');
    }
}
