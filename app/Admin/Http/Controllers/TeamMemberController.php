<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Http\Resources\TeamMemberResource;
use App\Admin\Support\Permissions;
use App\Http\Controllers\Controller;
use App\Models\TeamMember;
use Illuminate\Http\Request;
use Inertia\Inertia;

class TeamMemberController extends Controller
{
    public function index(Request $request)
    {
        $this->authorize(Permissions::MenuTeamMember->value);

        return Inertia::render('TeamMembers/Index', [
            'teamMembers' => fn () => TeamMemberResource::collection(
                TeamMember::when($request->search, fn ($query) => 
                    $query->where('name', 'like', '%' . $request->search . '%')
                        ->orWhere('department', 'like', '%' . $request->search . '%')
                        ->orWhere('role', 'like', '%' . $request->search . '%')
                )
                ->latest('id')
                ->paginate($request->perPage ?? 15)
            ),
            'filters' => $request->all(['search']),
        ]);
    }

    public function create()
    {
        $this->authorize(Permissions::CreateTeamMember->value);

        return Inertia::render('TeamMembers/Create');
    }

    public function store(Request $request)
    {
        $this->authorize(Permissions::CreateTeamMember->value);

        $validated = $request->validate([
            'department' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'role' => 'required|string|max:255',
            'description' => 'nullable|string',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:255',
            'bio' => 'nullable|string',
        ]);

        TeamMember::create($validated);

        session()->flash('success', 'Team member has been successfully created.');

        return redirect()->route('admin.team-members.index');
    }

    public function edit(TeamMember $teamMember)
    {
        $this->authorize(Permissions::EditTeamMember->value);

        return Inertia::render('TeamMembers/Edit', [
            'teamMember' => new TeamMemberResource($teamMember),
        ]);
    }

    public function update(Request $request, TeamMember $teamMember)
    {
        $this->authorize(Permissions::EditTeamMember->value);

        $validated = $request->validate([
            'department' => 'required|string|max:255',
            'name' => 'required|string|max:255',
            'role' => 'required|string|max:255',
            'description' => 'nullable|string',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:255',
            'bio' => 'nullable|string',
        ]);

        $teamMember->update($validated);

        session()->flash('success', 'Team member has been successfully updated.');

        return redirect()->route('admin.team-members.index');
    }

    public function destroy(TeamMember $teamMember)
    {
        $this->authorize(Permissions::DeleteTeamMember->value);

        $teamMember->delete();

        session()->flash('success', 'Team member has been successfully deleted.');

        return redirect()->route('admin.team-members.index');
    }
}
