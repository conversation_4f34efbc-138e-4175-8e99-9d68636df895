<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Http\Resources\ProjectMapResource;
use App\Admin\Support\Permissions;
use App\Http\Controllers\Controller;
use App\Models\Project;
use App\Models\ProjectMap;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class ProjectMapController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $this->authorize(Permissions::MenuProjects->value);

        return Inertia::render('ProjectMaps/Index', [
            'maps' => fn () => ProjectMapResource::collection(
                ProjectMap::with(['project', 'cities', 'markers'])
                    ->latest('id')
                    ->paginate($request->perPage ?? 15)
            ),
            'filters' => $request->all(['search', 'project_id']),
            'projects' => Project::select('id', 'name')->get(),
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create(Request $request)
    {
        $this->authorize(Permissions::CreateProjects->value);

        $project = null;
        if ($request->has('project_id')) {
            $project = Project::find($request->project_id);
        }

        return Inertia::render('ProjectMaps/Create', [
            'projects' => Project::select('id', 'name')->get(),
            'selectedProject' => $project,
            'availableCities' => \App\Admin\Http\Resources\CityResource::collection(
                \App\Models\ProjectCity::standalone()->active()->get()
            )->toArray(request()),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $this->authorize(Permissions::CreateProjects->value);

        $data = $request->validate([
            'project_id' => ['required', 'exists:projects,id'],
            'name' => ['required', 'string', 'max:255'],
            'type' => ['required', 'in:single,multi_city'],
            'description' => ['nullable', 'string'],
            'center_coordinates' => ['nullable', 'array', 'size:2'],
            'center_coordinates.*' => ['numeric'],
            'zoom_level' => ['integer', 'min:1', 'max:20'],
            'bounds' => ['nullable', 'array'],
            'is_active' => ['boolean'],
            'markers' => ['nullable', 'array'],
            'markers.*.title' => ['required', 'array'],
            'markers.*.title.en' => ['required', 'string', 'max:255'],
            'markers.*.title.ku' => ['nullable', 'string', 'max:255'],
            'markers.*.description' => ['nullable', 'array'],
            'markers.*.description.en' => ['nullable', 'string'],
            'markers.*.description.ku' => ['nullable', 'string'],
            'markers.*.coordinates' => ['required', 'array'],
            'markers.*.coordinates.lat' => ['required', 'numeric', 'between:-90,90'],
            'markers.*.coordinates.lng' => ['required', 'numeric', 'between:-180,180'],
            'markers.*.icon_type' => ['nullable', 'string', 'in:default,bim,atm'],
            'markers.*.is_active' => ['boolean'],
            'markers.*.sort_order' => ['nullable', 'integer', 'min:0'],
            'selected_city_ids' => ['nullable', 'array'],
            'selected_city_ids.*' => ['exists:project_cities,id'],
            'city_titles' => ['nullable', 'array'],
            'city_titles.*' => ['array'],
            'city_titles.*.en' => ['nullable', 'string'],
            'city_titles.*.ku' => ['nullable', 'string'],
            'city_descriptions' => ['nullable', 'array'],
            'city_descriptions.*' => ['array'],
            'city_descriptions.*.en' => ['nullable', 'string'],
            'city_descriptions.*.ku' => ['nullable', 'string'],
        ]);

        // Create the map
        $map = ProjectMap::create([
            'project_id' => $data['project_id'],
            'name' => $data['name'],
            'type' => $data['type'],
            'description' => $data['description'] ?? null,
            'default_center' => $data['center_coordinates'] ?? null,
            'default_zoom' => $data['zoom_level'] ?? 10,
            'bounds' => $data['bounds'] ?? null,
            'is_active' => $data['is_active'] ?? true,
        ]);

        // Handle markers for single type maps
        if ($data['type'] === 'single' && isset($data['markers'])) {
            foreach ($data['markers'] as $markerData) {
                $map->markers()->create([
                    'title' => $markerData['title'],
                    'description' => $markerData['description'] ?? null,
                    'coordinates' => $markerData['coordinates'],
                    'icon_type' => $markerData['icon_type'] ?? 'default',
                    'is_active' => $markerData['is_active'] ?? true,
                    'sort_order' => $markerData['sort_order'] ?? 0,
                ]);
            }
        }

        // Handle cities for multi_city type maps
        if ($data['type'] === 'multi_city' && isset($data['selected_city_ids'])) {
            // Create customizations for the selected standalone cities
            foreach ($data['selected_city_ids'] as $index => $cityId) {
                $standaloneCity = \App\Models\ProjectCity::find($cityId);
                
                if ($standaloneCity && $standaloneCity->isStandalone()) {
                    // Get custom titles and descriptions for this city
                    $customTitle = $data['city_titles'][$cityId] ?? null;
                    $customDescription = $data['city_descriptions'][$cityId] ?? null;

                    // Create customization record (always create one to track selected cities)
                    \App\Models\ProjectCityCustomization::create([
                        'project_map_id' => $map->id,
                        'project_city_id' => $cityId, // Reference the standalone city directly
                        'custom_title' => $customTitle && (trim($customTitle['en'] ?? '') !== '' || trim($customTitle['ku'] ?? '') !== '') ? $customTitle : null,
                        'custom_description' => $customDescription && (trim($customDescription['en'] ?? '') !== '' || trim($customDescription['ku'] ?? '') !== '') ? $customDescription : null,
                        'sort_order' => $index,
                    ]);
                }
            }
        }

        session()->flash('success', 'Project map has been created');

        return redirect()->route('admin.project-maps.show', $map);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show(ProjectMap $projectMap)
    {
        $this->authorize(Permissions::MenuProjects->value);

        $projectMap->load(['project', 'cities.markers', 'markers']);

        return Inertia::render('ProjectMaps/Show', [
            'map' => new ProjectMapResource($projectMap),
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit(ProjectMap $projectMap)
    {
        $this->authorize(Permissions::EditProjects->value);

        $projectMap->load(['project', 'cities.markers', 'markers', 'customizations.projectCity']);

        return Inertia::render('ProjectMaps/Edit', [
            'projectMap' => new ProjectMapResource($projectMap),
            'projects' => Project::select('id', 'name')->get()->map(fn ($project) => [
                'id' => $project->id,
                'name' => $project->getTranslation('name', 'en'),
            ]),
            'availableCities' => \App\Admin\Http\Resources\CityResource::collection(
                \App\Models\ProjectCity::standalone()->active()->get()
            )->toArray(request()),
        ]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, ProjectMap $projectMap)
    {
        $this->authorize(Permissions::EditProjects->value);

        $data = $request->validate([
            'project_id' => ['required', 'exists:projects,id'],
            'name' => ['required', 'string', 'max:255'],
            'type' => ['required', 'in:single,multi_city'],
            'description' => ['nullable', 'string'],
            'center_coordinates' => ['nullable', 'array', 'size:2'],
            'center_coordinates.*' => ['numeric'],
            'zoom_level' => ['integer', 'min:1', 'max:20'],
            'bounds' => ['nullable', 'array'],
            'is_active' => ['boolean'],
            'markers' => ['nullable', 'array'],
             'markers.*.title' => ['required', 'array'],
            'markers.*.title.en' => ['required', 'string', 'max:255'],
            'markers.*.title.ku' => ['nullable', 'string', 'max:255'],
            'markers.*.description' => ['nullable', 'array'],
            'markers.*.description.en' => ['nullable', 'string'],
            'markers.*.description.ku' => ['nullable', 'string'],
            'markers.*.coordinates' => ['required', 'array', 'size:2'],
            'markers.*.coordinates.*' => ['numeric'],
            'markers.*.is_active' => ['boolean'],
            'markers.*.sort_order' => ['integer', 'min:0'],
            'markers.*.icon_type' => ['nullable', 'string'],
            'selected_city_ids' => ['nullable', 'array'],
            'selected_city_ids.*' => ['exists:project_cities,id'],
            'city_titles' => ['nullable', 'array'],
            'city_titles.*' => ['array'],
            'city_titles.*.en' => ['nullable', 'string'],
            'city_titles.*.ku' => ['nullable', 'string'],
            'city_descriptions' => ['nullable', 'array'],
            'city_descriptions.*' => ['array'],
            'city_descriptions.*.en' => ['nullable', 'string'],
            'city_descriptions.*.ku' => ['nullable', 'string'],
        ]);

        // Update the map itself
        $projectMap->update([
            'project_id' => $data['project_id'],
            'name' => $data['name'],
            'type' => $data['type'],
            'description' => $data['description'] ?? null,
            'default_center' => $data['center_coordinates'] ?? null, // Use center_coordinates instead of default_center
            'default_zoom' => $data['zoom_level'] ?? 10, // Use zoom_level instead of default_zoom
            'bounds' => $data['bounds'] ?? null,
            'is_active' => $data['is_active'] ?? true,
        ]);

        // Handle markers for single type maps
       DB::beginTransaction();
        if ($data['type'] === 'single' && isset($data['markers'])) {
            // Delete existing markers
            $projectMap->markers()->delete();
            
            // Create new markers
            foreach ($data['markers'] as $markerData) {
                $projectMap->markers()->create([
                    'title' => $markerData['title'],
                    'description' => $markerData['description'] ?? null,
                    'coordinates' => $markerData['coordinates'],
                    'icon_type' => $markerData['icon_type'] ?? 'default',
                    'is_active' => $markerData['is_active'] ?? true,
                    'sort_order' => $markerData['sort_order'] ?? 0,
                ]);
            }
        }
        DB::commit();

       DB::beginTransaction();
        // Handle cities for multi_city type maps - only update customizations, don't create new cities
        if ($data['type'] === 'multi_city' && isset($data['selected_city_ids'])) {
            // Delete existing customizations
            \App\Models\ProjectCityCustomization::where('project_map_id', $projectMap->id)->delete();
            
            // Create new customizations for the selected cities
            foreach ($data['selected_city_ids'] as $index => $cityId) {
                // Verify the city exists
                $standaloneCity = \App\Models\ProjectCity::find($cityId);
                
                if ($standaloneCity && $standaloneCity->isStandalone()) {
                    // Check if there are custom titles or descriptions for this city
                    $customTitle = $data['city_titles'][$cityId] ?? null;
                    $customDescription = $data['city_descriptions'][$cityId] ?? null;

                    // Always create customization record (even if title/description are empty)
                    // This ensures we have a record of which cities are selected for this map
                    \App\Models\ProjectCityCustomization::create([
                        'project_map_id' => $projectMap->id,
                        'project_city_id' => $cityId, // Use the standalone city ID directly
                        'custom_title' => $customTitle && (trim($customTitle['en'] ?? '') !== '' || trim($customTitle['ku'] ?? '') !== '') ? $customTitle : null,
                        'custom_description' => $customDescription && (trim($customDescription['en'] ?? '') !== '' || trim($customDescription['ku'] ?? '') !== '') ? $customDescription : null,
                    ]);
                }
            }
        }
        DB::commit();

        session()->flash('success', 'Project map has been updated');

        return redirect()->route('admin.project-maps.show', $projectMap);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(ProjectMap $projectMap)
    {
        $this->authorize(Permissions::DeleteProjects->value);

        $projectMap->delete();

        session()->flash('success', 'Project map has been deleted');

        return redirect()->route('admin.project-maps.index');
    }
}
