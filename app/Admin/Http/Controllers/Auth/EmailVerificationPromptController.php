<?php

namespace App\Admin\Http\Controllers\Auth;

use App\Admin\Providers\AdminRoutesProvider;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class EmailVerificationPromptController extends Controller
{
    /**
     * Display the email verification prompt.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function __invoke(Request $request)
    {
        return $request->user()->hasVerifiedEmail()
                    ? redirect()->intended(AdminRoutesProvider::HOME)
                    : view('admin.auth.verify-email');
    }
}
