<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Http\Resources\ProjectMarkerResource;
use App\Models\ProjectMarker;
use App\Models\ProjectCity;
use App\Models\ProjectMap;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use App\Http\Controllers\Controller;

class ProjectMarkerController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'permission:manage_projects']);
    }

    public function index(Request $request): JsonResponse
    {
        $query = ProjectMarker::with(['projectCity.projectMap.project']);

        // Filter by project map
        if ($request->has('project_map_id')) {
            $query->whereHas('projectCity', function ($q) use ($request) {
                $q->where('project_map_id', $request->project_map_id);
            });
        }

        // Filter by project city
        if ($request->has('project_city_id')) {
            $query->where('project_city_id', $request->project_city_id);
        }

        // Filter by project
        if ($request->has('project_id')) {
            $query->whereHas('projectCity.projectMap', function ($q) use ($request) {
                $q->where('project_id', $request->project_id);
            });
        }

        // Search
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereJsonContains('title->en', $search)
                  ->orWhereJsonContains('title->ar', $search)
                  ->orWhereJsonContains('title->ku', $search)
                  ->orWhereJsonContains('description->en', $search)
                  ->orWhereJsonContains('description->ar', $search)
                  ->orWhereJsonContains('description->ku', $search);
            });
        }

        // Sort
        $sortBy = $request->get('sort_by', 'sort_order');
        $sortDirection = $request->get('sort_direction', 'asc');
        $query->orderBy($sortBy, $sortDirection);

        $markers = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'data' => ProjectMarkerResource::collection($markers->items()),
            'meta' => [
                'current_page' => $markers->currentPage(),
                'last_page' => $markers->lastPage(),
                'per_page' => $markers->perPage(),
                'total' => $markers->total(),
            ]
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'project_city_id' => 'required|exists:project_cities,id',
            'title' => 'required|array',
            'title.en' => 'required|string|max:255',
            'title.ar' => 'nullable|string|max:255',
            'title.ku' => 'nullable|string|max:255',
            'description' => 'nullable|array',
            'description.en' => 'nullable|string',
            'description.ar' => 'nullable|string',
            'description.ku' => 'nullable|string',
            'coordinates' => 'required|array',
            'coordinates.lat' => 'required|numeric|between:-90,90',
            'coordinates.lng' => 'required|numeric|between:-180,180',
            'icon' => 'nullable|string|max:255',
            'color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Verify the project city exists and user has access
        $projectCity = ProjectCity::with('projectMap.project')->findOrFail($request->project_city_id);
        
        // Set sort order if not provided
        if (!$request->has('sort_order')) {
            $maxOrder = ProjectMarker::where('project_city_id', $request->project_city_id)->max('sort_order') ?? 0;
            $request->merge(['sort_order' => $maxOrder + 1]);
        }

        $marker = ProjectMarker::create($request->all());
        $marker->load('projectCity.projectMap.project');

        return response()->json([
            'message' => 'Project marker created successfully',
            'data' => new ProjectMarkerResource($marker)
        ], 201);
    }

    public function show(ProjectMarker $projectMarker): JsonResponse
    {
        $projectMarker->load('projectCity.projectMap.project');
        
        return response()->json([
            'data' => new ProjectMarkerResource($projectMarker)
        ]);
    }

    public function update(Request $request, ProjectMarker $projectMarker): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'project_city_id' => 'sometimes|required|exists:project_cities,id',
            'title' => 'sometimes|required|array',
            'title.en' => 'sometimes|required|string|max:255',
            'title.ar' => 'nullable|string|max:255',
            'title.ku' => 'nullable|string|max:255',
            'description' => 'nullable|array',
            'description.en' => 'nullable|string',
            'description.ar' => 'nullable|string',
            'description.ku' => 'nullable|string',
            'coordinates' => 'sometimes|required|array',
            'coordinates.lat' => 'sometimes|required|numeric|between:-90,90',
            'coordinates.lng' => 'sometimes|required|numeric|between:-180,180',
            'icon' => 'nullable|string|max:255',
            'color' => 'nullable|string|max:7|regex:/^#[0-9A-Fa-f]{6}$/',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $projectMarker->update($request->all());
        $projectMarker->load('projectCity.projectMap.project');

        return response()->json([
            'message' => 'Project marker updated successfully',
            'data' => new ProjectMarkerResource($projectMarker)
        ]);
    }

    public function destroy(ProjectMarker $projectMarker): JsonResponse
    {
        $projectMarker->delete();

        return response()->json([
            'message' => 'Project marker deleted successfully'
        ]);
    }

    public function updateSortOrder(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'markers' => 'required|array',
            'markers.*.id' => 'required|exists:project_markers,id',
            'markers.*.sort_order' => 'required|integer|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        foreach ($request->markers as $markerData) {
            ProjectMarker::where('id', $markerData['id'])
                ->update(['sort_order' => $markerData['sort_order']]);
        }

        return response()->json([
            'message' => 'Sort order updated successfully'
        ]);
    }

    public function toggleStatus(ProjectMarker $projectMarker): JsonResponse
    {
        $projectMarker->update(['is_active' => !$projectMarker->is_active]);
        $projectMarker->load('projectCity.projectMap.project');

        return response()->json([
            'message' => 'Project marker status updated successfully',
            'data' => new ProjectMarkerResource($projectMarker)
        ]);
    }
}
