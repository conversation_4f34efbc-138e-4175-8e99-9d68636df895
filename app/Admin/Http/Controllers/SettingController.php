<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Support\Permissions;
use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Inertia\Inertia;

class SettingController extends Controller
{
    public function edit(Request $request)
    {
        $this->authorize(Permissions::EditSetting->value);

        return Inertia::render('Settings/Edit', [
            'settings' => Setting::get(),
        ]);
    }

    public function update(Request $request, Setting $setting)
    {
        $this->authorize(Permissions::EditSetting->value);

        $key = $setting->key;
        $request->validate([
            $key => ['required'],
        ]);

        // Handle boolean values (convert to string for storage)
        $value = $request->get($key);
        
        // Handle both boolean values and string representations of booleans
        if (is_bool($value) || $value === "true" || $value === "false" || $value === true || $value === false || $value === 1 || $value === 0 || $value === "1" || $value === "0") {
            // Convert to boolean first to normalize
            $boolValue = filter_var($value, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
            // Only proceed if it's actually boolean (not null)
            if ($boolValue !== null) {
                $value = $boolValue ? 'true' : 'false';
            }
        }

        $setting->update([
            'value' => $value,
        ]);

        Cache::forget(Setting::cacheKey());

        session()->flash('success', 'Settings have been updated');

        return redirect()->back();
    }
}
