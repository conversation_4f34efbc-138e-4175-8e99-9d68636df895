<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Http\Resources\SlideResource;
use App\Admin\Support\Permissions;
use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\Slide;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class SlideController extends Controller
{
    public function index(Request $request)
    {
        return Inertia::render('Slides/Index', [
            'slides' => fn () => SlideResource::collection(
                Slide::with(['media'])
                    ->latest('id')
                    ->paginate($request->perPage ?? 15)
            ),
            'filters' => $request->all(['search']),
        ]);
    }

    public function create()
    {
        return Inertia::render('Slides/Create');
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'title' => ['required', 'array'],
            'title.*' => ['required', 'string'],
            'description' => ['nullable', 'array'],
            'description.*' => ['nullable', 'string'],
            'thumbnail' => ['required', 'image'],
            'link' => ['nullable', 'string'],
            'link_text.*' => ['nullable', 'string'],
        ]);

        /** @var \App\Models\Slide */
        $slide = Slide::create($data);

        if ($slide) {
            // Upload Thumbnail
            $slide->addMediaFromRequest('thumbnail')
                ->toMediaCollection('image');
        }

        session()->flash('success', 'Slide has been created');

        return redirect()->route('admin.slides.index');
    }

    public function edit(Slide $slide)
    {
        return Inertia::render('Slides/Edit', [
            'slide' => $slide->load('media'),
            'thumbnail' => $slide->getFirstMedia('image'),
        ]);
    }

    public function update(Request $request, Slide $slide)
    {
        $data = $request->validate([
            'title' => ['required', 'array'],
            'title.*' => ['required', 'string'],
            'description' => ['nullable', 'array'],
            'description.*' => ['nullable', 'string'],
            'thumbnail' => ['nullable', 'image'],
            'link' => ['nullable', 'string'],
            'link_text.*' => ['nullable', 'string'],
            'thumbnailHasChanged' => ['nullable', 'boolean']
        ]);

        $slide->update($data);

        if ($request->hasFile('thumbnail') && $request->boolean('thumbnailHasChanged')) {
            $slide->addMediaFromRequest('thumbnail')
                ->toMediaCollection('image');
        }

        session()->flash('success', 'Slide has been updated');

        return redirect()->route('admin.slides.index');
    }

    public function destroyMedia(Media $media)
    {
        $media->delete();

        session()->flash('success', 'Attachment has been deleted');

        return back();
    }

    public function destroy(Slide $slide)
    {
        $slide->delete();

        session()->flash('success', 'Slide has been deleted');

        return back();
    }
}
