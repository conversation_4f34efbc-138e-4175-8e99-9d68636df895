<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Http\Resources\ProjectCityResource;
use App\Admin\Support\Permissions;
use App\Http\Controllers\Controller;
use App\Models\ProjectCity;
use App\Models\ProjectMap;
use Illuminate\Http\Request;
use Inertia\Inertia;

class ProjectCityController extends Controller
{
    public function index(Request $request)
    {
        $this->authorize(Permissions::MenuProjects->value);

        return Inertia::render('ProjectCities/Index', [
            'cities' => fn () => ProjectCityResource::collection(
                ProjectCity::with(['projectMap.project', 'markers'])
                    ->when($request->map_id, fn ($query) => $query->where('project_map_id', $request->map_id))
                    ->orderBy('sort_order')
                    ->latest('id')
                    ->paginate($request->perPage ?? 15)
            ),
            'filters' => $request->all(['search', 'map_id']),
            'maps' => ProjectMap::with('project')->get()->map(fn ($map) => [
                'id' => $map->id,
                'name' => $map->name,
                'project_name' => $map->project->getTranslation('name', 'en'),
            ]),
        ]);
    }

    public function create(Request $request)
    {
        $this->authorize(Permissions::CreateProjects->value);

        $map = null;
        if ($request->has('map_id')) {
            $map = ProjectMap::with('project')->find($request->map_id);
        }

        return Inertia::render('ProjectCities/Create', [
            'maps' => ProjectMap::with('project')->get()->map(fn ($map) => [
                'id' => $map->id,
                'name' => $map->name,
                'project_name' => $map->project->getTranslation('name', 'en'),
            ]),
            'selectedMap' => $map,
        ]);
    }

    public function store(Request $request)
    {
        $this->authorize(Permissions::CreateProjects->value);

        $data = $request->validate([
            'project_map_id' => ['required', 'exists:project_maps,id'],
            'name' => ['required', 'array'],
            'name.en' => ['required', 'string', 'max:255'],
            'name.*' => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'array'],
            'description.*' => ['nullable', 'string'],
            'center_coordinates' => ['required', 'array', 'size:2'],
            'center_coordinates.*' => ['required', 'numeric'],
            'zoom_level' => ['integer', 'min:1', 'max:20'],
            'bounds' => ['nullable', 'array'],
            'is_active' => ['boolean'],
            'sort_order' => ['integer', 'min:0'],
        ]);

        $city = ProjectCity::create($data);

        session()->flash('success', 'Project city has been created');

        return redirect()->route('admin.project-cities.show', $city);
    }

    public function show(ProjectCity $projectCity)
    {
        $this->authorize(Permissions::MenuProjects->value);

        $projectCity->load(['projectMap.project', 'markers']);

        return Inertia::render('ProjectCities/Show', [
            'city' => new ProjectCityResource($projectCity),
        ]);
    }

    public function edit(ProjectCity $projectCity)
    {
        $this->authorize(Permissions::EditProjects->value);

        return Inertia::render('ProjectCities/Edit', [
            'city' => new ProjectCityResource($projectCity),
            'maps' => ProjectMap::with('project')->get()->map(fn ($map) => [
                'id' => $map->id,
                'name' => $map->name,
                'project_name' => $map->project->getTranslation('name', 'en'),
            ]),
        ]);
    }

    public function update(Request $request, ProjectCity $projectCity)
    {
        $this->authorize(Permissions::EditProjects->value);

        $data = $request->validate([
            'project_map_id' => ['required', 'exists:project_maps,id'],
            'name' => ['required', 'array'],
            'name.en' => ['required', 'string', 'max:255'],
            'name.*' => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'array'],
            'description.*' => ['nullable', 'string'],
            'center_coordinates' => ['required', 'array', 'size:2'],
            'center_coordinates.*' => ['required', 'numeric'],
            'zoom_level' => ['integer', 'min:1', 'max:20'],
            'bounds' => ['nullable', 'array'],
            'is_active' => ['boolean'],
            'sort_order' => ['integer', 'min:0'],
        ]);

        $projectCity->update($data);

        session()->flash('success', 'Project city has been updated');

        return redirect()->route('admin.project-cities.show', $projectCity);
    }

    public function destroy(ProjectCity $projectCity)
    {
        $this->authorize(Permissions::DeleteProjects->value);

        $projectCity->delete();

        session()->flash('success', 'Project city has been deleted');

        return redirect()->route('admin.project-cities.index');
    }
}
