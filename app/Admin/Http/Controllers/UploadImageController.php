<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Support\ContentImageUploader;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class UploadImageController extends Controller
{
    public function store(Request $request, ContentImageUploader $uploader)
    {
        // validate image size and dimensions
        $request->validate([
            'image' => ['required', 'image', 'max:1200'],
            'media_type' => ['required'],
        ]);

        $path = $uploader->upload($request->media_type, $request->file('image'));

        return response()->json([
            'data' => [
                'url' => Storage::url($path),
            ],
        ]);
    }
}
