<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Http\Resources\JobVacancyResource;
use App\Admin\Support\Permissions;
use App\Http\Controllers\Controller;
use App\Models\JobVacancy;
use Illuminate\Http\Request;
use Inertia\Inertia;

class JobVacancyController extends Controller
{
    public function index(Request $request)
    {
        return Inertia::render('Vacancies/Index', [
            'vacancies' => fn () => JobVacancyResource::collection(
                JobVacancy::latest('id')
                ->with('submissions')
                    ->paginate($request->perPage ?? 15)
            ),
            'filters' => $request->all(['search']),
        ]);
    }

    public function create()
    {
        $this->authorize(Permissions::CreateVacancy->value);

        return Inertia::render('Vacancies/Create');
    }

    public function store(Request $request)
    {
        $data = $request->validate([
            'title' => ['required', 'string'],
            'description' => ['required', 'string'],
            'location' => ['nullable', 'string'],
            'closed' => ['nullable', 'boolean'],
        ]);

        $this->authorize(Permissions::CreateVacancy->value);

        JobVacancy::create($data);

        session()->flash('success', 'Job vacancy has been created');

        return redirect()->route('admin.job-vacancies.index');
    }

    public function show(JobVacancy $jobVacancy)
    {
        $this->authorize(Permissions::MenuVacancy->value);

        return Inertia::render('Vacancies/Show', [
            'vacancy' => $jobVacancy->load('submissions'),
            'submissionsCount' => $jobVacancy->submissions()->count(),
            'unreadSubmissionsCount' => $jobVacancy->submissions()->where('reviewed', false)->count(),
        ]);
    }

    public function edit(JobVacancy $jobVacancy)
    {
        $this->authorize(Permissions::EditVacancy->value);

        return Inertia::render('Vacancies/Edit', [
            'vacancy' => $jobVacancy,
        ]);
    }

    public function update(Request $request, JobVacancy $jobVacancy)
    {
        $data = $request->validate([
            'title' => ['required', 'string'],
            'description' => ['required', 'string'],
            'location' => ['nullable', 'string'],
            'closed' => ['nullable', 'boolean'],
        ]);

        $this->authorize(Permissions::EditVacancy->value);

        $jobVacancy->update($data);

        session()->flash('success', 'Job vacancy has been updated');

        return redirect()->route('admin.job-vacancies.index');
    }

    public function destroy(JobVacancy $jobVacancy)
    {
        $this->authorize(Permissions::DeleteVacancy->value);
        $jobVacancy->delete();

        session()->flash('success', 'Job vacancy has been deleted');

        return back();
    }
}
