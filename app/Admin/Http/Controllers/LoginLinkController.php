<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Providers\AdminRoutesProvider;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoginLinkController extends Controller
{
    public function __invoke(Request $request)
    {
        if (app()->environment('production')) {
            return back();
        }

        $admin = User::where('email', '<EMAIL>')->firstOr(function () {
            return User::create([
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => bcrypt('pa$$word'),
            ]);
        });

        $admin->forceFill([
            'is_super_admin' => true,
        ]);
        
        $admin->assignRole('admin');

        Auth::login($admin);

        return redirect()->to(AdminRoutesProvider::HOME);
    }
}
