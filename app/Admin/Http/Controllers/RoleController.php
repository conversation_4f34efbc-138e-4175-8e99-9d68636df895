<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Http\Requests\RoleStoreFormRequest;
use App\Admin\Http\Requests\RoleUpdateFormRequest;
use App\Admin\Http\Resources\RoleResource;
use App\Admin\Support\Permissions;
use App\Admin\Support\Roles;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Inertia\Inertia;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth.superadmin');
    }

    public function index(Request $request)
    {
        $roles = Role::withCount('users')
            ->with('permissions')
            ->get();

        return Inertia::render('Role/Index', [
            'roles' => RoleResource::collection($roles),
        ]);
    }

    public function create()
    {
        return Inertia::render('Role/Create', [
            'permissions' => Permission::all(),
            'grouped_permissions' => $this->getGroupedPermissions(),
        ]);
    }

    public function store(RoleStoreFormRequest $request)
    {
        $role = Role::create([
            'name' => $request->name,
            'description' => $request->description,
        ]);

        if ($request->has('permissions')) {
            $role->givePermissionTo($request->permissions);
        }

        Cache::forget(Roles::Permission_Cache_key);

        session()->flash('success', 'Role has been successfully created.');

        return redirect()->route('admin.roles.index');
    }

    public function edit(Role $role)
    {
        // Prevent editing of system roles
        if ($this->isSystemRole($role)) {
            abort(403, 'System roles cannot be edited.');
        }

        return Inertia::render('Role/Edit', [
            'role' => $role->load('permissions'),
            'permissions' => Permission::all(),
            'grouped_permissions' => $this->getGroupedPermissions(),
        ]);
    }

    public function update(RoleUpdateFormRequest $request, Role $role)
    {
        // Prevent editing of system roles
        if ($this->isSystemRole($role)) {
            abort(403, 'System roles cannot be edited.');
        }

        $role->update([
            'name' => $request->name,
            'description' => $request->description,
        ]);

        // Sync permissions
        if ($request->has('permissions')) {
            $role->syncPermissions($request->permissions);
        } else {
            $role->syncPermissions([]);
        }

        Cache::forget(Roles::Permission_Cache_key);

        session()->flash('success', 'Role has been successfully updated.');

        return redirect()->route('admin.roles.index');
    }

    public function destroy(Role $role)
    {
        // Prevent deletion of system roles
        if ($this->isSystemRole($role)) {
            abort(403, 'System roles cannot be deleted.');
        }

        // Check if role has users assigned
        if ($role->users()->count() > 0) {
            session()->flash('error', 'Cannot delete role that has users assigned to it.');
            return redirect()->back();
        }

        $role->delete();
        Cache::forget(Roles::Permission_Cache_key);

        session()->flash('success', 'Role has been successfully deleted.');

        return redirect()->back();
    }

    /**
     * Check if this is a system role that shouldn't be modified
     */
    private function isSystemRole(Role $role): bool
    {
        return in_array(strtolower($role->name), ['admin', 'super admin', 'superadmin']);
    }

    /**
     * Get permissions grouped by their category
     */
    private function getGroupedPermissions(): array
    {
        $permissions = Permission::all();
        
        return $permissions->groupBy(function ($permission) {
            // Extract the category from permission name (e.g., "create projects" -> "projects")
            $parts = explode(' ', $permission->name);
            return count($parts) > 1 ? $parts[1] : 'general';
        })->toArray();
    }
}
