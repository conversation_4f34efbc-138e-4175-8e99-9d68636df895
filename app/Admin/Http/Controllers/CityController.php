<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Http\Resources\ProjectCityResource;
use App\Admin\Support\Permissions;
use App\Http\Controllers\Controller;
use App\Models\ProjectCity;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CityController extends Controller
{
    public function index(Request $request)
    {
        $this->authorize(Permissions::MenuProjects->value);

        return Inertia::render('Cities/Index', [
            'cities' => fn () => ProjectCityResource::collection(
                ProjectCity::whereNull('project_map_id') // Only standalone cities
                    ->when($request->search, fn ($query) => 
                        $query->whereJsonContains('name->en', $request->search)
                            ->orWhereJsonContains('name->ar', $request->search)
                            ->orWhereJsonContains('name->ku', $request->search)
                    )
                    ->orderBy('sort_order')
                    ->latest('id')
                    ->paginate($request->perPage ?? 15)
            ),
            'filters' => $request->all(['search']),
        ]);
    }

    public function create()
    {
        $this->authorize(Permissions::CreateProjects->value);

        return Inertia::render('Cities/Create');
    }

    public function store(Request $request)
    {
        $this->authorize(Permissions::CreateProjects->value);

        $data = $request->validate([
            'name' => ['required', 'array'],
            'name.en' => ['required', 'string', 'max:255'],
            'name.*' => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'array'],
            'description.*' => ['nullable', 'string'],
            'center_coordinates' => ['required', 'array', 'size:2'],
            'center_coordinates.*' => ['required', 'numeric'],
            'zoom_level' => ['integer', 'min:1', 'max:20'],
            'bounds' => ['required', 'array'], // This will contain the drawn boundary
            'is_active' => ['boolean'],
            'sort_order' => ['integer', 'min:0'],
        ]);

        // Set project_map_id to null for standalone cities
        $data['project_map_id'] = null;

        $city = ProjectCity::create($data);

        session()->flash('success', 'City has been created');

        return redirect()->route('admin.cities.show', $city);
    }

    public function show(ProjectCity $city)
    {
        $this->authorize(Permissions::MenuProjects->value);

        // Only show standalone cities
        if ($city->project_map_id) {
            abort(404);
        }

        return Inertia::render('Cities/Show', [
            'city' => new ProjectCityResource($city),
        ]);
    }

    public function edit(ProjectCity $city)
    {
        $this->authorize(Permissions::EditProjects->value);

        // Only edit standalone cities
        if ($city->project_map_id) {
            abort(404);
        }

        return Inertia::render('Cities/Edit', [
            'city' => new ProjectCityResource($city),
        ]);
    }

    public function update(Request $request, ProjectCity $city)
    {
        $this->authorize(Permissions::EditProjects->value);

        // Only update standalone cities
        if ($city->project_map_id) {
            abort(404);
        }

        $data = $request->validate([
            'name' => ['required', 'array'],
            'name.en' => ['required', 'string', 'max:255'],
            'name.*' => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'array'],
            'description.*' => ['nullable', 'string'],
            'center_coordinates' => ['required', 'array', 'size:2'],
            'center_coordinates.*' => ['required', 'numeric'],
            'zoom_level' => ['integer', 'min:1', 'max:20'],
            'bounds' => ['required', 'array'],
            'is_active' => ['boolean'],
            'sort_order' => ['integer', 'min:0'],
        ]);

        $city->update($data);

        session()->flash('success', 'City has been updated');

        return redirect()->route('admin.cities.show', $city);
    }

    public function destroy(ProjectCity $city)
    {
        $this->authorize(Permissions::DeleteProjects->value);

        // Only delete standalone cities
        if ($city->project_map_id) {
            abort(404);
        }

        // Check if city is being used in any project maps
        $usageCount = ProjectCity::where('project_map_id', '!=', null)
            ->whereJsonContains('name->en', $city->getTranslation('name', 'en'))
            ->count();

        if ($usageCount > 0) {
            session()->flash('error', 'Cannot delete city as it is being used in project maps');
            return back();
        }

        $city->delete();

        session()->flash('success', 'City has been deleted');

        return redirect()->route('admin.cities.index');
    }
}
