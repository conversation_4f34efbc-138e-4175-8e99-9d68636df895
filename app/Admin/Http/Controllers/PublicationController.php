<?php

namespace App\Admin\Http\Controllers;

use App\Admin\Http\Requests\PublicationFormRequest;
use App\Admin\Http\Requests\PublicationUpdateFormRequest;
use App\Admin\Http\Resources\PublicationResource;
use App\Admin\Support\Permissions;
use App\Http\Controllers\Controller;
use App\Models\Publication;
use App\Shared\Enums\PublicationMediaCollection;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class PublicationController extends Controller
{
    public function index(Request $request)
    {
        return Inertia::render('Publications/Index', [
            'publications' => fn () => PublicationResource::collection(
                Publication::with(['media'])
                    ->latest('id')
                    ->paginate($request->perPage ?? 15)
            ),
            'filters' => $request->all(['search']),
        ]);
    }

    public function create()
    {
        $this->authorize(Permissions::CreateNews->value);

        return Inertia::render('Publications/Create');
    }

    public function store(PublicationFormRequest $request)
    {
        $this->authorize(Permissions::CreateNews->value);

        /** @var \App\Models\Publication */
        $publication = Publication::create($request->validated());

        if ($publication) {
            // Upload Thumbnail
            $publication->addMediaFromRequest('thumbnail')
                ->toMediaCollection(PublicationMediaCollection::Thumbnail->value);

            // Upload attachments
            collect($request->file('attachments'))
                ->each(
                    fn ($attachment, $lang) => $publication->addMedia($attachment)
                        ->withCustomProperties(['lang' => $lang])
                        ->toMediaCollection(PublicationMediaCollection::Attachments->value)
                );
        }

        session()->flash('success', 'Publication has been created');

        return redirect()->route('admin.publications.index');
    }

    public function edit(Publication $publication)
    {
        $this->authorize(Permissions::EditNews->value);

        return Inertia::render('Publications/Edit', [
            'publication' => $publication,
            'thumbnail' => $publication->getFirstMedia(PublicationMediaCollection::Thumbnail->value),
            'attachments' => $publication->getMedia(PublicationMediaCollection::Attachments->value),
        ]);
    }

    public function update(PublicationUpdateFormRequest $request, Publication $publication)
    {
        $this->authorize(Permissions::EditNews->value);

        $publication->update($request->validated());

        if ($request->hasFile('thumbnail') && $request->shouldUploadThumbnail()) {
            $publication->addMediaFromRequest('thumbnail')
                ->toMediaCollection(PublicationMediaCollection::Thumbnail->value);
        }

        if ($request->hasFile('attachments')) {
            collect($request->file('attachments'))
                ->each(
                    fn ($attachment, $lang) => $publication->addMedia($attachment)
                        ->withCustomProperties(['lang' => $lang])
                        ->toMediaCollection(PublicationMediaCollection::Attachments->value)
                );
        }

        session()->flash('success', 'Publication has been updated');

        return redirect()->route('admin.publications.index');
    }

    public function destroyMedia(Media $media)
    {
        $media->delete();

        session()->flash('success', 'Attachment has been deleted');

        return back();
    }

    public function destroy(Publication $publication)
    {
        $this->authorize(Permissions::DeleteNews->value);
        $publication->delete();

        session()->flash('success', 'Publication has been deleted');

        return back();
    }
}
