<?php

namespace App\Admin\Http\Resources;

use App\Admin\Support\Permissions;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class VideoResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        /** @var \App\Models\User */
        $user = Auth::user();

        return [
            'id' => $this->id,
            'title' => $this->getTranslation('title', 'en'),
            'type' => class_basename($this->videoble_type),
            'videoble_name' => $this->whenLoaded('videoble', fn () => $this->videoble?->name),
            'can_edit' => $user->can(Permissions::EditVideo->value),
            'can_delete' => $user->can(Permissions::DeleteVideo->value),
        ];
    }
}
