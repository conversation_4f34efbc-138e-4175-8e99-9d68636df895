<?php

namespace App\Admin\Http\Resources;

use App\Admin\Support\Permissions;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class ProjectMapResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        /** @var \App\Models\User */
        $user = Auth::user();

        return [
            'id' => $this->id,
            'name' => $this->name,
            'type' => $this->type,
            'center_coordinates' => $this->center_coordinates,
            'default_zoom' => $this->default_zoom,
            'bounds' => $this->bounds,
            'is_active' => $this->is_active,
            'project' => $this->whenLoaded('project', fn () => [
                'id' => $this->project->id,
                'name' => $this->project->getTranslation('name', 'en'),
                'slug' => $this->project->slug,
            ]),
            'cities' => $this->whenLoaded('cities', fn () => 
                $this->cities->map(fn ($city) => [
                    'id' => $city->id,
                    'name' => $city->getTranslations('name'),
                    'slug' => $city->slug,
                    'center_coordinates' => $city->center_coordinates,
                    'zoom_level' => $city->zoom_level,
                    'bounds' => $city->bounds,
                    'is_active' => $city->is_active,
                    'markers_count' => $city->markers_count ?? $city->markers()->count(),
                ])
            ),
            'customizations' => $this->whenLoaded('customizations', fn () => 
                $this->customizations->map(fn ($customization) => [
                    'id' => $customization->id,
                    'project_city_id' => $customization->project_city_id,
                    'custom_title' => $customization->getTranslations('custom_title'),
                    'custom_description' => $customization->getTranslations('custom_description'),
                    'project_city' => [
                        'id' => $customization->projectCity->id,
                        'name' => $customization->projectCity->getTranslations('name'),
                        'slug' => $customization->projectCity->slug,
                    ],
                ])
            ),
            'markers' => $this->whenLoaded('markers', fn () => 
                $this->markers->map(fn ($marker) => [
                    'id' => $marker->id,
                    'title' => $marker->getTranslations('title'),
                    'description' => $marker->getTranslations('description'),
                    'coordinates' => $marker->coordinates,
                    'icon_type' => $marker->icon_type,
                    'icon_color' => $marker->icon_color,
                    'is_active' => $marker->is_active,
                ])
            ),
            'can_edit' => $user->can(Permissions::EditProjects->value),
            'can_delete' => $user->can(Permissions::DeleteProjects->value),
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
        ];
    }
}
