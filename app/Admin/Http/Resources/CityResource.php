<?php

namespace App\Admin\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'center_coordinates' => $this->center_coordinates,
            'zoom_level' => $this->zoom_level,
            'bounds' => $this->bounds,
            'description' => $this->description,
            'is_active' => $this->is_active,
            'sort_order' => $this->sort_order,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'is_standalone' => $this->isStandalone(),
            'markers_count' => $this->whenLoaded('markers', function () {
                return $this->markers->count();
            }),
            'active_markers_count' => $this->whenLoaded('markers', function () {
                return $this->markers->where('is_active', true)->count();
            }),
        ];
    }
}
