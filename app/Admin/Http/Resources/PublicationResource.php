<?php

namespace App\Admin\Http\Resources;

use App\Admin\Support\Permissions;
use App\Shared\Enums\PublicationMediaCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class PublicationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        /** @var \App\Models\User */
        $user = Auth::user();

        return [
            'id' => $this->id,
            'title' => $this->getTranslation('title', 'en'),
            'slug' => $this->slug,
            'thumbnail' => $this->getFirstMediaUrl(PublicationMediaCollection::Thumbnail->value),
            'program' => $this->whenLoaded('program', fn () => $this->program->getTranslation('name', 'en')),
            'can_edit' => $user->can(Permissions::EditNews->value),
            'can_delete' => $user->can(Permissions::DeleteNews->value),
            'created_at' => $this->created_at->format('Y-m-d h:i:s'),
        ];
    }
}
