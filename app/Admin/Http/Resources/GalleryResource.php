<?php

namespace App\Admin\Http\Resources;

use App\Admin\Support\Permissions;
use App\Shared\Enums\EventMediaCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class GalleryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        /** @var \App\Models\User */
        $user = Auth::user();

        return [
            'id' => $this->id,
            'caption' => $this->caption,
            'thumbnail' => $this->thumb(),
            'photo' => $this->photo(),
            'can_edit' => $user->can(Permissions::EditCategory->value),
            'can_delete' => $user->can(Permissions::DeleteGallery->value),
            'created_at' => $this->created_at->format('Y-m-d h:i:s'),
        ];
    }
}
