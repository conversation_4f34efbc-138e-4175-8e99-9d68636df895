<?php

namespace App\Admin\Http\Resources;

use App\Admin\Support\Permissions;
use App\Shared\Enums\ProjectMediaCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class ProjectResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        /** @var \App\Models\User */
        $user = Auth::user();

        return [
            'id' => $this->id,
            'name' => $this->getTranslation('name', 'en'),
            'slug' => $this->slug,
            'sort' => $this->sort,
            'thumbnail' => $this->getFirstMediaUrl(ProjectMediaCollection::Thumbnail->value),
            'can_edit' => $user->can(Permissions::EditProjects->value),
            'can_delete' => $user->can(Permissions::DeleteProjects->value),
            'created_at' => $this->created_at->format('Y-m-d h:i:s'),
        ];
    }
}
