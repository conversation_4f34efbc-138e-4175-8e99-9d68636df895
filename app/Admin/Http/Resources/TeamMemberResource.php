<?php

namespace App\Admin\Http\Resources;

use App\Admin\Support\Permissions;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Gate;

class TeamMemberResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'department' => $this->department,
            'name' => $this->name,
            'description' => $this->description,
            'email' => $this->email,
            'phone' => $this->phone,
            'role' => $this->role,
            'photo_url' => $this->photoUrl(),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            'can_edit' => Gate::allows(Permissions::EditTeamMember->value),
            'can_delete' => Gate::allows(Permissions::DeleteTeamMember->value),
        ];
    }
}
