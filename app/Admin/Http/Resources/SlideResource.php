<?php

namespace App\Admin\Http\Resources;

use App\Admin\Support\Permissions;
use App\Shared\Enums\EventMediaCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class SlideResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->getTranslation('title', 'en'),
            'image' => $this->imageUrl(),
            'can_edit' => true,
            'can_delete' => true,
            'created_at' => $this->created_at->format('Y-m-d h:i:s'),
        ];
    }
}
