<?php

namespace App\Admin\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class RoleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        /** @var \App\Models\User */
        $user = Auth::user();

        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description ?? '',
            'permissions_count' => $this->whenLoaded('permissions', fn () => $this->permissions->count(), 0),
            'users_count' => $this->whenCounted('users'),
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'can_edit' => $user->isSuperAdmin() && !$this->isSystemRole(),
            'can_delete' => $user->isSuperAdmin() && !$this->isSystemRole() && $this->users_count === 0,
        ];
    }

    /**
     * Check if this is a system role that shouldn't be modified
     */
    private function isSystemRole(): bool
    {
        return in_array(strtolower($this->name), ['admin', 'super admin', 'superadmin']);
    }
}
