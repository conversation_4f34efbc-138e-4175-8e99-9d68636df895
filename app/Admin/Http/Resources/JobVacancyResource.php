<?php

namespace App\Admin\Http\Resources;

use App\Admin\Support\Permissions;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class JobVacancyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        /** @var \App\Models\User */
        $user = Auth::user();

        return [
            'id' => $this->id,
            'title' => $this->title,
            'slug' => $this->slug,
            'description' => $this->description,
            'location' => $this->location,
            'closed' => $this->closed,
            'submissions_count' => $this->whenLoaded('submissions', fn() => $this->submissions->count()),
            'unread_submissions_count' => $this->whenLoaded('submissions', fn() => $this->submissions->where('reviewed', false)->count()),
            'can_edit' => $user->can(Permissions::EditVacancy->value),
            'can_delete' => $user->can(Permissions::DeleteVacancy->value),
            'can_view_submissions' => $user->can(Permissions::MenuVacancy->value),
            'created_at' => $this->created_at->format('Y-m-d h:i:s'),
        ];
    }
}
