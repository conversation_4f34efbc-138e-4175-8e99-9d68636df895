<?php

namespace App\Admin\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ProjectMarkerResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'project_city_id' => $this->project_city_id,
            'title' => $this->getTranslations('title'),
            'description' => $this->getTranslations('description'),
            'coordinates' => $this->coordinates,
            'icon' => $this->icon,
            'color' => $this->color,
            'is_active' => $this->is_active,
            'sort_order' => $this->sort_order,
            'project_city' => $this->whenLoaded('projectCity', function () {
                return [
                    'id' => $this->projectCity->id,
                    'name' => $this->projectCity->getTranslations('name'),
                    'slug' => $this->projectCity->slug,
                ];
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
