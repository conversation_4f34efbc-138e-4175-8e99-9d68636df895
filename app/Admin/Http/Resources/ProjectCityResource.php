<?php

namespace App\Admin\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ProjectCityResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'project_map_id' => $this->project_map_id,
            'slug' => $this->slug,
            'name' => $this->getTranslations('name'),
            'description' => $this->getTranslations('description'),
            'center_coordinates' => $this->center_coordinates,
            'bounds' => $this->bounds,
            'zoom_level' => $this->zoom_level,
            'is_active' => $this->is_active,
            'sort_order' => $this->sort_order,
            'markers' => ProjectMarkerResource::collection($this->whenLoaded('markers')),
            'is_standalone' => $this->isStandalone(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'markers_count' => $this->whenLoaded('markers', function () {
                return $this->markers->count();
            }),
            'active_markers_count' => $this->whenLoaded('markers', function () {
                return $this->markers->where('is_active', true)->count();
            }),
        ];
    }
}
