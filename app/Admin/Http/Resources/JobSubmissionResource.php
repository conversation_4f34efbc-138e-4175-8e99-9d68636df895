<?php

namespace App\Admin\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class JobSubmissionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'job_vacancy_id' => $this->job_vacancy_id,
            'full_name' => $this->full_name,
            'email' => $this->email,
            'summary' => $this->summary,
            'reviewed' => $this->reviewed,
            'cv_url' => $this->cvUrl(),
            'has_cv' => $this->getFirstMedia('cv') !== null,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            'job_vacancy' => $this->whenLoaded('jobVacancy', function () {
                return [
                    'id' => $this->jobVacancy->id,
                    'title' => $this->jobVacancy->title,
                    'slug' => $this->jobVacancy->slug,
                ];
            }),
        ];
    }
}
