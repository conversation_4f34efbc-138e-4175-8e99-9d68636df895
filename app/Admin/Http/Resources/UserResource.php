<?php

namespace App\Admin\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        /** @var \App\Models\User */
        $user = Auth::user();

        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'role' => $this->whenLoaded('roles', fn () => $this->roles->first()?->name ?? 'No Role'),
            'role_id' => $this->whenLoaded('roles', fn () => $this->roles->first()?->id),
            'can_edit' => $user->isSuperAdmin(),
            'can_delete' => $user->isSuperAdmin(),
            'created_at' => $this->created_at->format('Y-m-d h:i:s'),
        ];
    }
}
