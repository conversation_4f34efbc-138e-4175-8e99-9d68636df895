<?php

namespace App\Admin\Http\Middleware;

use App\Admin\Support\Roles;
use App\Shared\Support\LocaleManager;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Inertia\Middleware;

class HandleInertiaRequests extends Middleware
{
    /**
     * The root template that's loaded on the first page visit.
     *
     * @see https://inertiajs.com/server-side-setup#root-template
     *
     * @var string
     */
    protected $rootView = 'admin.app';

    /**
     * Determines the current asset version.
     *
     * @see https://inertiajs.com/asset-versioning
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    public function version(Request $request): ?string
    {
        return parent::version($request);
    }

    /**
     * Defines the props that are shared by default.
     *
     * @see https://inertiajs.com/shared-data
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function share(Request $request): array
    {
        /** @var \App\Models\User */
        $auth = current_user();

        return array_merge(parent::share($request), [
            'auth' => $auth,
            'isSuperAdmin' => $auth->isSuperAdmin(),
            'authPermissions' => $auth->isSuperAdmin() ? [] : Cache::rememberForever(
                Roles::Permission_Cache_key,
                fn () => $auth->roles
                    ?->first()
                    ?->permissions
                    ?->pluck('name')
                    ?->values()
            ),
            'isLocalMode' => app()->environment('local'),
            'languages' => LocaleManager::locales(),
            'flash' => function () use ($request) {
                return [
                    'success' => $request->session()->get('success'),
                    'error' => $request->session()->get('error'),
                ];
            },
        ]);
    }
}
