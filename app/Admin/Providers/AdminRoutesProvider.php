<?php

namespace App\Admin\Providers;

use Illuminate\Foundation\Support\Providers\RouteServiceProvider;
use Illuminate\Support\Facades\Route;

class AdminRoutesProvider extends RouteServiceProvider
{
    /**
     * The path to the "home" route for your application.
     *
     * Typically, users are redirected here after authentication.
     *
     * @var string
     */
    public const HOME = '/admin';

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        $this->routes(function () {
            Route::middleware(['admin', 'auth', 'auth.admin'])
                ->prefix('admin')
                ->as('admin.')
                ->group(base_path('routes/admin/admin.php'));

            Route::middleware(['web', 'admin'])
                ->prefix('admin')
                ->as('admin.')
                ->group(base_path('routes/admin/auth.php'));
        });
    }
}
