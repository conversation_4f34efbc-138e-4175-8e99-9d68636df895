<?php

namespace App\Http\Livewire;

use App\Models\Publication;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Livewire\Component;

class NewsList extends Component
{
    public function render(): View
    {
        return view('livewire.news-list', [
            'news' => $this->news(),
        ]);
    }

    public function news(): Collection
    {
        return Publication::query()
            ->latest()
            ->with(['media'])
            ->get();
    }
}
