<?php

namespace App\Http\Livewire\Frontend;

use App\Models\Publication;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Livewire\Component;
use Livewire\WithPagination;

class PublicationList extends Component
{
    use WithPagination;

    public $program;

    protected $paginationTheme = 'tailwind';

    public $perPage = 12;

    protected $queryString = [
        'program' => ['except' => ''],
        'page' => ['except' => 1],
    ];

    public function render()
    {
        return view('livewire.frontend.publication-list', [
            'publications' => $this->publications(),
        ]);
    }

    public function updatingProgram()
    {
        $this->resetPage();
    }

    public function publications(): LengthAwarePaginator
    {
        return Publication::select(['id', 'title', 'slug', 'excerpt'])
            ->with(['media'])
            ->latest('id')
            ->paginate($this->perPage ?? 25);
    }
}
