<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class LocalizeMiddleware
{
    const HttpIgnoreMethods = ['POST', 'PUT', 'PATCH', 'DELETE'];

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        $locale = $request->route()->parameter('locale') ?? $request->get('locale');

        if ($this->isSupported($locale)) {
            session()->put('locale', $locale);
        }

        // if (
        //     ! $this->shouldIgnore($request) &&
        //     ! blank($locale) &&
        //     (! $request->route()->hasParameter('locale') || ! $this->isSupported($locale))
        // ) {
        //     return redirect()->to($this->redirectPath($request));
        // }

        app_locale(session('locale') ?? config('app.fallback_locale'));

        return $next($request);
    }

    protected function isSupported(?string $locale): bool
    {
        return array_key_exists($locale, config('lang.supported_locales'));
    }

    protected function shouldIgnore(Request $request): bool
    {
        return in_array($request->method(), self::HttpIgnoreMethods);
    }

    public function redirectPath(Request $request): string
    {
        $locale = session('locale') ?? config('app.fallback_locale');

        return $locale.'/'.$request->path();
    }
}
