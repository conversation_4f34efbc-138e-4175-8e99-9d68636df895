<?php
// Path: app/Services/SearchService.php
namespace App\Services;

use App\Models\Publication;
use Illuminate\Support\Collection;

class SearchService
{
    public function search(string $term = ''): Collection
    {
        return collect([
            ...$this->pages($term),
            ...$this->news($term),
        ])
            ->sortByDesc(fn (PageMeta $item) => $item->search($term));
    }

    /**
     * Return news collection
     *
     * @return Collection<\App\Services\PageMeta>
     */
    public function news(string $term): Collection
    {
        return Publication::query()
            ->where('title->en', 'like', "%{$term}%")
            ->orWhere('excerpt->en', 'like', "%{$term}%")
            ->get()
            ->map(function ($news) {
                return new PageMeta($news->title, $news->path(), $news->excerpt);
            })
            ->values();
    }

    /**
     * Return page collection
     *
     * @return Collection<\App\Services\PageMeta>
     */
    public function pages(string $term = ''): Collection
    {

        return collect([
            [
                'title' => 'Home',
                'url' => route('home'),
            ],
            [
                'title' => 'News',
                'url' => route('news'),
            ],
            [
                'title' => 'Gallery',
                'url' => route('gallery'),
            ],
            [
                'title' => 'HSE (Health, Safety and Environment)',
                'url' => route('hse'),
            ],
            [
                'title' => 'Health and Safety Policy',
                'url' => route('hse') . '#hsp',
            ],
            [
                'title' => 'QHSE Management System',
                'url' => route('hse') . '#qhse',
            ],
            [
                'title' => 'Risk Management',
                'url' => route('hse') . '#risk_management',
            ],
            [
                'title' => 'Permit to Work System',
                'url' => route('hse') . '#ptw',
            ],
            [
                'title' => 'Incident Management',
                'url' => route('hse') . '#incident_management',
            ],
            [
                'title' => 'Training',
                'url' => route('hse') . '#training',
            ],
            [
                'title' => 'About us',
                'url' => route('about'),
            ],
            [
                'title' => 'Contact us',
                'url' => route('contact'),
            ],
        ])
            ->map(function ($page) {
                return new PageMeta($page['title'], $page['url'], $page['description'] ?? null);
            })
            ->filter(fn (PageMeta $page) => $page->search($term));
    }
}