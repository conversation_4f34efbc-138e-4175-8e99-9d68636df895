<?php

namespace App\Services;

use Illuminate\Contracts\Support\Arrayable;

class PageMeta implements Arrayable
{
    public function __construct(
        public readonly string $title,
        public readonly string $url,
        public readonly ?string $description = ''
    ) {
    }

    public function toArray()
    {
        return [
            'title' => $this->title,
            'url' => $this->url,
            'description' => $this->description,
        ];
    }

    public function search(string|null $term = null): bool
    {
        if (empty($term)) {
            return false;
        }

        $term = trim(strip_tags(
            strtolower($term)
        ));

        $title = strtolower($this->title);
        $description = strtolower($this->description ?? '');
        return str_contains($title, $term) || str_contains($description ?? '', $term);
    }

    public function getKey()
    {
        return $this->url;
    }
}
