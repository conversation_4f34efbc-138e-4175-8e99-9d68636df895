<?php

namespace App\Shared\Support;

class Locale
{
    public string $name;

    public string $native;

    public string $direction;

    public string $iso_code_name;

    public ?string $flag_icon = null;

    public function __construct(
        string $name,
        string $native,
        string $direction,
        string $iso_code_name,
        ?string $flag_icon,
    ) {
        $this->name = $name;
        $this->native = $native;
        $this->direction = $direction;
        $this->iso_code_name = $iso_code_name;
        $this->flag_icon = $flag_icon;
    }

    /**
     * A friendly function to get iso_short_code: en, ar, ku, tr
     *
     * @return string
     */
    public function shortName(): string
    {
        return $this->iso_code_name;
    }

    /**
     * Determine if locale is active
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return app_locale() == $this->iso_code_name;
    }

    /**
     * Get flag icon
     *
     * @return string|null
     */
    public function flag(): ?string
    {
        return $this->flag_icon;
    }
}
