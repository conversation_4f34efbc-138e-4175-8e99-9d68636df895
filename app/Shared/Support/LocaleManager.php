<?php

namespace App\Shared\Support;

use Illuminate\Support\Collection;

class LocaleManager
{
    public Collection $locales;

    public function __construct()
    {
        $this->locales = collect(config('lang.supported_locales'))
            ->map(function ($localeData, $shortName) {
                return new Locale(
                    name: $localeData['name'],
                    native: $localeData['native'],
                    direction: $localeData['direction'],
                    iso_code_name: $shortName,
                    flag_icon: env('APP_URL')."/images/flag_icons/$shortName.png"
                );
            });
    }

    /**
     * Get list of all supported locales
     *
     * @return Collection<Locale>
     */
    public static function supportedLocales(): Collection
    {
        return (new self)->locales;
    }

    /**
     * Get all supported locale keys
     * `example: en,ar,ku`
     *
     * @return Collection<string>
     */
    public static function supportedLocaleKeys(): Collection
    {
        return self::supportedLocales()->keys();
    }

    /**
     * Get all supported locales
     *
     * @return Collection<Locale>
     */
    public static function locales(): Collection
    {
        return self::supportedLocales();
    }

    public static function activeLocale(): ?Locale
    {
        /** @var Locale */
        $active = self::supportedLocales()
            ->first(function (Locale $locale) {
                return $locale->isActive();
            });

        return $active;
    }

    /**
     * Get Locales except active locale
     *
     * @return Collection<Locale>
     */
    public static function localesExceptActive(): Collection
    {
        return self::supportedLocales()
            ->filter(function (Locale $locale) {
                return ! $locale->isActive();
            });
    }
}
