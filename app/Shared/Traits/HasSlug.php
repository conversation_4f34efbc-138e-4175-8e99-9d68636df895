<?php

namespace App\Shared\Traits;

use Exception;
use Illuminate\Support\Str;

trait HasSlug
{
    public static function bootHasSlug()
    {
        static::creating(function (self $model) {
            if (
                property_exists($model, self::slugProperty())
                || property_exists($model, self::slugifyProperty())
            ) {
                throw new Exception('Slug or slugify property doesn\'t exists on model '.$model::class);
            }

            $model->{self::slugProperty()} ??= Str::slug($model->{self::slugifyProperty()});
        });
    }

    protected static function slugifyProperty(): string
    {
        return self::$slugify_property;
    }

    protected static function slugProperty(): string
    {
        return self::$slug_property;
    }
}
