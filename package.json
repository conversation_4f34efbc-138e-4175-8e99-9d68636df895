{"private": true, "scripts": {"dev": "vite", "build:frontend": "vite build", "dev:admin": "vite --config vite-admin.config.js", "build:admin": "vite build --config vite-admin.config.js", "build": "npm-run-all build:*"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.7", "@vitejs/plugin-legacy": "^2.0.1", "alpinejs": "^3.4.2", "autoprefixer": "^10.4.2", "axios": "^0.21.1", "cross-env": "^7.0.3", "laravel-vite-plugin": "^0.6.0", "lodash": "^4.17.19", "npm-run-all": "^4.1.5", "postcss": "^8.4.6", "tailwindcss": "^3.1.0", "tailwindcss-rtl": "^0.9.0", "terser": "^5.14.2", "unplugin-vue-macros": "^0.11.0", "vite": "^3.0.0"}, "dependencies": {"@alpinejs/collapse": "^3.10.3", "@alpinejs/focus": "^3.10.3", "@fancyapps/ui": "^5.0.32", "@headlessui/vue": "^1.6.7", "@heroicons/vue": "^2.0.8", "@inertiajs/inertia": "^0.11.0", "@inertiajs/inertia-vue3": "^0.6.0", "@inertiajs/progress": "^0.2.7", "@popperjs/core": "^2.11.6", "@vitejs/plugin-vue": "^3.0.3", "@vuepic/vue-datepicker": "^3.4.7", "@vueup/vue-quill": "^1.0.0-beta.10", "bootstrap": "^5.2.0", "bootstrap-icons": "^1.9.1", "filepond": "^4.30.4", "filepond-plugin-file-validate-type": "^1.2.8", "filepond-plugin-image-preview": "^4.6.11", "fullpage.js": "^4.0.11", "jquery": "^3.6.1", "leaflet": "^1.9.4", "momentum-modal": "^0.1.13", "motion": "^10.16.4", "naive-ui": "^2.33.3", "nanoid": "^5.1.5", "preline": "^2.0.3", "quill-image-uploader": "^1.2.3", "quill-magic-url": "^4.2.0", "swiper": "^11.0.5", "uuid": "^8.3.2", "vue": "^3.2.37", "vue-filepond": "^7.0.3", "vue-leaflet": "^0.1.0", "vue-toastification": "^2.0.0-rc.5"}}