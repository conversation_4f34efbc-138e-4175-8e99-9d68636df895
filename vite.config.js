import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import fs from 'fs'
import {homedir} from 'os'
import {resolve} from 'path'
import legacy from '@vitejs/plugin-legacy'

let host = 'bim.test'
const path = 'resources/assets/frontend';
let inputs = [
    `${path}/website.css`,
    `${path}/js/app.js`,
    `${path}/js/gallery.js`,
    `${path}/js/components/ProjectMap.js`,
    `${path}/css/components/project-map.css`,
];

let buildDirectory = 'assets/frontend';


export default defineConfig({
    plugins: [
        laravel({
            input: inputs,
            refresh: true,
            buildDirectory,
        }),
        bladeRefresher(),
          {
            ...legacy({
                targets: ['defaults', 'not IE 11']
              }), 
            apply: 'build'
          }
    ],
    resolve: {
        alias: {
            '@': '/resources/js',
        },
    },
    css: {
        postcss: {
            plugins: [
                require("autoprefixer"),
                require('tailwindcss')('./tailwind.website.config.js'),
            ]
        },
    },
    server: detectServerConfig(host),
    optimizeDeps: { exclude: ["swiper/vue", "swiper/types"], },
    build: {
        polyfillModulePreload: true,
    }
});

function detectServerConfig(host) {
    let keyPath = resolve(homedir(), `Library/Application Support/Herd/config/valet/Certificates/${host}.key`)
    let certificatePath = resolve(homedir(), `Library/Application Support/Herd/config/valet/Certificates/${host}.crt`)

    if (!fs.existsSync(keyPath)) {
        return {}
    }

    if (!fs.existsSync(certificatePath)) {
        return {}
    }

    return {
        hmr: {host},
        host,
        https: {
            key: fs.readFileSync(keyPath),
            cert: fs.readFileSync(certificatePath),
        },
    }
}

function bladeRefresher() {
    return {
        name: 'blade',
        handleHotUpdate({ file, server }) {
            if (file.endsWith('.blade.php')) {
                server.ws.send({
                    type: 'full-reload',
                    path: '*',
                })
            }
        }
    }
}