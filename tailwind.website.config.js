const defaultTheme = require("tailwindcss/defaultTheme");

/** @type {import('tailwindcss').Config} */
module.exports = {
    content: [
        "./vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php",
        "./storage/framework/views/*.php",
        "./resources/views/**/*.blade.php",
        "./resources/assets/js/**/*.{vue,js}",
        "node_modules/preline/dist/*.js",
    ],
    darkMode: "class",
    theme: {
        extend: {
            fontFamily: {
                sans: ["Inter", ...defaultTheme.fontFamily.sans],
            },
            colors: {
                primary: "#008cd3",
                secondary: "#080A09",
                brand: {
                    DEFAULT: "#008cd3",
                    50: "#f0f9ff",
                    100: "#e0f2fe",
                    200: "#b9e6fe",
                    300: "#7cd3fd",
                    400: "#36bffa",
                    500: "#0ca7eb",
                    600: "#008cd3",
                    700: "#016aa3",
                    800: "#065986",
                    900: "#0b4a6f",
                    950: "#072f4a",
                },
            },
        },
    },

    plugins: [
        require("@tailwindcss/typography"),
        require("@tailwindcss/forms"),
        require("@tailwindcss/aspect-ratio"),
        require("tailwindcss-rtl"),
        require("preline/plugin"),
    ],
};
